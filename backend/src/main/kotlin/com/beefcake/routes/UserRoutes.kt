package com.beefcake.routes

import com.beefcake.database.tables.UserType
import com.beefcake.models.PasswordChangeRequest
import com.beefcake.models.PasswordResetRequest
import com.beefcake.models.UserUpdateRequest
import com.beefcake.services.UserService
import com.beefcake.utils.ResponseUtils.respondError
import com.beefcake.utils.ResponseUtils.respondForbidden
import com.beefcake.utils.ResponseUtils.respondNotFound
import com.beefcake.utils.ResponseUtils.respondSuccess
import com.beefcake.utils.ResponseUtils.respondUnauthorized
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory

fun Route.userRoutes() {
    val userService = UserService()
    val logger = LoggerFactory.getLogger("UserRoutes")
    
    route("/users") {
        authenticate("auth-jwt") {
            get("/me") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@get
                    }
                    
                    val user = userService.getUserById(userId)
                    if (user != null) {
                        call.respondSuccess(user, "获取用户信息成功")
                    } else {
                        call.respondNotFound("用户不存在")
                    }
                } catch (e: Exception) {
                    logger.error("获取用户信息异常", e)
                    call.respondError("获取用户信息失败")
                }
            }
            
            put("/me") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@put
                    }
                    
                    val request = call.receive<UserUpdateRequest>()
                    val result = userService.updateProfile(userId, request)
                    
                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "更新用户信息成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "更新用户信息失败")
                    }
                } catch (e: Exception) {
                    logger.error("更新用户信息异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            post("/change-password") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@post
                    }
                    
                    val request = call.receive<PasswordChangeRequest>()
                    val result = userService.changePassword(userId, request)
                    
                    if (result.isSuccess) {
                        call.respondSuccess("密码修改成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "密码修改失败")
                    }
                } catch (e: Exception) {
                    logger.error("修改密码异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            // 管理员功能
            get("/list") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userType = principal?.getClaim("userType", String::class)
                    
                    if (userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@get
                    }
                    
                    val page = call.request.queryParameters["page"]?.toIntOrNull() ?: 1
                    val pageSize = call.request.queryParameters["pageSize"]?.toIntOrNull() ?: 20
                    
                    val result = userService.getUserList(page, pageSize)
                    call.respondSuccess(result, "获取用户列表成功")
                } catch (e: Exception) {
                    logger.error("获取用户列表异常", e)
                    call.respondError("获取用户列表失败")
                }
            }
            
            post("/reset-password") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val adminUserId = principal?.getClaim("userId", Long::class)
                    val userType = principal?.getClaim("userType", String::class)
                    
                    if (adminUserId == null || userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@post
                    }
                    
                    val request = call.receive<PasswordResetRequest>()
                    val result = userService.resetPassword(adminUserId, request.userId)
                    
                    if (result.isSuccess) {
                        call.respondSuccess("密码重置成功，新密码为：1234")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "密码重置失败")
                    }
                } catch (e: Exception) {
                    logger.error("重置密码异常", e)
                    call.respondError("请求格式错误")
                }
            }
        }
    }
}
