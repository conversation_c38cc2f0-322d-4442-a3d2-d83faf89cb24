package com.beefcake.repositories

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.*
import com.beefcake.models.Task
import com.beefcake.models.TaskQueryParams
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class TaskRepository {
    
    suspend fun create(
        title: String,
        description: String?,
        requirementDescription: String?,
        priorityImportance: Priority,
        priorityUrgency: Priority,
        estimatedHours: Double?,
        creatorId: Long,
        assigneeId: Long?,
        parentTaskId: Long?,
        dueDate: LocalDateTime?
    ): Task? = dbQuery {
        val insertStatement = Tasks.insert {
            it[Tasks.title] = title
            it[Tasks.description] = description
            it[Tasks.requirementDescription] = requirementDescription
            it[Tasks.priorityImportance] = priorityImportance
            it[Tasks.priorityUrgency] = priorityUrgency
            it[Tasks.estimatedHours] = estimatedHours?.toBigDecimal()
            it[Tasks.creatorId] = creatorId
            it[Tasks.assigneeId] = assigneeId
            it[Tasks.parentTaskId] = parentTaskId
            it[Tasks.dueDate] = dueDate
            it[Tasks.createdAt] = LocalDateTime.now()
            it[Tasks.updatedAt] = LocalDateTime.now()
        }
        
        insertStatement.resultedValues?.singleOrNull()?.let { resultRowToTask(it) }
    }
    
    suspend fun findById(id: Long): Task? = dbQuery {
        Tasks.selectAll().where { Tasks.id eq id }
            .map { resultRowToTask(it) }
            .singleOrNull()
    }
    
    suspend fun update(
        id: Long,
        title: String?,
        description: String?,
        requirementDescription: String?,
        status: TaskStatus?,
        priorityImportance: Priority?,
        priorityUrgency: Priority?,
        estimatedHours: Double?,
        actualHours: Double?,
        assigneeId: Long?,
        dueDate: LocalDateTime?
    ): Boolean = dbQuery {
        Tasks.update({ Tasks.id eq id }) {
            if (title != null) it[Tasks.title] = title
            if (description != null) it[Tasks.description] = description
            if (requirementDescription != null) it[Tasks.requirementDescription] = requirementDescription
            if (status != null) it[Tasks.status] = status
            if (priorityImportance != null) it[Tasks.priorityImportance] = priorityImportance
            if (priorityUrgency != null) it[Tasks.priorityUrgency] = priorityUrgency
            if (estimatedHours != null) it[Tasks.estimatedHours] = estimatedHours.toBigDecimal()
            if (actualHours != null) it[Tasks.actualHours] = actualHours.toBigDecimal()
            if (assigneeId != null) it[Tasks.assigneeId] = assigneeId
            if (dueDate != null) it[Tasks.dueDate] = dueDate
            it[Tasks.updatedAt] = LocalDateTime.now()
        } > 0
    }
    
    suspend fun updateStatus(id: Long, status: TaskStatus): Boolean = dbQuery {
        val now = LocalDateTime.now()

        Tasks.update({ Tasks.id eq id }) {
            it[Tasks.status] = status
            it[Tasks.updatedAt] = now

            // 根据状态更新时间戳
            when (status) {
                TaskStatus.IN_DEVELOPMENT -> {
                    it[Tasks.startedAt] = now
                }
                TaskStatus.RELEASED -> {
                    it[Tasks.completedAt] = now
                }
                else -> {}
            }
        } > 0
    }
    
    suspend fun delete(id: Long): Boolean = dbQuery {
        Tasks.deleteWhere { Tasks.id eq id } > 0
    }
    
    suspend fun findAll(params: TaskQueryParams): Pair<List<Task>, Int> = dbQuery {
        var query = Tasks.selectAll()
        
        // 添加过滤条件
        if (params.status != null) {
            query = query.andWhere { Tasks.status eq params.status }
        }
        if (params.assigneeId != null) {
            query = query.andWhere { Tasks.assigneeId eq params.assigneeId }
        }
        if (params.creatorId != null) {
            query = query.andWhere { Tasks.creatorId eq params.creatorId }
        }
        if (params.priorityImportance != null) {
            query = query.andWhere { Tasks.priorityImportance eq params.priorityImportance }
        }
        if (params.priorityUrgency != null) {
            query = query.andWhere { Tasks.priorityUrgency eq params.priorityUrgency }
        }
        if (params.parentTaskId != null) {
            query = query.andWhere { Tasks.parentTaskId eq params.parentTaskId }
        }
        if (!params.search.isNullOrBlank()) {
            query = query.andWhere { 
                (Tasks.title like "%${params.search}%") or 
                (Tasks.description like "%${params.search}%") or
                (Tasks.requirementDescription like "%${params.search}%")
            }
        }
        
        // 计算总数
        val total = query.count().toInt()
        
        // 添加排序
        val sortColumn = when (params.sortBy) {
            "title" -> Tasks.title
            "status" -> Tasks.status
            "priority" -> Tasks.priorityImportance
            "dueDate" -> Tasks.dueDate
            "updatedAt" -> Tasks.updatedAt
            else -> Tasks.createdAt
        }
        
        query = if (params.sortOrder.lowercase() == "asc") {
            query.orderBy(sortColumn, SortOrder.ASC)
        } else {
            query.orderBy(sortColumn, SortOrder.DESC)
        }
        
        // 添加分页
        val tasks = query
            .limit(params.pageSize, offset = ((params.page - 1) * params.pageSize).toLong())
            .map { resultRowToTask(it) }
        
        tasks to total
    }
    
    suspend fun findByStatus(status: TaskStatus): List<Task> = dbQuery {
        Tasks.selectAll().where { Tasks.status eq status }
            .orderBy(Tasks.createdAt, SortOrder.DESC)
            .map { resultRowToTask(it) }
    }
    
    suspend fun findOverdueTasks(): List<Task> = dbQuery {
        val now = LocalDateTime.now()
        Tasks.selectAll().where {
            (Tasks.dueDate less now) and
            (Tasks.status neq TaskStatus.RELEASED)
        }
        .orderBy(Tasks.dueDate, SortOrder.ASC)
        .map { resultRowToTask(it) }
    }
    
    suspend fun getTaskStatistics(): Map<String, Any> = dbQuery {
        val total = Tasks.selectAll().count().toInt()
        val completed = Tasks.selectAll().where { Tasks.status eq TaskStatus.RELEASED }.count().toInt()
        val inProgress = Tasks.selectAll().where {
            Tasks.status inList listOf(
                TaskStatus.IN_DEVELOPMENT,
                TaskStatus.TESTING,
                TaskStatus.PENDING_RELEASE
            )
        }.count().toInt()

        val overdue = Tasks.selectAll().where {
            (Tasks.dueDate less LocalDateTime.now()) and
            (Tasks.status neq TaskStatus.RELEASED)
        }.count().toInt()
        
        mapOf(
            "totalTasks" to total,
            "completedTasks" to completed,
            "inProgressTasks" to inProgress,
            "overdueTasks" to overdue
        )
    }
    
    private fun resultRowToTask(row: ResultRow): Task {
        val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
        return Task(
            id = row[Tasks.id].value,
            title = row[Tasks.title],
            description = row[Tasks.description],
            requirementDescription = row[Tasks.requirementDescription],
            status = row[Tasks.status],
            priorityImportance = row[Tasks.priorityImportance],
            priorityUrgency = row[Tasks.priorityUrgency],
            estimatedHours = row[Tasks.estimatedHours]?.toDouble(),
            actualHours = row[Tasks.actualHours]?.toDouble(),
            creatorId = row[Tasks.creatorId],
            assigneeId = row[Tasks.assigneeId],
            parentTaskId = row[Tasks.parentTaskId],
            dueDate = row[Tasks.dueDate]?.format(formatter),
            startedAt = row[Tasks.startedAt]?.format(formatter),
            completedAt = row[Tasks.completedAt]?.format(formatter),
            createdAt = row[Tasks.createdAt].format(formatter),
            updatedAt = row[Tasks.updatedAt].format(formatter),
            creator = null, // 需要时单独查询
            assignee = null, // 需要时单独查询
            parentTask = null, // 需要时单独查询
            subTasks = null // 需要时单独查询
        )
    }
}
