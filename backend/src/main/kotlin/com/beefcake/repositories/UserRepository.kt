package com.beefcake.repositories

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.UserStatus
import com.beefcake.database.tables.Users
import com.beefcake.models.User
import org.jetbrains.exposed.sql.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class UserRepository {

    suspend fun create(username: String, hashedPassword: String, nickname: String? = null): User? = dbQuery {
        val insertStatement = Users.insert {
            it[Users.username] = username
            it[Users.password] = hashedPassword
            it[Users.nickname] = nickname
            it[Users.createdAt] = LocalDateTime.now()
            it[Users.updatedAt] = LocalDateTime.now()
        }

        insertStatement.resultedValues?.singleOrNull()?.let(::resultRowToUser)
    }

    suspend fun findByUsername(username: String): User? = dbQuery {
        Users.select { Users.username eq username }
            .map(::resultRowToUser)
            .singleOrNull()
    }

    suspend fun findById(id: Long): User? = dbQuery {
        Users.select { Users.id eq id }
            .map(::resultRowToUser)
            .singleOrNull()
    }

    suspend fun findByIdWithPassword(id: Long): Pair<User, String>? = dbQuery {
        Users.select { Users.id eq id }
            .map { row ->
                resultRowToUser(row) to row[Users.password]
            }
            .singleOrNull()
    }

    suspend fun findByUsernameWithPassword(username: String): Pair<User, String>? = dbQuery {
        Users.select { Users.username eq username }
            .map { row ->
                resultRowToUser(row) to row[Users.password]
            }
            .singleOrNull()
    }

    suspend fun updateLoginFailure(userId: Long, failedCount: Int, lockedUntil: LocalDateTime? = null): Boolean =
        dbQuery {
            Users.update({ Users.id eq userId }) {
                it[failedLoginCount] = failedCount
                it[Users.lockedUntil] = lockedUntil
                it[updatedAt] = LocalDateTime.now()
            } > 0
        }

    suspend fun resetLoginFailure(userId: Long): Boolean = dbQuery {
        Users.update({ Users.id eq userId }) {
            it[failedLoginCount] = 0
            it[lockedUntil] = null
            it[updatedAt] = LocalDateTime.now()
        } > 0
    }

    suspend fun updateProfile(userId: Long, nickname: String?, avatarUrl: String?): Boolean = dbQuery {
        Users.update({ Users.id eq userId }) {
            if (nickname != null) it[Users.nickname] = nickname
            if (avatarUrl != null) it[Users.avatarUrl] = avatarUrl
            it[updatedAt] = LocalDateTime.now()
        } > 0
    }

    suspend fun updatePassword(userId: Long, hashedPassword: String): Boolean = dbQuery {
        Users.update({ Users.id eq userId }) {
            it[password] = hashedPassword
            it[updatedAt] = LocalDateTime.now()
        } > 0
    }

    suspend fun updateStatus(userId: Long, status: UserStatus): Boolean = dbQuery {
        Users.update({ Users.id eq userId }) {
            it[Users.status] = status
            it[updatedAt] = LocalDateTime.now()
        } > 0
    }

    suspend fun findAll(page: Int = 1, pageSize: Int = 20): Pair<List<User>, Int> = dbQuery {
        val total = Users.selectAll().count().toInt()
        val users = Users.selectAll()
            .limit(pageSize, offset = ((page - 1) * pageSize).toLong())
            .orderBy(Users.createdAt, SortOrder.DESC)
            .map(::resultRowToUser)

        users to total
    }

    private fun resultRowToUser(row: ResultRow): User {
        val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
        return User(
            id = row[Users.id].value,
            username = row[Users.username],
            nickname = row[Users.nickname],
            avatarUrl = row[Users.avatarUrl],
            userType = row[Users.userType],
            status = row[Users.status],
            failedLoginCount = row[Users.failedLoginCount],
            lockedUntil = row[Users.lockedUntil]?.format(formatter),
            createdAt = row[Users.createdAt].format(formatter),
            updatedAt = row[Users.updatedAt].format(formatter)
        )
    }
}
