package com.beefcake.plugins

import com.beefcake.routes.authRoutes
import com.beefcake.routes.userRoutes
import com.beefcake.routes.taskRoutes
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*

fun Application.configureRouting() {
    routing {
        get("/") {
            call.respondText("猛男项目管理系统 API 服务正在运行")
        }
        
        get("/health") {
            call.respondText("OK")
        }
        
        route("/api") {
            authRoutes()
            userRoutes()
            taskRoutes()
        }
    }
}
