package com.beefcake.database

import com.beefcake.database.tables.*
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import io.ktor.server.config.*
import kotlinx.coroutines.Dispatchers
import org.flywaydb.core.Flyway
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.LoggerFactory

object DatabaseFactory {
    private val logger = LoggerFactory.getLogger(DatabaseFactory::class.java)
    
    fun init(config: ApplicationConfig) {
        val databaseConfig = config.config("database")
        val database = Database.connect(createHikariDataSource(databaseConfig))
        
        // 运行数据库迁移
        runMigrations(databaseConfig)
        
        // 创建表（如果不存在）
        transaction(database) {
            SchemaUtils.create(
                Users,
                Tasks,
                TaskLogs,
                TaskComments,
                WeeklyPlans,
                WeeklyPlanTasks,
                TaskAttachments,
                Notifications
            )
        }
        
        // 初始化超级管理员
        initSuperAdmin()
        
        logger.info("数据库初始化完成")
    }
    
    private fun createHikariDataSource(config: ApplicationConfig): HikariDataSource {
        val hikariConfig = HikariConfig().apply {
            driverClassName = "com.mysql.cj.jdbc.Driver"
            jdbcUrl = "jdbc:mysql://${config.property("host").getString()}:${config.property("port").getString()}/${config.property("name").getString()}?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai"
            username = config.property("user").getString()
            password = config.property("password").getString()
            maximumPoolSize = config.propertyOrNull("maxPoolSize")?.getString()?.toInt() ?: 20
            isAutoCommit = false
            transactionIsolation = "TRANSACTION_REPEATABLE_READ"
            validate()
        }
        return HikariDataSource(hikariConfig)
    }
    
    private fun runMigrations(config: ApplicationConfig) {
        val flyway = Flyway.configure()
            .dataSource(
                "jdbc:mysql://${config.property("host").getString()}:${config.property("port").getString()}/${config.property("name").getString()}?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai",
                config.property("user").getString(),
                config.property("password").getString()
            )
            .locations("classpath:db/migration")
            .load()
        
        try {
            flyway.migrate()
            logger.info("数据库迁移完成")
        } catch (e: Exception) {
            logger.error("数据库迁移失败", e)
            throw e
        }
    }
    
    private fun initSuperAdmin() {
        transaction {
            // 这里会在后续实现用户服务时添加超级管理员初始化逻辑
        }
    }
    
    suspend fun <T> dbQuery(block: suspend () -> T): T =
        newSuspendedTransaction(Dispatchers.IO) { block() }
}
