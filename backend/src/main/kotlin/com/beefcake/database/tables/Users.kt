package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.datetime
import java.time.LocalDateTime

object Users : LongIdTable("users") {
    val username = varchar("username", 50).uniqueIndex()
    val password = varchar("password", 255)
    val nickname = varchar("nickname", 100).nullable()
    val avatarUrl = varchar("avatar_url", 500).nullable()
    val userType = enumeration("user_type", UserType::class).default(UserType.NORMAL)
    val failedLoginCount = integer("failed_login_count").default(0)
    val status = enumeration("status", UserStatus::class).default(UserStatus.ACTIVE)
    val lockedUntil = datetime("locked_until").nullable()
    val createdAt = datetime("created_at").default(LocalDateTime.now())
    val updatedAt = datetime("updated_at").default(LocalDateTime.now())
}

enum class UserType {
    NORMAL, SUPER_ADMIN
}

enum class UserStatus {
    ACTIVE, DISABLED, LOCKED
}
