package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.datetime
import java.time.LocalDateTime

object TaskLogs : LongIdTable("task_logs") {
    val taskId = long("task_id").references(Tasks.id)
    val userId = long("user_id").references(Users.id)
    val actionType = enumeration("action_type", ActionType::class)
    val oldValue = text("old_value").nullable()
    val newValue = text("new_value").nullable()
    val comment = text("comment").nullable()
    val createdAt = datetime("created_at").default(LocalDateTime.now())
}

enum class ActionType {
    CREATE,
    UPDATE_STATUS,
    UPDATE_ASSIGNEE,
    UPDATE_PRIORITY,
    ADD_COMMENT,
    UPLOAD_ATTACHMENT
}
