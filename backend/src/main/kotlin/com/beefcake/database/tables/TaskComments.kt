package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.datetime
import java.time.LocalDateTime

object TaskComments : LongIdTable("task_comments") {
    val taskId = long("task_id").references(Tasks.id)
    val userId = long("user_id").references(Users.id)
    val content = text("content")
    val commentType = enumeration("comment_type", CommentType::class).default(CommentType.COMMENT)
    val parentCommentId = long("parent_comment_id").references(TaskComments.id).nullable()
    val createdAt = datetime("created_at").default(LocalDateTime.now())
}

enum class CommentType {
    COMMENT, BUG_REPORT
}
