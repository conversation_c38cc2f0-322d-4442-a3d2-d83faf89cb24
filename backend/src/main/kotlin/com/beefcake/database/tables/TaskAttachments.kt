package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.*
import java.time.LocalDateTime

object TaskAttachments : LongIdTable("task_attachments") {
    val taskId = long("task_id").references(Tasks.id)
    val fileName = varchar("file_name", 255)
    val filePath = varchar("file_path", 500)
    val fileSize = long("file_size")
    val fileType = varchar("file_type", 100).nullable()
    val uploadedBy = long("uploaded_by").references(Users.id)
    val createdAt = datetime("created_at")
}

object Notifications : LongIdTable("notifications") {
    val userId = long("user_id").references(Users.id)
    val title = varchar("title", 200)
    val content = text("content").nullable()
    val notificationType = enumeration("notification_type", NotificationType::class)
    val relatedTaskId = long("related_task_id").references(Tasks.id).nullable()
    val isRead = bool("is_read").default(false)
    val createdAt = datetime("created_at")
}

enum class NotificationType {
    TASK_ASSIGNED,
    TASK_STATUS_CHANGED,
    TASK_OVERDUE,
    COMMENT_MENTIONED
}
