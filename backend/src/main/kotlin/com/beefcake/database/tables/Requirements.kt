package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.*
import java.time.LocalDateTime

object Requirements : LongIdTable("requirements") {
    val title = varchar("title", 200)
    val businessDescription = text("business_description") // 业务描述，面向老板
    val acceptanceCriteria = text("acceptance_criteria").nullable() // 验收标准
    val status = enumeration("status", RequirementStatus::class).default(RequirementStatus.DRAFT)
    val priorityImportance = enumeration("priority_importance", Priority::class).default(Priority.MEDIUM)
    val priorityUrgency = enumeration("priority_urgency", Priority::class).default(Priority.MEDIUM)
    val estimatedValue = varchar("estimated_value", 100).nullable() // 预估价值/收益
    val targetUsers = varchar("target_users", 200).nullable() // 目标用户群体
    val businessGoal = text("business_goal").nullable() // 业务目标
    val creatorId = long("creator_id").references(Users.id)
    val assigneeId = long("assignee_id").references(Users.id).nullable() // 需求负责人
    val expectedDeliveryDate = datetime("expected_delivery_date").nullable() // 期望交付时间
    val actualDeliveryDate = datetime("actual_delivery_date").nullable() // 实际交付时间
    val createdAt = datetime("created_at")
    val updatedAt = datetime("updated_at")
}

enum class RequirementStatus {
    DRAFT,          // 草稿
    REVIEW,         // 评审中
    APPROVED,       // 已批准
    IN_PROGRESS,    // 进行中
    TESTING,        // 测试中
    DELIVERED,      // 已交付
    REJECTED        // 已拒绝
}

// 需求附件表
object RequirementAttachments : LongIdTable("requirement_attachments") {
    val requirementId = long("requirement_id").references(Requirements.id)
    val fileName = varchar("file_name", 255)
    val filePath = varchar("file_path", 500)
    val fileSize = long("file_size")
    val fileType = varchar("file_type", 100).nullable()
    val description = varchar("description", 500).nullable() // 附件说明
    val uploadedBy = long("uploaded_by").references(Users.id)
    val createdAt = datetime("created_at")
}
