package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.*
import java.time.LocalDateTime

object Tasks : LongIdTable("tasks") {
    val title = varchar("title", 200)
    val description = text("description").nullable()
    val requirementDescription = text("requirement_description").nullable()
    val status = enumerationByName("status", 20, TaskStatus::class).default(TaskStatus.REQUIREMENT_CREATED)
    val priorityImportance = enumerationByName("priority_importance", 20, Priority::class).default(Priority.MEDIUM)
    val priorityUrgency = enumerationByName("priority_urgency", 20, Priority::class).default(Priority.MEDIUM)
    val estimatedHours = decimal("estimated_hours", 5, 2).nullable()
    val actualHours = decimal("actual_hours", 5, 2).nullable()
    val creatorId = long("creator_id").references(Users.id)
    val assigneeId = long("assignee_id").references(Users.id).nullable()
    val parentTaskId = long("parent_task_id").references(Tasks.id).nullable()
    val dueDate = datetime("due_date").nullable()
    val startedAt = datetime("started_at").nullable()
    val completedAt = datetime("completed_at").nullable()
    val createdAt = datetime("created_at")
    val updatedAt = datetime("updated_at")
}

enum class TaskStatus {
    REQUIREMENT_CREATED,    // 需求创建
    TASK_BREAKDOWN,         // 分解任务
    IN_DEVELOPMENT,         // 开发中
    PAUSED,                 // 暂停
    TESTING,                // 测试
    PENDING_RELEASE,        // 待上线
    RELEASED                // 上线
}

enum class Priority {
    HIGH, MEDIUM, LOW
}
