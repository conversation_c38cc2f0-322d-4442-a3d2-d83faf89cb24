package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.datetime
import java.time.LocalDateTime

object Tasks : LongIdTable("tasks") {
    val title = varchar("title", 200)
    val description = text("description").nullable()
    val requirementDescription = text("requirement_description").nullable()
    val status = enumeration("status", TaskStatus::class).default(TaskStatus.REQUIREMENT_CREATED)
    val priorityImportance = enumeration("priority_importance", Priority::class).default(Priority.MEDIUM)
    val priorityUrgency = enumeration("priority_urgency", Priority::class).default(Priority.MEDIUM)
    val estimatedHours = decimal("estimated_hours", 5, 2).nullable()
    val actualHours = decimal("actual_hours", 5, 2).nullable()
    val creatorId = long("creator_id").references(Users.id)
    val assigneeId = long("assignee_id").references(Users.id).nullable()
    val parentTaskId = long("parent_task_id").references(Tasks.id).nullable()
    val dueDate = datetime("due_date").nullable()
    val startedAt = datetime("started_at").nullable()
    val completedAt = datetime("completed_at").nullable()
    val createdAt = datetime("created_at").default(LocalDateTime.now())
    val updatedAt = datetime("updated_at").default(LocalDateTime.now())
}

enum class TaskStatus {
    REQUIREMENT_CREATED,    // 需求创建
    TASK_BREAKDOWN,         // 分解任务
    IN_DEVELOPMENT,         // 开发中
    PAUSED,                 // 暂停
    TESTING,                // 测试
    PENDING_RELEASE,        // 待上线
    RELEASED                // 上线
}

enum class Priority {
    HIGH, MEDIUM, LOW
}
