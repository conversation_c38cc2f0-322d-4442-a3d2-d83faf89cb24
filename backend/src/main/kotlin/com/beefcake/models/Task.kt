package com.beefcake.models

import com.beefcake.database.tables.Priority
import com.beefcake.database.tables.TaskStatus
import kotlinx.serialization.Serializable

@Serializable
data class Task(
    val id: Long,
    val title: String,
    val description: String?,
    val requirementDescription: String?,
    val status: TaskStatus,
    val priorityImportance: Priority,
    val priorityUrgency: Priority,
    val estimatedHours: Double?,
    val actualHours: Double?,
    val creatorId: Long,
    val assigneeId: Long?,
    val parentTaskId: Long?,
    val dueDate: String?,
    val startedAt: String?,
    val completedAt: String?,
    val createdAt: String,
    val updatedAt: String,
    val creator: User?,
    val assignee: User?,
    val parentTask: Task?,
    val subTasks: List<Task>?
)

@Serializable
data class TaskCreateRequest(
    val title: String,
    val description: String? = null,
    val requirementDescription: String? = null,
    val priorityImportance: Priority = Priority.MEDIUM,
    val priorityUrgency: Priority = Priority.MEDIUM,
    val estimatedHours: Double? = null,
    val assigneeId: Long? = null,
    val parentTaskId: Long? = null,
    val dueDate: String? = null
)

@Serializable
data class TaskUpdateRequest(
    val title: String? = null,
    val description: String? = null,
    val requirementDescription: String? = null,
    val status: TaskStatus? = null,
    val priorityImportance: Priority? = null,
    val priorityUrgency: Priority? = null,
    val estimatedHours: Double? = null,
    val actualHours: Double? = null,
    val assigneeId: Long? = null,
    val dueDate: String? = null
)

@Serializable
data class TaskListResponse(
    val tasks: List<Task>,
    val total: Int,
    val page: Int,
    val pageSize: Int
)

@Serializable
data class TaskStatusUpdateRequest(
    val status: TaskStatus,
    val comment: String? = null
)

@Serializable
data class TaskAssignRequest(
    val assigneeId: Long,
    val comment: String? = null
)

@Serializable
data class TaskQueryParams(
    val status: TaskStatus? = null,
    val assigneeId: Long? = null,
    val creatorId: Long? = null,
    val priorityImportance: Priority? = null,
    val priorityUrgency: Priority? = null,
    val parentTaskId: Long? = null,
    val search: String? = null,
    val page: Int = 1,
    val pageSize: Int = 20,
    val sortBy: String = "createdAt",
    val sortOrder: String = "desc"
)

// 四象限数据
@Serializable
data class QuadrantData(
    val urgentImportant: List<Task>,      // 紧急且重要
    val notUrgentImportant: List<Task>,   // 不紧急但重要
    val urgentNotImportant: List<Task>,   // 紧急但不重要
    val notUrgentNotImportant: List<Task> // 不紧急不重要
)

// 看板数据
@Serializable
data class KanbanData(
    val requirementCreated: List<Task>,
    val taskBreakdown: List<Task>,
    val inDevelopment: List<Task>,
    val paused: List<Task>,
    val testing: List<Task>,
    val pendingRelease: List<Task>,
    val released: List<Task>
)

// 任务统计
@Serializable
data class TaskStatistics(
    val totalTasks: Int,
    val completedTasks: Int,
    val inProgressTasks: Int,
    val overdueTasks: Int,
    val tasksByStatus: Map<TaskStatus, Int>,
    val tasksByPriority: Map<String, Int>, // "HIGH_HIGH", "HIGH_MEDIUM", etc.
    val averageCompletionTime: Double?, // 平均完成时间（小时）
    val totalEstimatedHours: Double,
    val totalActualHours: Double
)
