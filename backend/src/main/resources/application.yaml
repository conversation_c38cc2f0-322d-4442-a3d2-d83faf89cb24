ktor:
  application:
    modules:
      - com.beefcake.ApplicationKt.module
  deployment:
    port: 8080
    host: 0.0.0.0

database:
  host: ${DB_HOST:localhost}
  port: ${DB_PORT:3306}
  name: ${DB_NAME:beefcake}
  user: ${DB_USER:root}
  password: ${DB_PASSWORD:root123}
  driver: com.mysql.cj.jdbc.Driver
  maxPoolSize: 20

jwt:
  secret: ${JWT_SECRET:your-super-secret-jwt-key-change-in-production}
  issuer: beefcake-system
  audience: beefcake-users
  realm: beefcake
  expirationTime: 86400000 # 24 hours in milliseconds

app:
  environment: ${APP_ENV:development}
  uploadDir: ${UPLOAD_DIR:./uploads}
  maxFileSize: 10485760 # 10MB
  allowedFileTypes: ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx"]

security:
  maxLoginAttempts: 5
  lockoutDurationMinutes: 30
  passwordMinLength: 8
