-- 创建用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(100) COMMENT '昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    user_type ENUM('NORMAL', 'SUPER_ADMIN') DEFAULT 'NORMAL' COMMENT '用户类型',
    status ENUM('ACTIVE', 'DISABLED', 'LOCKED') DEFAULT 'ACTIVE' COMMENT '用户状态',
    failed_login_count INT DEFAULT 0 COMMENT '登录失败次数',
    locked_until DATETIME COMMENT '锁定到期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建任务表
CREATE TABLE tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    requirement_description TEXT COMMENT '需求描述（面向非技术人员）',
    status ENUM('REQUIREMENT_CREATED', 'TASK_BREAKDOWN', 'IN_DEVELOPMENT', 'PAUSED', 'TESTING', 'PENDING_RELEASE', 'RELEASED') DEFAULT 'REQUIREMENT_CREATED' COMMENT '任务状态',
    priority_importance ENUM('HIGH', 'MEDIUM', 'LOW') DEFAULT 'MEDIUM' COMMENT '重要程度',
    priority_urgency ENUM('HIGH', 'MEDIUM', 'LOW') DEFAULT 'MEDIUM' COMMENT '紧急程度',
    estimated_hours DECIMAL(5,2) COMMENT '预估工时',
    actual_hours DECIMAL(5,2) COMMENT '实际工时',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    assignee_id BIGINT COMMENT '负责人ID',
    parent_task_id BIGINT COMMENT '父任务ID',
    due_date DATETIME COMMENT '截止时间',
    started_at DATETIME COMMENT '开始时间',
    completed_at DATETIME COMMENT '完成时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users(id),
    FOREIGN KEY (assignee_id) REFERENCES users(id),
    FOREIGN KEY (parent_task_id) REFERENCES tasks(id)
);

-- 创建任务操作日志表
CREATE TABLE task_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    action_type ENUM('CREATE', 'UPDATE_STATUS', 'UPDATE_ASSIGNEE', 'UPDATE_PRIORITY', 'ADD_COMMENT', 'UPLOAD_ATTACHMENT') NOT NULL,
    old_value TEXT COMMENT '变更前的值',
    new_value TEXT COMMENT '变更后的值',
    comment TEXT COMMENT '操作备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建任务评论表
CREATE TABLE task_comments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    comment_type ENUM('COMMENT', 'BUG_REPORT') DEFAULT 'COMMENT',
    parent_comment_id BIGINT COMMENT '回复的评论ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (parent_comment_id) REFERENCES task_comments(id)
);

-- 创建周计划表
CREATE TABLE weekly_plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    week_start_date DATE NOT NULL COMMENT '周开始日期',
    week_end_date DATE NOT NULL COMMENT '周结束日期',
    plan_name VARCHAR(200) COMMENT '计划名称',
    status ENUM('PLANNING', 'IN_PROGRESS', 'COMPLETED') DEFAULT 'PLANNING',
    summary TEXT COMMENT '周总结',
    created_by BIGINT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 创建周计划任务关联表
CREATE TABLE weekly_plan_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    weekly_plan_id BIGINT NOT NULL,
    task_id BIGINT NOT NULL,
    planned_hours DECIMAL(5,2) COMMENT '计划工时',
    is_emergency_insertion BOOLEAN DEFAULT FALSE COMMENT '是否紧急插入',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (weekly_plan_id) REFERENCES weekly_plans(id),
    FOREIGN KEY (task_id) REFERENCES tasks(id)
);

-- 创建任务附件表
CREATE TABLE task_attachments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100),
    uploaded_by BIGINT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id),
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);

-- 创建通知表
CREATE TABLE notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    notification_type ENUM('TASK_ASSIGNED', 'TASK_STATUS_CHANGED', 'TASK_OVERDUE', 'COMMENT_MENTIONED') NOT NULL,
    related_task_id BIGINT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (related_task_id) REFERENCES tasks(id)
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_creator ON tasks(creator_id);
CREATE INDEX idx_tasks_assignee ON tasks(assignee_id);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_task_logs_task_id ON task_logs(task_id);
CREATE INDEX idx_task_logs_created_at ON task_logs(created_at);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
