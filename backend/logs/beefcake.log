2025-07-11 14:37:09.985 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 14:37:10.091 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 14:37:11.181 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
java.sql.SQLException: Access denied for user 'root'@'localhost' (using password: YES)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:359)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:201)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:470)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:100)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81)
	at com.beefcake.database.DatabaseFactory.createHikariDataSource(DatabaseFactory.kt:56)
	at com.beefcake.database.DatabaseFactory.init(DatabaseFactory.kt:20)
	at com.beefcake.ApplicationKt.module(Application.kt:19)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at kotlin.reflect.jvm.internal.calls.CallerImpl$Method.callMethod(CallerImpl.kt:97)
	at kotlin.reflect.jvm.internal.calls.CallerImpl$Method$Static.call(CallerImpl.kt:106)
	at kotlin.reflect.jvm.internal.KCallableImpl.callDefaultMethod$kotlin_reflection(KCallableImpl.kt:200)
	at kotlin.reflect.jvm.internal.KCallableImpl.callBy(KCallableImpl.kt:112)
	at io.ktor.server.engine.internal.CallableUtilsKt.callFunctionWithInjection(CallableUtils.kt:119)
	at io.ktor.server.engine.internal.CallableUtilsKt.executeModuleFunction(CallableUtils.kt:36)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$launchModuleByName$1.invoke(ApplicationEngineEnvironmentReloading.kt:332)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$launchModuleByName$1.invoke(ApplicationEngineEnvironmentReloading.kt:331)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.avoidingDoubleStartupFor(ApplicationEngineEnvironmentReloading.kt:356)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.launchModuleByName(ApplicationEngineEnvironmentReloading.kt:331)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.access$launchModuleByName(ApplicationEngineEnvironmentReloading.kt:32)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:312)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:310)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.avoidingDoubleStartup(ApplicationEngineEnvironmentReloading.kt:338)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.instantiateAndConfigureApplication(ApplicationEngineEnvironmentReloading.kt:310)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.createApplication(ApplicationEngineEnvironmentReloading.kt:150)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.start(ApplicationEngineEnvironmentReloading.kt:277)
	at io.ktor.server.netty.NettyApplicationEngine.start(NettyApplicationEngine.kt:216)
	at io.ktor.server.netty.EngineMain.main(EngineMain.kt:23)
	at com.beefcake.ApplicationKt.main(Application.kt:11)
2025-07-11 14:37:40.481 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 14:37:40.591 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 14:37:41.671 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
java.sql.SQLException: Access denied for user 'root'@'localhost' (using password: YES)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:359)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:201)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:470)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:100)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81)
	at com.beefcake.database.DatabaseFactory.createHikariDataSource(DatabaseFactory.kt:56)
	at com.beefcake.database.DatabaseFactory.init(DatabaseFactory.kt:20)
	at com.beefcake.ApplicationKt.module(Application.kt:19)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at kotlin.reflect.jvm.internal.calls.CallerImpl$Method.callMethod(CallerImpl.kt:97)
	at kotlin.reflect.jvm.internal.calls.CallerImpl$Method$Static.call(CallerImpl.kt:106)
	at kotlin.reflect.jvm.internal.KCallableImpl.callDefaultMethod$kotlin_reflection(KCallableImpl.kt:200)
	at kotlin.reflect.jvm.internal.KCallableImpl.callBy(KCallableImpl.kt:112)
	at io.ktor.server.engine.internal.CallableUtilsKt.callFunctionWithInjection(CallableUtils.kt:119)
	at io.ktor.server.engine.internal.CallableUtilsKt.executeModuleFunction(CallableUtils.kt:36)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$launchModuleByName$1.invoke(ApplicationEngineEnvironmentReloading.kt:332)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$launchModuleByName$1.invoke(ApplicationEngineEnvironmentReloading.kt:331)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.avoidingDoubleStartupFor(ApplicationEngineEnvironmentReloading.kt:356)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.launchModuleByName(ApplicationEngineEnvironmentReloading.kt:331)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.access$launchModuleByName(ApplicationEngineEnvironmentReloading.kt:32)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:312)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:310)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.avoidingDoubleStartup(ApplicationEngineEnvironmentReloading.kt:338)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.instantiateAndConfigureApplication(ApplicationEngineEnvironmentReloading.kt:310)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.createApplication(ApplicationEngineEnvironmentReloading.kt:150)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.start(ApplicationEngineEnvironmentReloading.kt:277)
	at io.ktor.server.netty.NettyApplicationEngine.start(NettyApplicationEngine.kt:216)
	at io.ktor.server.netty.EngineMain.main(EngineMain.kt:23)
	at com.beefcake.ApplicationKt.main(Application.kt:11)
2025-07-11 14:39:21.585 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 14:39:21.708 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 14:39:22.767 [main] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
java.sql.SQLSyntaxErrorException: Unknown database 'beefcake'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:359)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:201)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:470)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:100)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:81)
	at com.beefcake.database.DatabaseFactory.createHikariDataSource(DatabaseFactory.kt:56)
	at com.beefcake.database.DatabaseFactory.init(DatabaseFactory.kt:20)
	at com.beefcake.ApplicationKt.module(Application.kt:19)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at kotlin.reflect.jvm.internal.calls.CallerImpl$Method.callMethod(CallerImpl.kt:97)
	at kotlin.reflect.jvm.internal.calls.CallerImpl$Method$Static.call(CallerImpl.kt:106)
	at kotlin.reflect.jvm.internal.KCallableImpl.callDefaultMethod$kotlin_reflection(KCallableImpl.kt:200)
	at kotlin.reflect.jvm.internal.KCallableImpl.callBy(KCallableImpl.kt:112)
	at io.ktor.server.engine.internal.CallableUtilsKt.callFunctionWithInjection(CallableUtils.kt:119)
	at io.ktor.server.engine.internal.CallableUtilsKt.executeModuleFunction(CallableUtils.kt:36)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$launchModuleByName$1.invoke(ApplicationEngineEnvironmentReloading.kt:332)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$launchModuleByName$1.invoke(ApplicationEngineEnvironmentReloading.kt:331)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.avoidingDoubleStartupFor(ApplicationEngineEnvironmentReloading.kt:356)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.launchModuleByName(ApplicationEngineEnvironmentReloading.kt:331)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.access$launchModuleByName(ApplicationEngineEnvironmentReloading.kt:32)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:312)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:310)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.avoidingDoubleStartup(ApplicationEngineEnvironmentReloading.kt:338)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.instantiateAndConfigureApplication(ApplicationEngineEnvironmentReloading.kt:310)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.createApplication(ApplicationEngineEnvironmentReloading.kt:150)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.start(ApplicationEngineEnvironmentReloading.kt:277)
	at io.ktor.server.netty.NettyApplicationEngine.start(NettyApplicationEngine.kt:216)
	at io.ktor.server.netty.EngineMain.main(EngineMain.kt:23)
	at com.beefcake.ApplicationKt.main(Application.kt:11)
2025-07-11 14:48:49.755 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 14:48:49.852 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 14:48:49.912 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5328a9c1
2025-07-11 14:48:49.912 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 14:48:49.932 [main] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 9.22.3 by Redgate
2025-07-11 14:48:49.932 [main] INFO  o.f.c.i.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-11 14:48:49.932 [main] INFO  o.f.c.i.license.VersionPrinter - 
2025-07-11 14:48:49.945 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 14:48:49.948 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.0.
2025-07-11 14:48:49.962 [main] INFO  o.f.c.i.s.JdbcTableSchemaHistory - Schema history table `beefcake`.`flyway_schema_history` does not exist yet
2025-07-11 14:48:49.964 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 2 migrations (execution time 00:00.009s)
2025-07-11 14:48:49.983 [main] ERROR c.beefcake.database.DatabaseFactory - 数据库迁移失败
org.flywaydb.core.api.FlywayException: Found non-empty schema(s) `beefcake` but no schema history table. Use baseline() or set baselineOnMigrate to true to initialize the schema history table.
	at org.flywaydb.core.Flyway.lambda$migrate$0(Flyway.java:178)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:213)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:140)
	at com.beefcake.database.DatabaseFactory.runMigrations(DatabaseFactory.kt:70)
	at com.beefcake.database.DatabaseFactory.init(DatabaseFactory.kt:23)
	at com.beefcake.ApplicationKt.module(Application.kt:19)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at kotlin.reflect.jvm.internal.calls.CallerImpl$Method.callMethod(CallerImpl.kt:97)
	at kotlin.reflect.jvm.internal.calls.CallerImpl$Method$Static.call(CallerImpl.kt:106)
	at kotlin.reflect.jvm.internal.KCallableImpl.callDefaultMethod$kotlin_reflection(KCallableImpl.kt:200)
	at kotlin.reflect.jvm.internal.KCallableImpl.callBy(KCallableImpl.kt:112)
	at io.ktor.server.engine.internal.CallableUtilsKt.callFunctionWithInjection(CallableUtils.kt:119)
	at io.ktor.server.engine.internal.CallableUtilsKt.executeModuleFunction(CallableUtils.kt:36)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$launchModuleByName$1.invoke(ApplicationEngineEnvironmentReloading.kt:332)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$launchModuleByName$1.invoke(ApplicationEngineEnvironmentReloading.kt:331)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.avoidingDoubleStartupFor(ApplicationEngineEnvironmentReloading.kt:356)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.launchModuleByName(ApplicationEngineEnvironmentReloading.kt:331)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.access$launchModuleByName(ApplicationEngineEnvironmentReloading.kt:32)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:312)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:310)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.avoidingDoubleStartup(ApplicationEngineEnvironmentReloading.kt:338)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.instantiateAndConfigureApplication(ApplicationEngineEnvironmentReloading.kt:310)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.createApplication(ApplicationEngineEnvironmentReloading.kt:150)
	at io.ktor.server.engine.ApplicationEngineEnvironmentReloading.start(ApplicationEngineEnvironmentReloading.kt:277)
	at io.ktor.server.netty.NettyApplicationEngine.start(NettyApplicationEngine.kt:216)
	at io.ktor.server.netty.EngineMain.main(EngineMain.kt:23)
	at com.beefcake.ApplicationKt.main(Application.kt:11)
2025-07-11 14:50:34.948 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 14:50:35.046 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 14:50:35.108 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5328a9c1
2025-07-11 14:50:35.108 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 14:50:35.127 [main] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 9.22.3 by Redgate
2025-07-11 14:50:35.127 [main] INFO  o.f.c.i.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-11 14:50:35.127 [main] INFO  o.f.c.i.license.VersionPrinter - 
2025-07-11 14:50:35.141 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 14:50:35.144 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.0.
2025-07-11 14:50:35.154 [main] INFO  o.f.c.i.s.JdbcTableSchemaHistory - Schema history table `beefcake`.`flyway_schema_history` does not exist yet
2025-07-11 14:50:35.155 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 2 migrations (execution time 00:00.007s)
2025-07-11 14:50:35.166 [main] INFO  o.f.c.i.s.JdbcTableSchemaHistory - Creating Schema History table `beefcake`.`flyway_schema_history` ...
2025-07-11 14:50:35.192 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: << Empty Schema >>
2025-07-11 14:50:35.202 [main] INFO  o.f.core.internal.command.DbMigrate - Migrating schema `beefcake` to version "1 - Create initial tables"
2025-07-11 14:50:35.307 [main] INFO  o.f.core.internal.command.DbMigrate - Migrating schema `beefcake` to version "2 - Insert super admin"
2025-07-11 14:50:35.314 [main] INFO  o.f.core.internal.command.DbMigrate - Successfully applied 2 migrations to schema `beefcake`, now at version v2 (execution time 00:00.099s)
2025-07-11 14:50:35.317 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-11 14:50:35.357 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-11 14:50:35.444 [main] INFO  Application - Application started in 0.505 seconds.
2025-07-11 14:50:35.444 [main] INFO  Application - Application started: io.ktor.server.application.Application@69923ac3
2025-07-11 14:50:35.497 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-11 14:57:25.469 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #0 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 14:57:25.471 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 14:57:25.472 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #1 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 14:57:25.472 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 14:57:25.473 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #2 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 14:57:25.474 [eventLoopGroupProxy-4-3] ERROR AuthRoutes - 用户注册异常
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 14:57:25.477 [eventLoopGroupProxy-4-3] INFO  Application - 400 Bad Request: POST /api/auth/register - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-11 15:10:15.260 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@69923ac3
2025-07-11 15:10:15.264 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@69923ac3
2025-07-11 15:10:17.283 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 15:10:17.382 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:10:17.440 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5328a9c1
2025-07-11 15:10:17.441 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:10:17.457 [main] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 9.22.3 by Redgate
2025-07-11 15:10:17.457 [main] INFO  o.f.c.i.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-11 15:10:17.457 [main] INFO  o.f.c.i.license.VersionPrinter - 
2025-07-11 15:10:17.474 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 15:10:17.478 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.0.
2025-07-11 15:10:17.502 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 2 migrations (execution time 00:00.015s)
2025-07-11 15:10:17.510 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 2
2025-07-11 15:10:17.512 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-11 15:10:17.516 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-11 15:10:17.571 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-11 15:10:17.672 [main] INFO  Application - Application started in 0.401 seconds.
2025-07-11 15:10:17.673 [main] INFO  Application - Application started: io.ktor.server.application.Application@4b0f2299
2025-07-11 15:10:17.776 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-11 15:10:26.011 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #0 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:10:26.012 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:10:26.014 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #1 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:10:26.014 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:10:26.015 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #2 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:10:26.017 [eventLoopGroupProxy-4-1] ERROR AuthRoutes - 用户注册异常
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:10:26.023 [eventLoopGroupProxy-4-1] INFO  Application - 400 Bad Request: POST /api/auth/register - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-11 15:12:29.694 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@4b0f2299
2025-07-11 15:12:29.697 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@4b0f2299
2025-07-11 15:12:31.206 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 15:12:31.332 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:12:31.392 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5328a9c1
2025-07-11 15:12:31.393 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:12:31.410 [main] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 9.22.3 by Redgate
2025-07-11 15:12:31.410 [main] INFO  o.f.c.i.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-11 15:12:31.410 [main] INFO  o.f.c.i.license.VersionPrinter - 
2025-07-11 15:12:31.425 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 15:12:31.429 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.0.
2025-07-11 15:12:31.448 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 2 migrations (execution time 00:00.011s)
2025-07-11 15:12:31.457 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 2
2025-07-11 15:12:31.458 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-11 15:12:31.463 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-11 15:12:31.501 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-11 15:12:31.574 [main] INFO  Application - Application started in 0.379 seconds.
2025-07-11 15:12:31.574 [main] INFO  Application - Application started: io.ktor.server.application.Application@4b0f2299
2025-07-11 15:12:31.650 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-11 15:12:39.133 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #0 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:12:39.134 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:12:39.135 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #1 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:12:39.135 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:12:39.137 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #2 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:12:39.137 [eventLoopGroupProxy-4-1] ERROR AuthRoutes - 用户注册异常
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:12:39.145 [eventLoopGroupProxy-4-1] INFO  Application - 400 Bad Request: POST /api/auth/register - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-11 15:23:39.530 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@4b0f2299
2025-07-11 15:23:39.533 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@4b0f2299
2025-07-11 15:24:22.844 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 15:24:22.956 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:24:23.020 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@28952dea
2025-07-11 15:24:23.021 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:24:23.051 [main] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 9.22.3 by Redgate
2025-07-11 15:24:23.051 [main] INFO  o.f.c.i.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-11 15:24:23.051 [main] INFO  o.f.c.i.license.VersionPrinter - 
2025-07-11 15:24:23.069 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 15:24:23.075 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.0.
2025-07-11 15:24:23.096 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 2 migrations (execution time 00:00.015s)
2025-07-11 15:24:23.104 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 2
2025-07-11 15:24:23.106 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-11 15:24:23.110 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-11 15:24:23.142 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-11 15:24:23.237 [main] INFO  Application - Application started in 0.403 seconds.
2025-07-11 15:24:23.237 [main] INFO  Application - Application started: io.ktor.server.application.Application@3c3c4a71
2025-07-11 15:24:23.315 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-11 15:25:08.279 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #0 failed: java.sql.SQLException: Data truncated for column 'user_status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, updated_at, user_status, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'user_status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:14)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'user_status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:25:08.281 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:25:08.282 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #1 failed: java.sql.SQLException: Data truncated for column 'user_status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, updated_at, user_status, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'user_status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:14)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'user_status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:25:08.282 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:25:08.284 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #2 failed: java.sql.SQLException: Data truncated for column 'user_status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, updated_at, user_status, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'user_status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:14)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'user_status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:25:08.285 [eventLoopGroupProxy-4-1] ERROR AuthRoutes - 用户注册异常
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'user_status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:14)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'user_status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:25:08.292 [eventLoopGroupProxy-4-1] INFO  Application - 400 Bad Request: POST /api/auth/register - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-11 15:25:57.593 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@3c3c4a71
2025-07-11 15:25:57.595 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@3c3c4a71
2025-07-11 15:25:59.523 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 15:25:59.633 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:25:59.693 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4cee7fa0
2025-07-11 15:25:59.694 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:25:59.713 [main] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 9.22.3 by Redgate
2025-07-11 15:25:59.714 [main] INFO  o.f.c.i.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-11 15:25:59.714 [main] INFO  o.f.c.i.license.VersionPrinter - 
2025-07-11 15:25:59.727 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 15:25:59.730 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.0.
2025-07-11 15:25:59.742 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 2 migrations (execution time 00:00.008s)
2025-07-11 15:25:59.749 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 2
2025-07-11 15:25:59.749 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-11 15:25:59.753 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-11 15:25:59.784 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-11 15:25:59.850 [main] INFO  Application - Application started in 0.34 seconds.
2025-07-11 15:25:59.850 [main] INFO  Application - Application started: io.ktor.server.application.Application@7b306b9f
2025-07-11 15:25:59.945 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-11 15:26:11.884 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #0 failed: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'. Statement(s): SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.username = ?
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.AbstractQuery.iterator(AbstractQuery.kt:61)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invokeSuspend(UserRepository.kt:124)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:972)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeQuery(JdbcPreparedStatementImpl.kt:46)
	at org.jetbrains.exposed.sql.Query.executeInternal(Query.kt:104)
	at org.jetbrains.exposed.sql.Query.executeInternal(Query.kt:19)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 18 common frames omitted
2025-07-11 15:26:11.885 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:26:11.886 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #1 failed: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'. Statement(s): SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.username = ?
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.AbstractQuery.iterator(AbstractQuery.kt:61)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invokeSuspend(UserRepository.kt:124)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:972)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeQuery(JdbcPreparedStatementImpl.kt:46)
	at org.jetbrains.exposed.sql.Query.executeInternal(Query.kt:104)
	at org.jetbrains.exposed.sql.Query.executeInternal(Query.kt:19)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 18 common frames omitted
2025-07-11 15:26:11.887 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:26:11.887 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #2 failed: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'. Statement(s): SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.username = ?
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.AbstractQuery.iterator(AbstractQuery.kt:61)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invokeSuspend(UserRepository.kt:124)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:972)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeQuery(JdbcPreparedStatementImpl.kt:46)
	at org.jetbrains.exposed.sql.Query.executeInternal(Query.kt:104)
	at org.jetbrains.exposed.sql.Query.executeInternal(Query.kt:19)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 18 common frames omitted
2025-07-11 15:26:11.888 [eventLoopGroupProxy-4-1] ERROR AuthRoutes - 用户注册异常
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.AbstractQuery.iterator(AbstractQuery.kt:61)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invokeSuspend(UserRepository.kt:124)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$findByUsername$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'users.status' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:972)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeQuery(JdbcPreparedStatementImpl.kt:46)
	at org.jetbrains.exposed.sql.Query.executeInternal(Query.kt:104)
	at org.jetbrains.exposed.sql.Query.executeInternal(Query.kt:19)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 18 common frames omitted
2025-07-11 15:26:11.896 [eventLoopGroupProxy-4-1] INFO  Application - 400 Bad Request: POST /api/auth/register - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-11 15:26:47.759 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #0 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:26:47.759 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:26:47.761 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #1 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:26:47.761 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:26:47.762 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #2 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:26:47.763 [eventLoopGroupProxy-4-2] ERROR AuthRoutes - 用户注册异常
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:26:47.763 [eventLoopGroupProxy-4-2] INFO  Application - 400 Bad Request: POST /api/auth/register - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-11 15:29:56.516 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@7b306b9f
2025-07-11 15:29:56.520 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@7b306b9f
2025-07-11 15:29:57.845 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 15:29:57.966 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:29:58.024 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5328a9c1
2025-07-11 15:29:58.024 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:29:58.041 [main] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 9.22.3 by Redgate
2025-07-11 15:29:58.041 [main] INFO  o.f.c.i.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-11 15:29:58.041 [main] INFO  o.f.c.i.license.VersionPrinter - 
2025-07-11 15:29:58.057 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 15:29:58.061 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.0.
2025-07-11 15:29:58.082 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 3 migrations (execution time 00:00.014s)
2025-07-11 15:29:58.092 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 2
2025-07-11 15:29:58.107 [main] INFO  o.f.core.internal.command.DbMigrate - Migrating schema `beefcake` to version "3 - Fix enum values"
2025-07-11 15:29:58.242 [main] INFO  o.f.core.internal.command.DbMigrate - Successfully applied 1 migration to schema `beefcake`, now at version v3 (execution time 00:00.128s)
2025-07-11 15:29:58.246 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-11 15:29:58.246 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-11 15:29:58.368 [main] INFO  Application - Application started in 0.537 seconds.
2025-07-11 15:29:58.369 [main] INFO  Application - Application started: io.ktor.server.application.Application@4f0f7849
2025-07-11 15:29:58.435 [DefaultDispatcher-worker-2] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-11 15:30:08.326 [DefaultDispatcher-worker-2] WARN  Exposed - Transaction attempt #0 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:77)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:30:08.327 [DefaultDispatcher-worker-2] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:30:08.329 [DefaultDispatcher-worker-2] WARN  Exposed - Transaction attempt #1 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:77)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:30:08.329 [DefaultDispatcher-worker-2] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:30:08.330 [DefaultDispatcher-worker-2] WARN  Exposed - Transaction attempt #2 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:77)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:30:08.331 [eventLoopGroupProxy-4-1] ERROR AuthRoutes - 用户注册异常
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:77)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:30:08.338 [eventLoopGroupProxy-4-1] INFO  Application - 400 Bad Request: POST /api/auth/register - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-11 15:40:22.962 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@4f0f7849
2025-07-11 15:40:22.966 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@4f0f7849
2025-07-11 15:40:24.468 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 15:40:24.580 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:40:24.660 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5328a9c1
2025-07-11 15:40:24.661 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:40:24.685 [main] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 9.22.3 by Redgate
2025-07-11 15:40:24.685 [main] INFO  o.f.c.i.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-11 15:40:24.685 [main] INFO  o.f.c.i.license.VersionPrinter - 
2025-07-11 15:40:24.699 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 15:40:24.702 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.0.
2025-07-11 15:40:24.716 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 3 migrations (execution time 00:00.009s)
2025-07-11 15:40:24.724 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 3
2025-07-11 15:40:24.724 [main] WARN  o.f.core.internal.command.DbMigrate - Schema `beefcake` has a version (3) that is newer than the latest available migration (2) !
2025-07-11 15:40:24.725 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-11 15:40:24.729 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-11 15:40:24.766 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-11 15:40:24.842 [main] INFO  Application - Application started in 0.385 seconds.
2025-07-11 15:40:24.842 [main] INFO  Application - Application started: io.ktor.server.application.Application@4b0f2299
2025-07-11 15:40:24.906 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-11 15:41:00.966 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@4b0f2299
2025-07-11 15:41:00.970 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@4b0f2299
2025-07-11 15:41:01.849 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 15:41:01.946 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:41:01.997 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@35e478f
2025-07-11 15:41:01.997 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:41:02.017 [main] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 9.22.3 by Redgate
2025-07-11 15:41:02.017 [main] INFO  o.f.c.i.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-07-11 15:41:02.017 [main] INFO  o.f.c.i.license.VersionPrinter - 
2025-07-11 15:41:02.032 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 15:41:02.036 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.0.
2025-07-11 15:41:02.053 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 2 migrations (execution time 00:00.011s)
2025-07-11 15:41:02.061 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 2
2025-07-11 15:41:02.062 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-11 15:41:02.065 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-11 15:41:02.101 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-11 15:41:02.177 [main] INFO  Application - Application started in 0.34 seconds.
2025-07-11 15:41:02.177 [main] INFO  Application - Application started: io.ktor.server.application.Application@117d32e
2025-07-11 15:41:02.241 [DefaultDispatcher-worker-2] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-11 15:43:05.797 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@117d32e
2025-07-11 15:43:05.801 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@117d32e
2025-07-11 15:44:05.262 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 15:44:05.396 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:44:05.458 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5488b5c5
2025-07-11 15:44:05.458 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:44:05.540 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-11 15:44:05.540 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-11 15:44:05.540 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-11 15:44:05.557 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 15:44:05.562 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-11 15:44:05.585 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 2 migrations (execution time 00:00.015s)
2025-07-11 15:44:05.593 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 2
2025-07-11 15:44:05.594 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-11 15:44:05.598 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-11 15:44:05.639 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-11 15:44:05.677 [main] INFO  Application - Application started in 0.428 seconds.
2025-07-11 15:44:05.677 [main] INFO  Application - Application started: io.ktor.server.application.Application@3f50b680
2025-07-11 15:44:05.742 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-11 15:47:45.024 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@3f50b680
2025-07-11 15:47:45.032 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@3f50b680
2025-07-11 15:48:25.110 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-11 15:48:25.204 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 15:48:25.269 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5328a9c1
2025-07-11 15:48:25.269 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 15:48:25.371 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-11 15:48:25.371 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-11 15:48:25.371 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-11 15:48:25.392 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-11 15:48:25.400 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-11 15:48:25.420 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 2 migrations (execution time 00:00.015s)
2025-07-11 15:48:25.429 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 2
2025-07-11 15:48:25.431 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-11 15:48:25.436 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-11 15:48:25.472 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-11 15:48:25.502 [main] INFO  Application - Application started in 0.402 seconds.
2025-07-11 15:48:25.502 [main] INFO  Application - Application started: io.ktor.server.application.Application@6dab01d9
2025-07-11 15:48:25.562 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-11 15:48:41.050 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #0 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:48:41.051 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:48:41.052 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #1 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:48:41.053 [DefaultDispatcher-worker-1] WARN  Exposed - Wait 0 milliseconds before retrying
2025-07-11 15:48:41.054 [DefaultDispatcher-worker-1] WARN  Exposed - Transaction attempt #2 failed: java.sql.SQLException: Data truncated for column 'status' at row 1. Statement(s): INSERT INTO users (avatar_url, created_at, failed_login_count, locked_until, nickname, password, status, updated_at, user_type, username) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:48:41.055 [eventLoopGroupProxy-4-1] ERROR AuthRoutes - 用户注册异常
org.jetbrains.exposed.exceptions.ExposedSQLException: java.sql.SQLException: Data truncated for column 'status' at row 1
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:65)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:157)
	at org.jetbrains.exposed.sql.Transaction.exec(Transaction.kt:143)
	at org.jetbrains.exposed.sql.statements.Statement.execute(Statement.kt:28)
	at org.jetbrains.exposed.sql.QueriesKt.insert(Queries.kt:71)
	at com.beefcake.repositories.UserRepository$create$2.invokeSuspend(UserRepository.kt:16)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.repositories.UserRepository$create$2.invoke(UserRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:85)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:180)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: java.sql.SQLException: Data truncated for column 'status' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.jetbrains.exposed.sql.statements.jdbc.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:48)
	at org.jetbrains.exposed.sql.statements.InsertStatement.execInsertFunction(InsertStatement.kt:143)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:153)
	at org.jetbrains.exposed.sql.statements.InsertStatement.executeInternal(InsertStatement.kt:11)
	at org.jetbrains.exposed.sql.statements.Statement.executeIn$exposed_core(Statement.kt:63)
	... 19 common frames omitted
2025-07-11 15:48:41.061 [eventLoopGroupProxy-4-1] INFO  Application - 400 Bad Request: POST /api/auth/register - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-11 16:07:19.455 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@6dab01d9
2025-07-11 16:07:19.459 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@6dab01d9
