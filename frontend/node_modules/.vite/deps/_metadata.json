{"hash": "851a16c2", "browserHash": "11a066cc", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "eeda1ed0", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "2423caad", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "8d10a6b8", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "25410908", "needsInterop": true}, "@ant-design/icons": {"src": "../../@ant-design/icons/es/index.js", "file": "@ant-design_icons.js", "fileHash": "50929f8d", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/lib/index.mjs", "file": "@tanstack_react-query.js", "fileHash": "23155c6c", "needsInterop": false}, "antd": {"src": "../../antd/es/index.js", "file": "antd.js", "fileHash": "87589f85", "needsInterop": false}, "antd/locale/zh_CN": {"src": "../../antd/locale/zh_CN.js", "file": "antd_locale_zh_CN.js", "fileHash": "4a35633f", "needsInterop": true}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "70112d4f", "needsInterop": false}, "dayjs": {"src": "../../dayjs/dayjs.min.js", "file": "dayjs.js", "fileHash": "355c5d0a", "needsInterop": true}, "dayjs/locale/zh-cn": {"src": "../../dayjs/locale/zh-cn.js", "file": "dayjs_locale_zh-cn.js", "fileHash": "2d8ebeef", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "3cbbb796", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "9e514c44", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "88de1910", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "e9cf2c1c", "needsInterop": false}}, "chunks": {"chunk-WLPH6RON": {"file": "chunk-WLPH6RON.js"}, "chunk-WALXKXZM": {"file": "chunk-WALXKXZM.js"}, "chunk-G4NMQ6KI": {"file": "chunk-G4NMQ6KI.js"}, "chunk-WQMOH32Y": {"file": "chunk-WQMOH32Y.js"}, "chunk-5KWEUE2O": {"file": "chunk-5KWEUE2O.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}