{"version": 3, "file": "index.production.js", "sources": ["../../../query-core/build/lib/subscribable.mjs", "../../../query-core/build/lib/utils.mjs", "../../../query-core/build/lib/focusManager.mjs", "../../../query-core/build/lib/onlineManager.mjs", "../../../query-core/build/lib/retryer.mjs", "../../../query-core/build/lib/logger.mjs", "../../../query-core/build/lib/notifyManager.mjs", "../../../query-core/build/lib/removable.mjs", "../../../query-core/build/lib/query.mjs", "../../../query-core/build/lib/queryCache.mjs", "../../../query-core/build/lib/mutation.mjs", "../../../query-core/build/lib/mutationCache.mjs", "../../../query-core/build/lib/infiniteQueryBehavior.mjs", "../../../query-core/build/lib/queryObserver.mjs", "../../../query-core/build/lib/queriesObserver.mjs", "../../../query-core/build/lib/infiniteQueryObserver.mjs", "../../../query-core/build/lib/mutationObserver.mjs", "../../../query-core/build/lib/hydration.mjs", "../../src/reactBatchedUpdates.ts", "../../src/setBatchUpdatesFn.ts", "../../../../node_modules/.pnpm/use-sync-external-store@1.2.0_react@18.2.0/node_modules/use-sync-external-store/shim/index.js", "../../../../node_modules/.pnpm/use-sync-external-store@1.2.0_react@18.2.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.js", "../../src/useSyncExternalStore.ts", "../../src/QueryClientProvider.tsx", "../../src/isRestoring.tsx", "../../src/QueryErrorResetBoundary.tsx", "../../src/utils.ts", "../../src/errorBoundaryUtils.ts", "../../src/suspense.ts", "../../src/useQueries.ts", "../../src/useBaseQuery.ts", "../../src/Hydrate.tsx", "../../src/useMutation.ts", "../../../query-core/build/lib/queryClient.mjs", "../../src/queryOptions.ts", "../../src/useInfiniteQuery.ts", "../../src/useIsFetching.ts", "../../src/useIsMutating.ts", "../../src/useQuery.ts", "../../src/useSuspenseQueries.ts", "../../src/useSuspenseQuery.ts"], "sourcesContent": ["class Subscribable {\n  constructor() {\n    this.listeners = new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n\n  subscribe(listener) {\n    const identity = {\n      listener\n    };\n    this.listeners.add(identity);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(identity);\n      this.onUnsubscribe();\n    };\n  }\n\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n\n  onSubscribe() {// Do nothing\n  }\n\n  onUnsubscribe() {// Do nothing\n  }\n\n}\n\nexport { Subscribable };\n//# sourceMappingURL=subscribable.mjs.map\n", "// TYPES\n// UTILS\nconst isServer = typeof window === 'undefined' || 'Deno' in window;\nfunction noop() {\n  return undefined;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === 'number' && value >= 0 && value !== Infinity;\n}\nfunction difference(array1, array2) {\n  return array1.filter(x => !array2.includes(x));\n}\nfunction replaceAt(array, index, value) {\n  const copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQueryKey(arg1)) {\n    return arg1;\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3,\n      queryKey: arg1,\n      queryFn: arg2\n    };\n  }\n\n  return { ...arg2,\n    queryKey: arg1\n  };\n}\nfunction parseMutationArgs(arg1, arg2, arg3) {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3,\n        mutationKey: arg1,\n        mutationFn: arg2\n      };\n    }\n\n    return { ...arg2,\n      mutationKey: arg1\n    };\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2,\n      mutationFn: arg1\n    };\n  }\n\n  return { ...arg1\n  };\n}\nfunction parseFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [{ ...arg2,\n    queryKey: arg1\n  }, arg3] : [arg1 || {}, arg2];\n}\nfunction parseMutationFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [{ ...arg2,\n    mutationKey: arg1\n  }, arg3] : [arg1 || {}, arg2];\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive();\n\n    if (type === 'active' && !isActive) {\n      return false;\n    }\n\n    if (type === 'inactive' && isActive) {\n      return false;\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false;\n  }\n\n  if (typeof fetchStatus !== 'undefined' && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const {\n    exact,\n    fetching,\n    predicate,\n    mutationKey\n  } = filters;\n\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n\n    if (exact) {\n      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n\n  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;\n  return hashFn(queryKey);\n}\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\n\nfunction hashQueryKey(queryKey) {\n  return JSON.stringify(queryKey, (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n    result[key] = val[key];\n    return result;\n  }, {}) : val);\n}\n/**\n * Checks if key `b` partially matches with key `a`.\n */\n\nfunction partialMatchKey(a, b) {\n  return partialDeepEqual(a, b);\n}\n/**\n * Checks if `b` partially matches with `a`.\n */\n\nfunction partialDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== typeof b) {\n    return false;\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(key => !partialDeepEqual(a[key], b[key]));\n  }\n\n  return false;\n}\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\n\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b);\n\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aSize = array ? a.length : Object.keys(a).length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      copy[key] = replaceEqualDeep(a[key], b[key]);\n\n      if (copy[key] === a[key]) {\n        equalItems++;\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n\n  return b;\n}\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\n\nfunction shallowEqualObjects(a, b) {\n  if (a && !b || b && !a) {\n    return false;\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n} // Copied from: https://github.com/jonschlinkert/is-plain-object\n\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  } // If has modified constructor\n\n\n  const ctor = o.constructor;\n\n  if (typeof ctor === 'undefined') {\n    return true;\n  } // If has modified prototype\n\n\n  const prot = ctor.prototype;\n\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  } // If constructor does not have an Object-specific method\n\n\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false;\n  } // Most likely a plain Object\n\n\n  return true;\n}\n\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isQueryKey(value) {\n  return Array.isArray(value);\n}\nfunction isError(value) {\n  return value instanceof Error;\n}\nfunction sleep(timeout) {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout);\n  });\n}\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nfunction scheduleMicrotask(callback) {\n  sleep(0).then(callback);\n}\nfunction getAbortController() {\n  if (typeof AbortController === 'function') {\n    return new AbortController();\n  }\n\n  return;\n}\nfunction replaceData(prevData, data, options) {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual != null && options.isDataEqual(prevData, data)) {\n    return prevData;\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data);\n  }\n\n  return data;\n}\n\nexport { difference, functionalUpdate, getAbortController, hashQueryKey, hashQueryKeyByOptions, isError, isPlainArray, isPlainObject, isQueryKey, isServer, isValidTimeout, matchMutation, matchQuery, noop, parseFilterArgs, parseMutationArgs, parseMutationFilterArgs, parseQueryArgs, partialDeepEqual, partialMatchKey, replaceAt, replaceData, replaceEqualDeep, scheduleMicrotask, shallowEqualObjects, sleep, timeUntilStale };\n//# sourceMappingURL=utils.mjs.map\n", "import { Subscribable } from './subscribable.mjs';\nimport { isServer } from './utils.mjs';\n\nclass FocusManager extends Subscribable {\n  constructor() {\n    super();\n\n    this.setup = onFocus => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus(); // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n\n      return;\n    };\n  }\n\n  onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  }\n\n  setEventListener(setup) {\n    var _this$cleanup2;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(focused => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n\n  setFocused(focused) {\n    const changed = this.focused !== focused;\n\n    if (changed) {\n      this.focused = focused;\n      this.onFocus();\n    }\n  }\n\n  onFocus() {\n    this.listeners.forEach(({\n      listener\n    }) => {\n      listener();\n    });\n  }\n\n  isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  }\n\n}\nconst focusManager = new FocusManager();\n\nexport { FocusManager, focusManager };\n//# sourceMappingURL=focusManager.mjs.map\n", "import { Subscribable } from './subscribable.mjs';\nimport { isServer } from './utils.mjs';\n\nconst onlineEvents = ['online', 'offline'];\nclass OnlineManager extends Subscribable {\n  constructor() {\n    super();\n\n    this.setup = onOnline => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onOnline(); // Listen to online\n\n\n        onlineEvents.forEach(event => {\n          window.addEventListener(event, listener, false);\n        });\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach(event => {\n            window.removeEventListener(event, listener);\n          });\n        };\n      }\n\n      return;\n    };\n  }\n\n  onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  }\n\n  setEventListener(setup) {\n    var _this$cleanup2;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(online => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online);\n      } else {\n        this.onOnline();\n      }\n    });\n  }\n\n  setOnline(online) {\n    const changed = this.online !== online;\n\n    if (changed) {\n      this.online = online;\n      this.onOnline();\n    }\n  }\n\n  onOnline() {\n    this.listeners.forEach(({\n      listener\n    }) => {\n      listener();\n    });\n  }\n\n  isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  }\n\n}\nconst onlineManager = new OnlineManager();\n\nexport { OnlineManager, onlineManager };\n//# sourceMappingURL=onlineManager.mjs.map\n", "import { focusManager } from './focusManager.mjs';\nimport { onlineManager } from './onlineManager.mjs';\nimport { sleep } from './utils.mjs';\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * 2 ** failureCount, 30000);\n}\n\nfunction canFetch(networkMode) {\n  return (networkMode != null ? networkMode : 'online') === 'online' ? onlineManager.isOnline() : true;\n}\nclass CancelledError {\n  constructor(options) {\n    this.revert = options == null ? void 0 : options.revert;\n    this.silent = options == null ? void 0 : options.silent;\n  }\n\n}\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  let promiseResolve;\n  let promiseReject;\n  const promise = new Promise((outerResolve, outerReject) => {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  const cancel = cancelOptions => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort == null ? void 0 : config.abort();\n    }\n  };\n\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n\n  const shouldPause = () => !focusManager.isFocused() || config.networkMode !== 'always' && !onlineManager.isOnline();\n\n  const resolve = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  const reject = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  const pause = () => {\n    return new Promise(continueResolve => {\n      continueFn = value => {\n        const canContinue = isResolved || !shouldPause();\n\n        if (canContinue) {\n          continueResolve(value);\n        }\n\n        return canContinue;\n      };\n\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(() => {\n      continueFn = undefined;\n\n      if (!isResolved) {\n        config.onContinue == null ? void 0 : config.onContinue();\n      }\n    });\n  }; // Create loop function\n\n\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return;\n    }\n\n    let promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n\n    Promise.resolve(promiseOrValue).then(resolve).catch(error => {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      const retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      const retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      const delay = typeof retryDelay === 'function' ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === 'number' && failureCount < retry || typeof retry === 'function' && retry(failureCount, error);\n\n      if (isRetryCancelled || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(failureCount, error); // Delay\n\n      sleep(delay) // Pause if the document is not visible or when the device is offline\n      .then(() => {\n        if (shouldPause()) {\n          return pause();\n        }\n\n        return;\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  if (canFetch(config.networkMode)) {\n    run();\n  } else {\n    pause().then(run);\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn == null ? void 0 : continueFn();\n      return didContinue ? promise : Promise.resolve();\n    },\n    cancelRetry,\n    continueRetry\n  };\n}\n\nexport { CancelledError, canFetch, createRetryer, isCancelledError };\n//# sourceMappingURL=retryer.mjs.map\n", "const defaultLogger = console;\n\nexport { defaultLogger };\n//# sourceMappingURL=logger.mjs.map\n", "import { scheduleMicrotask } from './utils.mjs';\n\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n\n  let notifyFn = callback => {\n    callback();\n  };\n\n  let batchNotifyFn = callback => {\n    callback();\n  };\n\n  const batch = callback => {\n    let result;\n    transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      transactions--;\n\n      if (!transactions) {\n        flush();\n      }\n    }\n\n    return result;\n  };\n\n  const schedule = callback => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleMicrotask(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n\n\n  const batchCalls = callback => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args);\n      });\n    };\n  };\n\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n\n    if (originalQueue.length) {\n      scheduleMicrotask(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach(callback => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n\n\n  const setNotifyFunction = fn => {\n    notifyFn = fn;\n  };\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n\n\n  const setBatchNotifyFunction = fn => {\n    batchNotifyFn = fn;\n  };\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction\n  };\n} // SINGLETON\n\nconst notifyManager = createNotifyManager();\n\nexport { createNotifyManager, notifyManager };\n//# sourceMappingURL=notifyManager.mjs.map\n", "import { isValidTimeout, isServer } from './utils.mjs';\n\nclass Removable {\n  destroy() {\n    this.clearGcTimeout();\n  }\n\n  scheduleGc() {\n    this.clearGcTimeout();\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.cacheTime);\n    }\n  }\n\n  updateCacheTime(newCacheTime) {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(this.cacheTime || 0, newCacheTime != null ? newCacheTime : isServer ? Infinity : 5 * 60 * 1000);\n  }\n\n  clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  }\n\n}\n\nexport { Removable };\n//# sourceMappingURL=removable.mjs.map\n", "import { replaceData, noop, timeUntilStale, getAbortController } from './utils.mjs';\nimport { defaultLogger } from './logger.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { createRetryer, isCancelledError, canFetch } from './retryer.mjs';\nimport { Removable } from './removable.mjs';\n\n// CLASS\nclass Query extends Removable {\n  constructor(config) {\n    super();\n    this.abortSignalConsumed = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.logger = config.logger || defaultLogger;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || getDefaultState(this.options);\n    this.state = this.initialState;\n    this.scheduleGc();\n  }\n\n  get meta() {\n    return this.options.meta;\n  }\n\n  setOptions(options) {\n    this.options = { ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this);\n    }\n  }\n\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options); // Set data and mark it as cached\n\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt,\n      manual: options == null ? void 0 : options.manual\n    });\n    return data;\n  }\n\n  setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state,\n      setStateOptions\n    });\n  }\n\n  cancel(options) {\n    var _this$retryer;\n\n    const promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n\n  reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  }\n\n  isActive() {\n    return this.observers.some(observer => observer.options.enabled !== false);\n  }\n\n  isDisabled() {\n    return this.getObserversCount() > 0 && !this.isActive();\n  }\n\n  isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(observer => observer.getCurrentResult().isStale);\n  }\n\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n\n  onFocus() {\n    var _this$retryer2;\n\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  }\n\n  onOnline() {\n    var _this$retryer3;\n\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  }\n\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer\n      });\n    }\n  }\n\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        this.scheduleGc();\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer\n      });\n    }\n  }\n\n  getObserversCount() {\n    return this.observers.length;\n  }\n\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  }\n\n  fetch(options, fetchOptions) {\n    var _this$options$behavio, _context$fetchOptions;\n\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions != null && fetchOptions.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\"As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']\");\n      }\n    }\n\n    const abortController = getAbortController(); // Create query function context\n\n    const queryFnContext = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    }; // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n\n    const addSignalProperty = object => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true;\n            return abortController.signal;\n          }\n\n          return undefined;\n        }\n      });\n    };\n\n    addSignalProperty(queryFnContext); // Create fetch function\n\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\"Missing queryFn for queryKey '\" + this.options.queryHash + \"'\");\n      }\n\n      this.abortSignalConsumed = false;\n      return this.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    (_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch(context); // Store state in case the current fetch needs to be reverted\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (this.state.fetchStatus === 'idle' || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    }\n\n    const onError = error => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n\n      if (!isCancelledError(error)) {\n        var _this$cache$config$on, _this$cache$config, _this$cache$config$on2, _this$cache$config2;\n\n        // Notify cache callback\n        (_this$cache$config$on = (_this$cache$config = this.cache.config).onError) == null ? void 0 : _this$cache$config$on.call(_this$cache$config, error, this);\n        (_this$cache$config$on2 = (_this$cache$config2 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on2.call(_this$cache$config2, this.state.data, error, this);\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error);\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc();\n      }\n\n      this.isFetchingOptimistic = false;\n    }; // Try to fetch the data\n\n\n    this.retryer = createRetryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : abortController.abort.bind(abortController),\n      onSuccess: data => {\n        var _this$cache$config$on3, _this$cache$config3, _this$cache$config$on4, _this$cache$config4;\n\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\"Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: \" + this.queryHash);\n          }\n\n          onError(new Error(this.queryHash + \" data is undefined\"));\n          return;\n        }\n\n        this.setData(data); // Notify cache callback\n\n        (_this$cache$config$on3 = (_this$cache$config3 = this.cache.config).onSuccess) == null ? void 0 : _this$cache$config$on3.call(_this$cache$config3, data, this);\n        (_this$cache$config$on4 = (_this$cache$config4 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on4.call(_this$cache$config4, data, this.state.error, this);\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc();\n        }\n\n        this.isFetchingOptimistic = false;\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({\n          type: 'failed',\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: () => {\n        this.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  }\n\n  dispatch(action) {\n    const reducer = state => {\n      var _action$meta, _action$dataUpdatedAt;\n\n      switch (action.type) {\n        case 'failed':\n          return { ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n\n        case 'pause':\n          return { ...state,\n            fetchStatus: 'paused'\n          };\n\n        case 'continue':\n          return { ...state,\n            fetchStatus: 'fetching'\n          };\n\n        case 'fetch':\n          return { ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n            fetchStatus: canFetch(this.options.networkMode) ? 'fetching' : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading'\n            })\n          };\n\n        case 'success':\n          return { ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n\n        case 'error':\n          const error = action.error;\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState,\n              fetchStatus: 'idle'\n            };\n          }\n\n          return { ...state,\n            error: error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error'\n          };\n\n        case 'invalidate':\n          return { ...state,\n            isInvalidated: true\n          };\n\n        case 'setState':\n          return { ...state,\n            ...action.state\n          };\n      }\n    };\n\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate(action);\n      });\n      this.cache.notify({\n        query: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n\n}\n\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n  const hasData = typeof data !== 'undefined';\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle'\n  };\n}\n\nexport { Query };\n//# sourceMappingURL=query.mjs.map\n", "import { hashQueryKeyByOptions, parseFilterArgs, matchQuery } from './utils.mjs';\nimport { Query } from './query.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { Subscribable } from './subscribable.mjs';\n\n// CLASS\nclass QueryCache extends Subscribable {\n  constructor(config) {\n    super();\n    this.config = config || {};\n    this.queries = [];\n    this.queriesMap = {};\n  }\n\n  build(client, options, state) {\n    var _options$queryHash;\n\n    const queryKey = options.queryKey;\n    const queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n\n    return query;\n  }\n\n  add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'added',\n        query\n      });\n    }\n  }\n\n  remove(query) {\n    const queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(x => x !== query);\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'removed',\n        query\n      });\n    }\n  }\n\n  clear() {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        this.remove(query);\n      });\n    });\n  }\n\n  get(queryHash) {\n    return this.queriesMap[queryHash];\n  }\n\n  getAll() {\n    return this.queries;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(query => matchQuery(filters, query));\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n    return Object.keys(filters).length > 0 ? this.queries.filter(query => matchQuery(filters, query)) : this.queries;\n  }\n\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(event);\n      });\n    });\n  }\n\n  onFocus() {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onFocus();\n      });\n    });\n  }\n\n  onOnline() {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onOnline();\n      });\n    });\n  }\n\n}\n\nexport { QueryCache };\n//# sourceMappingURL=queryCache.mjs.map\n", "import { defaultLogger } from './logger.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { Removable } from './removable.mjs';\nimport { createRetryer, canFetch } from './retryer.mjs';\n\n// CLASS\nclass Mutation extends Removable {\n  constructor(config) {\n    super();\n    this.defaultOptions = config.defaultOptions;\n    this.mutationId = config.mutationId;\n    this.mutationCache = config.mutationCache;\n    this.logger = config.logger || defaultLogger;\n    this.observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n\n  setOptions(options) {\n    this.options = { ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n\n  get meta() {\n    return this.options.meta;\n  }\n\n  setState(state) {\n    this.dispatch({\n      type: 'setState',\n      state\n    });\n  }\n\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the mutation from being garbage collected\n\n      this.clearGcTimeout();\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer\n      });\n    }\n  }\n\n  removeObserver(observer) {\n    this.observers = this.observers.filter(x => x !== observer);\n    this.scheduleGc();\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer\n    });\n  }\n\n  optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc();\n      } else {\n        this.mutationCache.remove(this);\n      }\n    }\n  }\n\n  continue() {\n    var _this$retryer$continu, _this$retryer;\n\n    return (_this$retryer$continu = (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.continue()) != null ? _this$retryer$continu : this.execute();\n  }\n\n  async execute() {\n    const executeMutation = () => {\n      var _this$options$retry;\n\n      this.retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found');\n          }\n\n          return this.options.mutationFn(this.state.variables);\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({\n            type: 'failed',\n            failureCount,\n            error\n          });\n        },\n        onPause: () => {\n          this.dispatch({\n            type: 'pause'\n          });\n        },\n        onContinue: () => {\n          this.dispatch({\n            type: 'continue'\n          });\n        },\n        retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode\n      });\n      return this.retryer.promise;\n    };\n\n    const restored = this.state.status === 'loading';\n\n    try {\n      var _this$mutationCache$c3, _this$mutationCache$c4, _this$options$onSucce, _this$options2, _this$mutationCache$c5, _this$mutationCache$c6, _this$options$onSettl, _this$options3;\n\n      if (!restored) {\n        var _this$mutationCache$c, _this$mutationCache$c2, _this$options$onMutat, _this$options;\n\n        this.dispatch({\n          type: 'loading',\n          variables: this.options.variables\n        }); // Notify cache callback\n\n        await ((_this$mutationCache$c = (_this$mutationCache$c2 = this.mutationCache.config).onMutate) == null ? void 0 : _this$mutationCache$c.call(_this$mutationCache$c2, this.state.variables, this));\n        const context = await ((_this$options$onMutat = (_this$options = this.options).onMutate) == null ? void 0 : _this$options$onMutat.call(_this$options, this.state.variables));\n\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables\n          });\n        }\n      }\n\n      const data = await executeMutation(); // Notify cache callback\n\n      await ((_this$mutationCache$c3 = (_this$mutationCache$c4 = this.mutationCache.config).onSuccess) == null ? void 0 : _this$mutationCache$c3.call(_this$mutationCache$c4, data, this.state.variables, this.state.context, this));\n      await ((_this$options$onSucce = (_this$options2 = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options2, data, this.state.variables, this.state.context)); // Notify cache callback\n\n      await ((_this$mutationCache$c5 = (_this$mutationCache$c6 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c5.call(_this$mutationCache$c6, data, null, this.state.variables, this.state.context, this));\n      await ((_this$options$onSettl = (_this$options3 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options3, data, null, this.state.variables, this.state.context));\n      this.dispatch({\n        type: 'success',\n        data\n      });\n      return data;\n    } catch (error) {\n      try {\n        var _this$mutationCache$c7, _this$mutationCache$c8, _this$options$onError, _this$options4, _this$mutationCache$c9, _this$mutationCache$c10, _this$options$onSettl2, _this$options5;\n\n        // Notify cache callback\n        await ((_this$mutationCache$c7 = (_this$mutationCache$c8 = this.mutationCache.config).onError) == null ? void 0 : _this$mutationCache$c7.call(_this$mutationCache$c8, error, this.state.variables, this.state.context, this));\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error);\n        }\n\n        await ((_this$options$onError = (_this$options4 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options4, error, this.state.variables, this.state.context)); // Notify cache callback\n\n        await ((_this$mutationCache$c9 = (_this$mutationCache$c10 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c9.call(_this$mutationCache$c10, undefined, error, this.state.variables, this.state.context, this));\n        await ((_this$options$onSettl2 = (_this$options5 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options5, undefined, error, this.state.variables, this.state.context));\n        throw error;\n      } finally {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n    }\n  }\n\n  dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case 'failed':\n          return { ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n\n        case 'pause':\n          return { ...state,\n            isPaused: true\n          };\n\n        case 'continue':\n          return { ...state,\n            isPaused: false\n          };\n\n        case 'loading':\n          return { ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables\n          };\n\n        case 'success':\n          return { ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false\n          };\n\n        case 'error':\n          return { ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error'\n          };\n\n        case 'setState':\n          return { ...state,\n            ...action.state\n          };\n      }\n    };\n\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onMutationUpdate(action);\n      });\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n\n}\nfunction getDefaultState() {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined\n  };\n}\n\nexport { Mutation, getDefaultState };\n//# sourceMappingURL=mutation.mjs.map\n", "import { notify<PERSON>anager } from './notifyManager.mjs';\nimport { Mutation } from './mutation.mjs';\nimport { matchMutation, noop } from './utils.mjs';\nimport { Subscribable } from './subscribable.mjs';\n\n// CLASS\nclass MutationCache extends Subscribable {\n  constructor(config) {\n    super();\n    this.config = config || {};\n    this.mutations = [];\n    this.mutationId = 0;\n  }\n\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined\n    });\n    this.add(mutation);\n    return mutation;\n  }\n\n  add(mutation) {\n    this.mutations.push(mutation);\n    this.notify({\n      type: 'added',\n      mutation\n    });\n  }\n\n  remove(mutation) {\n    this.mutations = this.mutations.filter(x => x !== mutation);\n    this.notify({\n      type: 'removed',\n      mutation\n    });\n  }\n\n  clear() {\n    notifyManager.batch(() => {\n      this.mutations.forEach(mutation => {\n        this.remove(mutation);\n      });\n    });\n  }\n\n  getAll() {\n    return this.mutations;\n  }\n\n  find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(mutation => matchMutation(filters, mutation));\n  }\n\n  findAll(filters) {\n    return this.mutations.filter(mutation => matchMutation(filters, mutation));\n  }\n\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(event);\n      });\n    });\n  }\n\n  resumePausedMutations() {\n    var _this$resuming;\n\n    this.resuming = ((_this$resuming = this.resuming) != null ? _this$resuming : Promise.resolve()).then(() => {\n      const pausedMutations = this.mutations.filter(x => x.state.isPaused);\n      return notifyManager.batch(() => pausedMutations.reduce((promise, mutation) => promise.then(() => mutation.continue().catch(noop)), Promise.resolve()));\n    }).then(() => {\n      this.resuming = undefined;\n    });\n    return this.resuming;\n  }\n\n}\n\nexport { MutationCache };\n//# sourceMappingURL=mutationCache.mjs.map\n", "function infiniteQueryBehavior() {\n  return {\n    onFetch: context => {\n      context.fetchFn = () => {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n\n        const refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        const fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        const pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        const isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        const isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        const oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        const oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        let newPageParams = oldPageParams;\n        let cancelled = false;\n\n        const addSignalProperty = object => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              var _context$signal;\n\n              if ((_context$signal = context.signal) != null && _context$signal.aborted) {\n                cancelled = true;\n              } else {\n                var _context$signal2;\n\n                (_context$signal2 = context.signal) == null ? void 0 : _context$signal2.addEventListener('abort', () => {\n                  cancelled = true;\n                });\n              }\n\n              return context.signal;\n            }\n          });\n        }; // Get query function\n\n\n        const queryFn = context.options.queryFn || (() => Promise.reject(\"Missing queryFn for queryKey '\" + context.options.queryHash + \"'\"));\n\n        const buildNewPages = (pages, param, page, previous) => {\n          newPageParams = previous ? [param, ...newPageParams] : [...newPageParams, param];\n          return previous ? [page, ...pages] : [...pages, page];\n        }; // Create function to fetch a page\n\n\n        const fetchPage = (pages, manual, param, previous) => {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n\n          const queryFnContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const queryFnResult = queryFn(queryFnContext);\n          const promise = Promise.resolve(queryFnResult).then(page => buildNewPages(pages, param, page, previous));\n          return promise;\n        };\n\n        let promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param);\n        } // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param, true);\n        } // Refetch pages\n        else {\n          newPageParams = [];\n          const manual = typeof context.options.getNextPageParam === 'undefined';\n          const shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n          promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then(pages => {\n              const shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n\n              if (shouldFetchNextPage) {\n                const param = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n                return fetchPage(pages, manual, param);\n              }\n\n              return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n            });\n          }\n        }\n\n        const finalPromise = promise.then(pages => ({\n          pages,\n          pageParams: newPageParams\n        }));\n        return finalPromise;\n      };\n    }\n  };\n}\nfunction getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nfunction getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n\n  return;\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n\n  return;\n}\n\nexport { getNextPageParam, getPreviousPageParam, hasNextPage, hasPreviousPage, infiniteQueryBehavior };\n//# sourceMappingURL=infiniteQueryBehavior.mjs.map\n", "import { shallowEqualObjects, noop, isServer, isValidTimeout, timeUntilStale, replaceData } from './utils.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { focusManager } from './focusManager.mjs';\nimport { Subscribable } from './subscribable.mjs';\nimport { canFetch, isCancelledError } from './retryer.mjs';\n\nclass QueryObserver extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.client = client;\n    this.options = options;\n    this.trackedProps = new Set();\n    this.selectError = null;\n    this.bindMethods();\n    this.setOptions(options);\n  }\n\n  bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  }\n\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  }\n\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  }\n\n  destroy() {\n    this.listeners = new Set();\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n    this.currentQuery.removeObserver(this);\n  }\n\n  setOptions(options, notifyOptions) {\n    const prevOptions = this.options;\n    const prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryOptions(options);\n\n    if (process.env.NODE_ENV !== 'production' && typeof (options == null ? void 0 : options.isDataEqual) !== 'undefined') {\n      this.client.getLogger().error(\"The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option\");\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this\n      });\n    }\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    const mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n\n  getOptimisticResult(options) {\n    const query = this.client.getQueryCache().build(this.client, options);\n    const result = this.createResult(query, options);\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result;\n      this.currentResultOptions = this.options;\n      this.currentResultState = this.currentQuery.state;\n    }\n\n    return result;\n  }\n\n  getCurrentResult() {\n    return this.currentResult;\n  }\n\n  trackResult(result) {\n    const trackedResult = {};\n    Object.keys(result).forEach(key => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key);\n          return result[key];\n        }\n      });\n    });\n    return trackedResult;\n  }\n\n  getCurrentQuery() {\n    return this.currentQuery;\n  }\n\n  remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  }\n\n  refetch({\n    refetchPage,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        refetchPage\n      }\n    });\n  }\n\n  fetchOptimistic(options) {\n    const defaultedOptions = this.client.defaultQueryOptions(options);\n    const query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    query.isFetchingOptimistic = true;\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n\n  fetch(fetchOptions) {\n    var _fetchOptions$cancelR;\n\n    return this.executeFetch({ ...fetchOptions,\n      cancelRefetch: (_fetchOptions$cancelR = fetchOptions.cancelRefetch) != null ? _fetchOptions$cancelR : true\n    }).then(() => {\n      this.updateResult();\n      return this.currentResult;\n    });\n  }\n\n  executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    let promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions != null && fetchOptions.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  }\n\n  updateStaleTimeout() {\n    this.clearStaleTimeout();\n\n    if (isServer || this.currentResult.isStale || !isValidTimeout(this.options.staleTime)) {\n      return;\n    }\n\n    const time = timeUntilStale(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    const timeout = time + 1;\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n\n  computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  }\n\n  updateRefetchInterval(nextInterval) {\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (isServer || this.options.enabled === false || !isValidTimeout(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        this.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  }\n\n  updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  }\n\n  clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  }\n\n  clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  }\n\n  createResult(query, options) {\n    const prevQuery = this.currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.currentResult;\n    const prevResultState = this.currentResultState;\n    const prevResultOptions = this.currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    const prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    const {\n      state\n    } = query;\n    let {\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      fetchStatus,\n      status\n    } = state;\n    let isPreviousData = false;\n    let isPlaceholderData = false;\n    let data; // Optimistically set result in fetching state if needed\n\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode) ? 'fetching' : 'paused';\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle';\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdatedAt && prevQueryResult != null && prevQueryResult.isSuccess && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n        data = this.selectResult;\n      } else {\n        try {\n          this.selectFn = options.select;\n          data = options.select(state.data);\n          data = replaceData(prevResult == null ? void 0 : prevResult.data, data, options);\n          this.selectResult = data;\n          this.selectError = null;\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError);\n          }\n\n          this.selectError = selectError;\n        }\n      }\n    } // Use query data\n    else {\n      data = state.data;\n    } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && status === 'loading') {\n      let placeholderData; // Memoize placeholder data\n\n      if (prevResult != null && prevResult.isPlaceholderData && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n            this.selectError = null;\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError);\n            }\n\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = replaceData(prevResult == null ? void 0 : prevResult.data, placeholderData, options);\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    const isFetching = fetchStatus === 'fetching';\n    const isLoading = status === 'loading';\n    const isError = status === 'error';\n    const result = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  }\n\n  updateResult(notifyOptions) {\n    const prevResult = this.currentResult;\n    const nextResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify and update result if something has changed\n\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return;\n    }\n\n    this.currentResult = nextResult; // Determine which callbacks to trigger\n\n    const defaultNotifyOptions = {\n      cache: true\n    };\n\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n\n      const {\n        notifyOnChangeProps\n      } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === 'function' ? notifyOnChangeProps() : notifyOnChangeProps;\n\n      if (notifyOnChangePropsValue === 'all' || !notifyOnChangePropsValue && !this.trackedProps.size) {\n        return true;\n      }\n\n      const includedProps = new Set(notifyOnChangePropsValue != null ? notifyOnChangePropsValue : this.trackedProps);\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error');\n      }\n\n      return Object.keys(this.currentResult).some(key => {\n        const typedKey = key;\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify({ ...defaultNotifyOptions,\n      ...notifyOptions\n    });\n  }\n\n  updateQuery() {\n    const query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    const prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n\n  onQueryUpdate(action) {\n    const notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual;\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  }\n\n  notify(notifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        var _this$options$onSucce, _this$options, _this$options$onSettl, _this$options2;\n\n        (_this$options$onSucce = (_this$options = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options, this.currentResult.data);\n        (_this$options$onSettl = (_this$options2 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options2, this.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        var _this$options$onError, _this$options3, _this$options$onSettl2, _this$options4;\n\n        (_this$options$onError = (_this$options3 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options3, this.currentResult.error);\n        (_this$options$onSettl2 = (_this$options4 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options4, undefined, this.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({\n          listener\n        }) => {\n          listener(this.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  }\n\n}\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n} // this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\n\n\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult, options) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false;\n  } // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n\n\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData;\n  } // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n\n\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  } // basically, just keep previous properties if nothing changed\n\n\n  return false;\n}\n\nexport { QueryObserver };\n//# sourceMappingURL=queryObserver.mjs.map\n", "import { difference, replaceAt } from './utils.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { QueryObserver } from './queryObserver.mjs';\nimport { Subscribable } from './subscribable.mjs';\n\nclass QueriesObserver extends Subscribable {\n  constructor(client, queries) {\n    super();\n    this.client = client;\n    this.queries = [];\n    this.result = [];\n    this.observers = [];\n    this.observersMap = {};\n\n    if (queries) {\n      this.setQueries(queries);\n    }\n  }\n\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.observers.forEach(observer => {\n        observer.subscribe(result => {\n          this.onUpdate(observer, result);\n        });\n      });\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.listeners.size) {\n      this.destroy();\n    }\n  }\n\n  destroy() {\n    this.listeners = new Set();\n    this.observers.forEach(observer => {\n      observer.destroy();\n    });\n  }\n\n  setQueries(queries, notifyOptions) {\n    this.queries = queries;\n    notifyManager.batch(() => {\n      const prevObservers = this.observers;\n      const newObserverMatches = this.findMatchingObservers(this.queries); // set options for the new observers to notify of changes\n\n      newObserverMatches.forEach(match => match.observer.setOptions(match.defaultedQueryOptions, notifyOptions));\n      const newObservers = newObserverMatches.map(match => match.observer);\n      const newObserversMap = Object.fromEntries(newObservers.map(observer => [observer.options.queryHash, observer]));\n      const newResult = newObservers.map(observer => observer.getCurrentResult());\n      const hasIndexChange = newObservers.some((observer, index) => observer !== prevObservers[index]);\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n\n      this.observers = newObservers;\n      this.observersMap = newObserversMap;\n      this.result = newResult;\n\n      if (!this.hasListeners()) {\n        return;\n      }\n\n      difference(prevObservers, newObservers).forEach(observer => {\n        observer.destroy();\n      });\n      difference(newObservers, prevObservers).forEach(observer => {\n        observer.subscribe(result => {\n          this.onUpdate(observer, result);\n        });\n      });\n      this.notify();\n    });\n  }\n\n  getCurrentResult() {\n    return this.result;\n  }\n\n  getQueries() {\n    return this.observers.map(observer => observer.getCurrentQuery());\n  }\n\n  getObservers() {\n    return this.observers;\n  }\n\n  getOptimisticResult(queries) {\n    return this.findMatchingObservers(queries).map(match => match.observer.getOptimisticResult(match.defaultedQueryOptions));\n  }\n\n  findMatchingObservers(queries) {\n    const prevObservers = this.observers;\n    const prevObserversMap = new Map(prevObservers.map(observer => [observer.options.queryHash, observer]));\n    const defaultedQueryOptions = queries.map(options => this.client.defaultQueryOptions(options));\n    const matchingObservers = defaultedQueryOptions.flatMap(defaultedOptions => {\n      const match = prevObserversMap.get(defaultedOptions.queryHash);\n\n      if (match != null) {\n        return [{\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        }];\n      }\n\n      return [];\n    });\n    const matchedQueryHashes = new Set(matchingObservers.map(match => match.defaultedQueryOptions.queryHash));\n    const unmatchedQueries = defaultedQueryOptions.filter(defaultedOptions => !matchedQueryHashes.has(defaultedOptions.queryHash));\n    const matchingObserversSet = new Set(matchingObservers.map(match => match.observer));\n    const unmatchedObservers = prevObservers.filter(prevObserver => !matchingObserversSet.has(prevObserver));\n\n    const getObserver = options => {\n      const defaultedOptions = this.client.defaultQueryOptions(options);\n      const currentObserver = this.observersMap[defaultedOptions.queryHash];\n      return currentObserver != null ? currentObserver : new QueryObserver(this.client, defaultedOptions);\n    };\n\n    const newOrReusedObservers = unmatchedQueries.map((options, index) => {\n      if (options.keepPreviousData) {\n        // return previous data from one of the observers that no longer match\n        const previouslyUsedObserver = unmatchedObservers[index];\n\n        if (previouslyUsedObserver !== undefined) {\n          return {\n            defaultedQueryOptions: options,\n            observer: previouslyUsedObserver\n          };\n        }\n      }\n\n      return {\n        defaultedQueryOptions: options,\n        observer: getObserver(options)\n      };\n    });\n\n    const sortMatchesByOrderOfQueries = (a, b) => defaultedQueryOptions.indexOf(a.defaultedQueryOptions) - defaultedQueryOptions.indexOf(b.defaultedQueryOptions);\n\n    return matchingObservers.concat(newOrReusedObservers).sort(sortMatchesByOrderOfQueries);\n  }\n\n  onUpdate(observer, result) {\n    const index = this.observers.indexOf(observer);\n\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result);\n      this.notify();\n    }\n  }\n\n  notify() {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(this.result);\n      });\n    });\n  }\n\n}\n\nexport { QueriesObserver };\n//# sourceMappingURL=queriesObserver.mjs.map\n", "import { QueryObserver } from './queryObserver.mjs';\nimport { infiniteQueryBehavior, hasNextPage, hasPreviousPage } from './infiniteQueryBehavior.mjs';\n\nclass InfiniteQueryObserver extends QueryObserver {\n  // Type override\n  // Type override\n  // Type override\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(client, options) {\n    super(client, options);\n  }\n\n  bindMethods() {\n    super.bindMethods();\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  }\n\n  setOptions(options, notifyOptions) {\n    super.setOptions({ ...options,\n      behavior: infiniteQueryBehavior()\n    }, notifyOptions);\n  }\n\n  getOptimisticResult(options) {\n    options.behavior = infiniteQueryBehavior();\n    return super.getOptimisticResult(options);\n  }\n\n  fetchNextPage({\n    pageParam,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        fetchMore: {\n          direction: 'forward',\n          pageParam\n        }\n      }\n    });\n  }\n\n  fetchPreviousPage({\n    pageParam,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        fetchMore: {\n          direction: 'backward',\n          pageParam\n        }\n      }\n    });\n  }\n\n  createResult(query, options) {\n    var _state$fetchMeta, _state$fetchMeta$fetc, _state$fetchMeta2, _state$fetchMeta2$fet, _state$data, _state$data2;\n\n    const {\n      state\n    } = query;\n    const result = super.createResult(query, options);\n    const {\n      isFetching,\n      isRefetching\n    } = result;\n    const isFetchingNextPage = isFetching && ((_state$fetchMeta = state.fetchMeta) == null ? void 0 : (_state$fetchMeta$fetc = _state$fetchMeta.fetchMore) == null ? void 0 : _state$fetchMeta$fetc.direction) === 'forward';\n    const isFetchingPreviousPage = isFetching && ((_state$fetchMeta2 = state.fetchMeta) == null ? void 0 : (_state$fetchMeta2$fet = _state$fetchMeta2.fetchMore) == null ? void 0 : _state$fetchMeta2$fet.direction) === 'backward';\n    return { ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, (_state$data = state.data) == null ? void 0 : _state$data.pages),\n      hasPreviousPage: hasPreviousPage(options, (_state$data2 = state.data) == null ? void 0 : _state$data2.pages),\n      isFetchingNextPage,\n      isFetchingPreviousPage,\n      isRefetching: isRefetching && !isFetchingNextPage && !isFetchingPreviousPage\n    };\n  }\n\n}\n\nexport { InfiniteQueryObserver };\n//# sourceMappingURL=infiniteQueryObserver.mjs.map\n", "import { getDefaultState } from './mutation.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { Subscribable } from './subscribable.mjs';\nimport { shallowEqualObjects } from './utils.mjs';\n\n// CLASS\nclass MutationObserver extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.updateResult();\n  }\n\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n\n  setOptions(options) {\n    var _this$currentMutation;\n\n    const prevOptions = this.options;\n    this.options = this.client.defaultMutationOptions(options);\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this\n      });\n    }\n\n    (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.setOptions(this.options);\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$currentMutation2;\n\n      (_this$currentMutation2 = this.currentMutation) == null ? void 0 : _this$currentMutation2.removeObserver(this);\n    }\n  }\n\n  onMutationUpdate(action) {\n    this.updateResult(); // Determine which callbacks to trigger\n\n    const notifyOptions = {\n      listeners: true\n    };\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true;\n    }\n\n    this.notify(notifyOptions);\n  }\n\n  getCurrentResult() {\n    return this.currentResult;\n  }\n\n  reset() {\n    this.currentMutation = undefined;\n    this.updateResult();\n    this.notify({\n      listeners: true\n    });\n  }\n\n  mutate(variables, options) {\n    this.mutateOptions = options;\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this);\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, { ...this.options,\n      variables: typeof variables !== 'undefined' ? variables : this.options.variables\n    });\n    this.currentMutation.addObserver(this);\n    return this.currentMutation.execute();\n  }\n\n  updateResult() {\n    const state = this.currentMutation ? this.currentMutation.state : getDefaultState();\n    const isLoading = state.status === 'loading';\n    const result = { ...state,\n      isLoading,\n      isPending: isLoading,\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset\n    };\n    this.currentResult = result;\n  }\n\n  notify(options) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          var _this$mutateOptions$o, _this$mutateOptions, _this$mutateOptions$o2, _this$mutateOptions2;\n\n          (_this$mutateOptions$o = (_this$mutateOptions = this.mutateOptions).onSuccess) == null ? void 0 : _this$mutateOptions$o.call(_this$mutateOptions, this.currentResult.data, this.currentResult.variables, this.currentResult.context);\n          (_this$mutateOptions$o2 = (_this$mutateOptions2 = this.mutateOptions).onSettled) == null ? void 0 : _this$mutateOptions$o2.call(_this$mutateOptions2, this.currentResult.data, null, this.currentResult.variables, this.currentResult.context);\n        } else if (options.onError) {\n          var _this$mutateOptions$o3, _this$mutateOptions3, _this$mutateOptions$o4, _this$mutateOptions4;\n\n          (_this$mutateOptions$o3 = (_this$mutateOptions3 = this.mutateOptions).onError) == null ? void 0 : _this$mutateOptions$o3.call(_this$mutateOptions3, this.currentResult.error, this.currentResult.variables, this.currentResult.context);\n          (_this$mutateOptions$o4 = (_this$mutateOptions4 = this.mutateOptions).onSettled) == null ? void 0 : _this$mutateOptions$o4.call(_this$mutateOptions4, undefined, this.currentResult.error, this.currentResult.variables, this.currentResult.context);\n        }\n      } // Then trigger the listeners\n\n\n      if (options.listeners) {\n        this.listeners.forEach(({\n          listener\n        }) => {\n          listener(this.currentResult);\n        });\n      }\n    });\n  }\n\n}\n\nexport { MutationObserver };\n//# sourceMappingURL=mutationObserver.mjs.map\n", "// TYPES\n// FUNCTIONS\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state\n  };\n} // Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\n\n\nfunction dehydrateQuery(query) {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash\n  };\n}\n\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === 'success';\n}\nfunction dehydrate(client, options = {}) {\n  const mutations = [];\n  const queries = [];\n\n  if (options.dehydrateMutations !== false) {\n    const shouldDehydrateMutation = options.shouldDehydrateMutation || defaultShouldDehydrateMutation;\n    client.getMutationCache().getAll().forEach(mutation => {\n      if (shouldDehydrateMutation(mutation)) {\n        mutations.push(dehydrateMutation(mutation));\n      }\n    });\n  }\n\n  if (options.dehydrateQueries !== false) {\n    const shouldDehydrateQuery = options.shouldDehydrateQuery || defaultShouldDehydrateQuery;\n    client.getQueryCache().getAll().forEach(query => {\n      if (shouldDehydrateQuery(query)) {\n        queries.push(dehydrateQuery(query));\n      }\n    });\n  }\n\n  return {\n    mutations,\n    queries\n  };\n}\nfunction hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return;\n  }\n\n  const mutationCache = client.getMutationCache();\n  const queryCache = client.getQueryCache(); // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n\n  const mutations = dehydratedState.mutations || []; // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n\n  const queries = dehydratedState.queries || [];\n  mutations.forEach(dehydratedMutation => {\n    var _options$defaultOptio;\n\n    mutationCache.build(client, { ...(options == null ? void 0 : (_options$defaultOptio = options.defaultOptions) == null ? void 0 : _options$defaultOptio.mutations),\n      mutationKey: dehydratedMutation.mutationKey\n    }, dehydratedMutation.state);\n  });\n  queries.forEach(({\n    queryKey,\n    state,\n    queryHash\n  }) => {\n    var _options$defaultOptio2;\n\n    const query = queryCache.get(queryHash); // Do not hydrate if an existing query exists with newer data\n\n    if (query) {\n      if (query.state.dataUpdatedAt < state.dataUpdatedAt) {\n        // omit fetchStatus from dehydrated state\n        // so that query stays in its current fetchStatus\n        const {\n          fetchStatus: _ignored,\n          ...dehydratedQueryState\n        } = state;\n        query.setState(dehydratedQueryState);\n      }\n\n      return;\n    } // Restore query\n\n\n    queryCache.build(client, { ...(options == null ? void 0 : (_options$defaultOptio2 = options.defaultOptions) == null ? void 0 : _options$defaultOptio2.queries),\n      queryKey,\n      queryHash\n    }, // Reset fetch status to idle to avoid\n    // query being stuck in fetching state upon hydration\n    { ...state,\n      fetchStatus: 'idle'\n    });\n  });\n}\n\nexport { defaultShouldDehydrateMutation, defaultShouldDehydrateQuery, dehydrate, hydrate };\n//# sourceMappingURL=hydration.mjs.map\n", "'use client'\nimport * as ReactDOM from 'react-dom'\n\nexport const unstable_batchedUpdates = ReactDOM.unstable_batchedUpdates\n", "import { notifyManager } from '@tanstack/query-core'\nimport { unstable_batchedUpdates } from './reactBatchedUpdates'\n\nnotifyManager.setBatchNotifyFunction(unstable_batchedUpdates)\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var e=require(\"react\");function h(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var k=\"function\"===typeof Object.is?Object.is:h,l=e.useState,m=e.useEffect,n=e.useLayoutEffect,p=e.useDebugValue;function q(a,b){var d=b(),f=l({inst:{value:d,getSnapshot:b}}),c=f[0].inst,g=f[1];n(function(){c.value=d;c.getSnapshot=b;r(c)&&g({inst:c})},[a,d,b]);m(function(){r(c)&&g({inst:c});return a(function(){r(c)&&g({inst:c})})},[a]);p(d);return d}\nfunction r(a){var b=a.getSnapshot;a=a.value;try{var d=b();return!k(a,d)}catch(f){return!0}}function t(a,b){return b()}var u=\"undefined\"===typeof window||\"undefined\"===typeof window.document||\"undefined\"===typeof window.document.createElement?t:q;exports.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:u;\n", "'use client'\n// Temporary workaround due to an issue with react-native uSES - https://github.com/TanStack/query/pull/3601\nimport { useSyncExternalStore as uSES } from 'use-sync-external-store/shim/index.js'\n\nexport const useSyncExternalStore = uSES\n", "'use client'\nimport * as React from 'react'\n\nimport type { QueryClient } from '@tanstack/query-core'\nimport type { ContextOptions } from './types'\n\ndeclare global {\n  interface Window {\n    ReactQueryClientContext?: React.Context<QueryClient | undefined>\n  }\n}\n\nexport const defaultContext = React.createContext<QueryClient | undefined>(\n  undefined,\n)\nconst QueryClientSharingContext = React.createContext<boolean>(false)\n\n// If we are given a context, we will use it.\n// Otherwise, if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\nfunction getQueryClientContext(\n  context: React.Context<QueryClient | undefined> | undefined,\n  contextSharing: boolean,\n) {\n  if (context) {\n    return context\n  }\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext\n    }\n\n    return window.ReactQueryClientContext\n  }\n\n  return defaultContext\n}\n\nexport const useQueryClient = ({ context }: ContextOptions = {}) => {\n  const queryClient = React.useContext(\n    getQueryClientContext(context, React.useContext(QueryClientSharingContext)),\n  )\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return queryClient\n}\n\ntype QueryClientProviderPropsBase = {\n  client: QueryClient\n  children?: React.ReactNode\n}\ntype QueryClientProviderPropsWithContext = ContextOptions & {\n  contextSharing?: never\n} & QueryClientProviderPropsBase\ntype QueryClientProviderPropsWithContextSharing = {\n  context?: never\n  contextSharing?: boolean\n} & QueryClientProviderPropsBase\n\nexport type QueryClientProviderProps =\n  | QueryClientProviderPropsWithContext\n  | QueryClientProviderPropsWithContextSharing\n\nexport const QueryClientProvider = ({\n  client,\n  children,\n  context,\n  contextSharing = false,\n}: QueryClientProviderProps): JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  if (process.env.NODE_ENV !== 'production' && contextSharing) {\n    client\n      .getLogger()\n      .error(\n        `The contextSharing option has been deprecated and will be removed in the next major version`,\n      )\n  }\n\n  const Context = getQueryClientContext(context, contextSharing)\n\n  return (\n    <QueryClientSharingContext.Provider value={!context && contextSharing}>\n      <Context.Provider value={client}>{children}</Context.Provider>\n    </QueryClientSharingContext.Provider>\n  )\n}\n", "'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n", "'use client'\nimport * as React from 'react'\n\n// CONTEXT\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: () => void\n  isReset: () => boolean\n  reset: () => void\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport interface QueryErrorResetBoundaryProps {\n  children:\n    | ((value: QueryErrorResetBoundaryValue) => React.ReactNode)\n    | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function'\n        ? (children as Function)(value)\n        : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n", "export function shouldThrowError<T extends (...args: any[]) => boolean>(\n  _useErrorBoundary: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params)\n  }\n\n  return !!_useErrorBoundary\n}\n", "'use client'\nimport * as React from 'react'\nimport { shouldThrowError } from './utils'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  UseErrorBoundary,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (options.suspense || options.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  useErrorBoundary,\n  query,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  useErrorBoundary: UseErrorBoundary<\n    TQueryFnData,\n    TError,\n    TQueryData,\n    TQueryKey\n  >\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    shouldThrowError(useErrorBoundary, [result.error, query])\n  )\n}\n", "import type { DefaultedQueryObserverOptions } from '@tanstack/query-core'\nimport type { QueryObserver } from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\nimport type { QueryObserverResult } from '@tanstack/query-core'\nimport type { QueryKey } from '@tanstack/query-core'\n\nexport const ensureStaleTime = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => defaultedOptions?.suspense && willFetch(result, isRestoring)\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer\n    .fetchOptimistic(defaultedOptions)\n    .then(({ data }) => {\n      defaultedOptions.onSuccess?.(data as TData)\n      defaultedOptions.onSettled?.(data, null)\n    })\n    .catch((error) => {\n      errorResetBoundary.clearReset()\n      defaultedOptions.onError?.(error)\n      defaultedOptions.onSettled?.(undefined, error)\n    })\n", "'use client'\nimport * as React from 'react'\n\nimport { QueriesObserver, notifyManager } from '@tanstack/query-core'\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useIsRestoring } from './isRestoring'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport {\n  ensureStaleTime,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport type { OmitKeyof, QueryFunction, QueryKey } from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\n\n// This defines the `UseQueryOptions` that are accepted in `QueriesOptions` & `GetOptions`.\n// - `context` is omitted as it is passed as a root-level option to `useQueries` instead.\ntype UseQueryOptionsForUseQueries<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'context'\n>\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryOptionsForUseQueries<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryOptionsForUseQueries<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey> }\n    ? UseQueryOptionsForUseQueries<\n        TQueryFnData,\n        unknown,\n        TQueryFnData,\n        TQueryKey\n      >\n    : // Fallback\n      UseQueryOptionsForUseQueries\n\n// A defined initialData setting should return a DefinedUseQueryResult rather than UseQueryResult\ntype GetDefinedOrUndefinedQueryResult<T, TData, TError = unknown> = T extends {\n  initialData?: infer TInitialData\n}\n  ? unknown extends TInitialData\n    ? UseQueryResult<TData, TError>\n    : TInitialData extends TData\n    ? DefinedUseQueryResult<TData, TError>\n    : TInitialData extends () => infer TInitialDataResult\n    ? unknown extends TInitialDataResult\n      ? UseQueryResult<TData, TError>\n      : TInitialDataResult extends TData\n      ? DefinedUseQueryResult<TData, TError>\n      : UseQueryResult<TData, TError>\n    : UseQueryResult<TData, TError>\n  : UseQueryResult<TData, TError>\n\ntype GetUseQueryResult<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<unknown, any>\n        select: (data: any) => infer TData\n      }\n    ? GetDefinedOrUndefinedQueryResult<T, TData>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, any> }\n    ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData>\n    : // Fallback\n      UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends any[],\n  TResult extends any[] = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryOptionsForUseQueries[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...TResult, GetOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesOptions<[...Tail], [...TResult, GetOptions<Head>], [...TDepth, 1]>\n  : unknown[] extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends UseQueryOptionsForUseQueries<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      infer TQueryKey\n    >[]\n  ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData, TQueryKey>[]\n  : // Fallback\n    UseQueryOptionsForUseQueries[]\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends any[],\n  TResults extends any[] = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryResult[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...TResults, GetUseQueryResult<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesResults<\n      [...Tail],\n      [...TResults, GetUseQueryResult<Head>],\n      [...TDepth, 1]\n    >\n  : T extends UseQueryOptionsForUseQueries<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      any\n    >[]\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    UseQueryResult<unknown extends TData ? TQueryFnData : TData, TError>[]\n  : // Fallback\n    UseQueryResult[]\n\nexport function useQueries<T extends any[]>({\n  queries,\n  context,\n}: {\n  queries: readonly [...QueriesOptions<T>]\n  context?: UseQueryOptions['context']\n}): QueriesResults<T> {\n  const queryClient = useQueryClient({ context })\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n\n  const defaultedQueries = React.useMemo(\n    () =>\n      queries.map((options) => {\n        const defaultedOptions = queryClient.defaultQueryOptions(options)\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions._optimisticResults = isRestoring\n          ? 'isRestoring'\n          : 'optimistic'\n\n        return defaultedOptions\n      }),\n    [queries, queryClient, isRestoring],\n  )\n\n  defaultedQueries.forEach((query) => {\n    ensureStaleTime(query)\n    ensurePreventErrorBoundaryRetry(query, errorResetBoundary)\n  })\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () => new QueriesObserver(queryClient, defaultedQueries),\n  )\n\n  const optimisticResult = observer.getOptimisticResult(defaultedQueries)\n\n  useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        isRestoring\n          ? () => undefined\n          : observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer, isRestoring],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, { listeners: false })\n  }, [defaultedQueries, observer])\n\n  const shouldAtLeastOneSuspend = optimisticResult.some((result, index) =>\n    shouldSuspend(defaultedQueries[index], result, isRestoring),\n  )\n\n  const suspensePromises = shouldAtLeastOneSuspend\n    ? optimisticResult.flatMap((result, index) => {\n        const options = defaultedQueries[index]\n        const queryObserver = observer.getObservers()[index]\n\n        if (options && queryObserver) {\n          if (shouldSuspend(options, result, isRestoring)) {\n            return fetchOptimistic(options, queryObserver, errorResetBoundary)\n          } else if (willFetch(result, isRestoring)) {\n            void fetchOptimistic(options, queryObserver, errorResetBoundary)\n          }\n        }\n        return []\n      })\n    : []\n\n  if (suspensePromises.length > 0) {\n    throw Promise.all(suspensePromises)\n  }\n  const observerQueries = observer.getQueries()\n  const firstSingleResultWhichShouldThrow = optimisticResult.find(\n    (result, index) =>\n      getHasError({\n        result,\n        errorResetBoundary,\n        useErrorBoundary: defaultedQueries[index]?.useErrorBoundary ?? false,\n        query: observerQueries[index]!,\n      }),\n  )\n\n  if (firstSingleResultWhichShouldThrow?.error) {\n    throw firstSingleResultWhichShouldThrow.error\n  }\n\n  return optimisticResult as QueriesResults<T>\n}\n", "'use client'\nimport * as React from 'react'\n\nimport { notifyManager } from '@tanstack/query-core'\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useIsRestoring } from './isRestoring'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { ensureStaleTime, fetchOptimistic, shouldSuspend } from './suspense'\nimport type { QueryKey, QueryObserver } from '@tanstack/query-core'\nimport type { UseBaseQueryOptions } from './types'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryK<PERSON>\n  >,\n  Observer: typeof QueryObserver,\n) {\n  const queryClient = useQueryClient({ context: options.context })\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const defaultedOptions = queryClient.defaultQueryOptions(options)\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  // Include callbacks in batch renders\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(\n      defaultedOptions.onError,\n    )\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(\n      defaultedOptions.onSuccess,\n    )\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(\n      defaultedOptions.onSettled,\n    )\n  }\n\n  ensureStaleTime(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        queryClient,\n        defaultedOptions,\n      ),\n  )\n\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = isRestoring\n          ? () => undefined\n          : observer.subscribe(notifyManager.batchCalls(onStoreChange))\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, isRestoring],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, { listeners: false })\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result, isRestoring)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      useErrorBoundary: defaultedOptions.useErrorBoundary,\n      query: observer.getCurrentQuery(),\n    })\n  ) {\n    throw result.error\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n", "'use client'\nimport * as React from 'react'\n\nimport { hydrate } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { HydrateOptions } from '@tanstack/query-core'\nimport type { ContextOptions } from './types'\n\nexport function useHydrate(\n  state: unknown,\n  options: HydrateOptions & ContextOptions = {},\n) {\n  const queryClient = useQueryClient({ context: options.context })\n\n  const optionsRef = React.useRef(options)\n  optionsRef.current = options\n\n  // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n  React.useMemo(() => {\n    if (state) {\n      hydrate(queryClient, state, optionsRef.current)\n    }\n  }, [queryClient, state])\n}\n\nexport interface HydrateProps {\n  state?: unknown\n  options?: HydrateOptions\n  children?: React.ReactNode\n}\n\nexport const Hydrate = ({ children, options, state }: HydrateProps) => {\n  useHydrate(state, options)\n  return children as React.ReactElement\n}\n", "'use client'\nimport * as React from 'react'\nimport {\n  MutationObserver,\n  notifyManager,\n  parseMutationArgs,\n} from '@tanstack/query-core'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport { useQueryClient } from './QueryClientProvider'\nimport { shouldThrowError } from './utils'\nimport type { MutationFunction, MutationKey } from '@tanstack/query-core'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationFn: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationFn'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MutationKey,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MutationKey,\n  mutationFn?: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey' | 'mutationFn'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  arg1:\n    | MutationKey\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg2?:\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg3?: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const options = parseMutationArgs(arg1, arg2, arg3)\n  const queryClient = useQueryClient({ context: options.context })\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        queryClient,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.useErrorBoundary, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n", "import { parseFilter<PERSON>rgs, parseQueryArgs, functionalUpdate, noop, hashQ<PERSON>y<PERSON><PERSON>, partialMatch<PERSON>ey, hashQueryKeyByOptions } from './utils.mjs';\nimport { QueryCache } from './queryCache.mjs';\nimport { MutationCache } from './mutationCache.mjs';\nimport { focusManager } from './focusManager.mjs';\nimport { onlineManager } from './onlineManager.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior.mjs';\nimport { defaultLogger } from './logger.mjs';\n\n// CLASS\nclass QueryClient {\n  constructor(config = {}) {\n    this.queryCache = config.queryCache || new QueryCache();\n    this.mutationCache = config.mutationCache || new MutationCache();\n    this.logger = config.logger || defaultLogger;\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n    this.mountCount = 0;\n\n    if (process.env.NODE_ENV !== 'production' && config.logger) {\n      this.logger.error(\"Passing a custom logger has been deprecated and will be removed in the next major version.\");\n    }\n  }\n\n  mount() {\n    this.mountCount++;\n    if (this.mountCount !== 1) return;\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused()) {\n        this.resumePausedMutations();\n        this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (onlineManager.isOnline()) {\n        this.resumePausedMutations();\n        this.queryCache.onOnline();\n      }\n    });\n  }\n\n  unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    this.mountCount--;\n    if (this.mountCount !== 0) return;\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    this.unsubscribeFocus = undefined;\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n    this.unsubscribeOnline = undefined;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n    filters.fetchStatus = 'fetching';\n    return this.queryCache.findAll(filters).length;\n  }\n\n  isMutating(filters) {\n    return this.mutationCache.findAll({ ...filters,\n      fetching: true\n    }).length;\n  }\n\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData(arg1, arg2, arg3) {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    const cachedData = this.getQueryData(parsedOptions.queryKey);\n    return cachedData ? Promise.resolve(cachedData) : this.fetchQuery(parsedOptions);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(({\n      queryKey,\n      state\n    }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n\n  setQueryData(queryKey, updater, options) {\n    const query = this.queryCache.find(queryKey);\n    const prevData = query == null ? void 0 : query.state.data;\n    const data = functionalUpdate(updater, prevData);\n\n    if (typeof data === 'undefined') {\n      return undefined;\n    }\n\n    const parsedOptions = parseQueryArgs(queryKey);\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(data, { ...options,\n      manual: true\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData(queryKeyOrFilters, updater, options) {\n    return notifyManager.batch(() => this.getQueryCache().findAll(queryKeyOrFilters).map(({\n      queryKey\n    }) => [queryKey, this.setQueryData(queryKey, updater, options)]));\n  }\n\n  getQueryState(queryKey,\n  /**\n   * @deprecated This filters will be removed in the next major version.\n   */\n  filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n    const queryCache = this.queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        queryCache.remove(query);\n      });\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(arg1, arg2, arg3) {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);\n    const queryCache = this.queryCache;\n    const refetchFilters = {\n      type: 'active',\n      ...filters\n    };\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        query.reset();\n      });\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(arg1, arg2, arg3) {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3);\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    const promises = notifyManager.batch(() => this.queryCache.findAll(filters).map(query => query.cancel(cancelOptions)));\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(arg1, arg2, arg3) {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);\n    return notifyManager.batch(() => {\n      var _ref, _filters$refetchType;\n\n      this.queryCache.findAll(filters).forEach(query => {\n        query.invalidate();\n      });\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve();\n      }\n\n      const refetchFilters = { ...filters,\n        type: (_ref = (_filters$refetchType = filters.refetchType) != null ? _filters$refetchType : filters.type) != null ? _ref : 'active'\n      };\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(arg1, arg2, arg3) {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);\n    const promises = notifyManager.batch(() => this.queryCache.findAll(filters).filter(query => !query.isDisabled()).map(query => {\n      var _options$cancelRefetc;\n\n      return query.fetch(undefined, { ...options,\n        cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n        meta: {\n          refetchPage: filters.refetchPage\n        }\n      });\n    }));\n    let promise = Promise.all(promises).then(noop);\n\n    if (!(options != null && options.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery(arg1, arg2, arg3) {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery(arg1, arg2, arg3) {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    parsedOptions.behavior = infiniteQueryBehavior();\n    return this.fetchQuery(parsedOptions);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  }\n\n  resumePausedMutations() {\n    return this.mutationCache.resumePausedMutations();\n  }\n\n  getQueryCache() {\n    return this.queryCache;\n  }\n\n  getMutationCache() {\n    return this.mutationCache;\n  }\n\n  getLogger() {\n    return this.logger;\n  }\n\n  getDefaultOptions() {\n    return this.defaultOptions;\n  }\n\n  setDefaultOptions(options) {\n    this.defaultOptions = options;\n  }\n\n  setQueryDefaults(queryKey, options) {\n    const result = this.queryDefaults.find(x => hashQueryKey(queryKey) === hashQueryKey(x.queryKey));\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey,\n        defaultOptions: options\n      });\n    }\n  }\n\n  getQueryDefaults(queryKey) {\n    if (!queryKey) {\n      return undefined;\n    } // Get the first matching defaults\n\n\n    const firstMatchingDefaults = this.queryDefaults.find(x => partialMatchKey(queryKey, x.queryKey)); // Additional checks and error in dev mode\n\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter(x => partialMatchKey(queryKey, x.queryKey)); // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\"[QueryClient] Several query defaults match with key '\" + JSON.stringify(queryKey) + \"'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.\");\n      }\n    }\n\n    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;\n  }\n\n  setMutationDefaults(mutationKey, options) {\n    const result = this.mutationDefaults.find(x => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey));\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey,\n        defaultOptions: options\n      });\n    }\n  }\n\n  getMutationDefaults(mutationKey) {\n    if (!mutationKey) {\n      return undefined;\n    } // Get the first matching defaults\n\n\n    const firstMatchingDefaults = this.mutationDefaults.find(x => partialMatchKey(mutationKey, x.mutationKey)); // Additional checks and error in dev mode\n\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter(x => partialMatchKey(mutationKey, x.mutationKey)); // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\"[QueryClient] Several mutation defaults match with key '\" + JSON.stringify(mutationKey) + \"'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.\");\n      }\n    }\n\n    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;\n  }\n\n  defaultQueryOptions(options) {\n    if (options != null && options._defaulted) {\n      return options;\n    }\n\n    const defaultedOptions = { ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options == null ? void 0 : options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(defaultedOptions.queryKey, defaultedOptions);\n    } // dependent default values\n\n\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== 'always';\n    }\n\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense;\n    }\n\n    return defaultedOptions;\n  }\n\n  defaultMutationOptions(options) {\n    if (options != null && options._defaulted) {\n      return options;\n    }\n\n    return { ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options == null ? void 0 : options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n\n  clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  }\n\n}\n\nexport { QueryClient };\n//# sourceMappingURL=queryClient.mjs.map\n", "import type {\n  InitialDataFunction,\n  NonUndefinedGuard,\n  OmitKeyof,\n  Query<PERSON><PERSON>,\n  WithRequired,\n} from '@tanstack/query-core'\nimport type { UseQueryOptions } from './types'\n\ntype UseQueryOptionsOmitted<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  T<PERSON>ueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'onSuccess' | 'onError' | 'onSettled' | 'refetchInterval'\n>\n\ntype ProhibitedQueryOptionsKeyInV5 = keyof Pick<\n  UseQueryOptionsOmitted,\n  'useErrorBoundary' | 'suspense' | 'getNextPageParam' | 'getPreviousPageParam'\n>\n\nexport type UndefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  T<PERSON><PERSON>y<PERSON>ey extends QueryKey = QueryKey,\n> = UseQueryOptionsOmitted<TQueryFnData, TError, TData, TQueryKey> & {\n  initialData?:\n    | undefined\n    | InitialDataFunction<NonUndefinedGuard<TQueryFnData>>\n    | NonUndefinedGuard<TQueryFnData>\n}\n\nexport type DefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = UseQueryOptionsOmitted<TQueryFnData, TError, TData, TQueryKey> & {\n  initialData:\n    | NonUndefinedGuard<TQueryFnData>\n    | (() => NonUndefinedGuard<TQueryFnData>)\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: WithRequired<\n    OmitKeyof<\n      DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n      ProhibitedQueryOptionsKeyInV5\n    >,\n    'queryKey'\n  >,\n): WithRequired<\n  DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'queryKey'\n>\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: WithRequired<\n    OmitKeyof<\n      UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n      ProhibitedQueryOptionsKeyInV5\n    >,\n    'queryKey'\n  >,\n): WithRequired<\n  UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'queryKey'\n>\n\nexport function queryOptions(options: unknown) {\n  return options\n}\n", "'use client'\nimport { InfiniteQueryObserver, parseQueryArgs } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  QueryFunction,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type { UseInfiniteQueryOptions, UseInfiniteQueryResult } from './types'\n\n// HOOK\n\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  T<PERSON>uer<PERSON><PERSON><PERSON> extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey'\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey' | 'queryFn'\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1:\n    | TQueryKey\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg3?: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >,\n): UseInfiniteQueryResult<TData, TError> {\n  const options = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(\n    options,\n    InfiniteQueryObserver as typeof QueryObserver,\n  ) as UseInfiniteQueryResult<TData, TError>\n}\n", "'use client'\nimport * as React from 'react'\nimport { notifyManager, parseFilterArgs } from '@tanstack/query-core'\n\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { ContextOptions } from './types'\nimport type { QueryFilters, QueryKey } from '@tanstack/query-core'\n\ninterface Options extends ContextOptions {}\n\nexport function useIsFetching(filters?: QueryFilters, options?: Options): number\nexport function useIsFetching(\n  queryKey?: QueryKey,\n  filters?: QueryFilters,\n  options?: Options,\n): number\nexport function useIsFetching(\n  arg1?: QueryKey | QueryFilters,\n  arg2?: QueryFilters | Options,\n  arg3?: Options,\n): number {\n  const [filters, options = {}] = parseFilterArgs(arg1, arg2, arg3)\n  const queryClient = useQueryClient({ context: options.context })\n  const queryCache = queryClient.getQueryCache()\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        queryCache.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [queryCache],\n    ),\n    () => queryClient.isFetching(filters),\n    () => queryClient.isFetching(filters),\n  )\n}\n", "'use client'\nimport * as React from 'react'\nimport { notify<PERSON>anager, parseMutationFilterArgs } from '@tanstack/query-core'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport { useQueryClient } from './QueryClientProvider'\nimport type { MutationFilters, MutationKey } from '@tanstack/query-core'\nimport type { ContextOptions } from './types'\n\ninterface Options extends ContextOptions {}\n\nexport function useIsMutating(\n  filters?: MutationFilters,\n  options?: Options,\n): number\nexport function useIsMutating(\n  mutationKey?: MutationKey,\n  filters?: Omit<MutationFilters, 'mutationKey'>,\n  options?: Options,\n): number\nexport function useIsMutating(\n  arg1?: MutationKey | MutationFilters,\n  arg2?: Omit<MutationFilters, 'mutationKey'> | Options,\n  arg3?: Options,\n): number {\n  const [filters, options = {}] = parseMutationFilterArgs(arg1, arg2, arg3)\n\n  const queryClient = useQueryClient({ context: options.context })\n  const mutationCache = queryClient.getMutationCache()\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        mutationCache.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [mutationCache],\n    ),\n    () => queryClient.isMutating(filters),\n    () => queryClient.isMutating(filters),\n  )\n}\n", "'use client'\nimport { QueryObserver, parseQueryArgs } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  InitialDataFunction,\n  NonUndefinedGuard,\n  OmitKeyof,\n  QueryFunction,\n  QueryKey,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\n\n// HOOK\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & {\n    initialData:\n      | NonUndefinedGuard<TQueryFnData>\n      | (() => NonUndefinedGuard<TQueryFnData>)\n  },\n): DefinedUseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & {\n    initialData?:\n      | undefined\n      | InitialDataFunction<NonUndefinedGuard<TQueryFnData>>\n      | NonUndefinedGuard<TQueryFnData>\n  },\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError>\n\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  >,\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey'\n  >,\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData?: () => undefined },\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn'\n  >,\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg3?: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError> {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(parsedOptions, QueryObserver)\n}\n", "import { useQueries } from './useQueries'\nimport type {\n  UseQueryOptions,\n  UseSuspenseQueryOptions,\n  UseSuspenseQueryResult,\n} from './types'\nimport type { NetworkMode, QueryFunction } from '@tanstack/query-core'\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetSuspenseOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseSuspenseQueryOptions<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseSuspenseQueryOptions<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseSuspenseQueryOptions<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseSuspenseQueryOptions<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select?: (data: any) => infer TData\n      }\n    ? UseSuspenseQueryOptions<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n      }\n    ? UseSuspenseQueryOptions<TQueryFnData, unknown, TQueryFnData, TQueryKey>\n    : // Fallback\n      UseSuspenseQueryOptions\n\ntype GetSuspenseResults<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseSuspenseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseSuspenseQueryResult<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseSuspenseQueryResult<TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? UseSuspenseQueryResult<TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseSuspenseQueryResult<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseSuspenseQueryResult<TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, any>\n        select?: (data: any) => infer TData\n      }\n    ? UseSuspenseQueryResult<unknown extends TData ? TQueryFnData : TData>\n    : T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, any>\n      }\n    ? UseSuspenseQueryResult<TQueryFnData>\n    : // Fallback\n      UseSuspenseQueryResult\n\n/**\n * SuspenseQueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type SuspenseQueriesOptions<\n  T extends Array<any>,\n  TResult extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryOptions>\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...TResult, GetSuspenseOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? SuspenseQueriesOptions<\n      [...Tail],\n      [...TResult, GetSuspenseOptions<Head>],\n      [...TDepth, 1]\n    >\n  : Array<unknown> extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends Array<\n      UseSuspenseQueryOptions<\n        infer TQueryFnData,\n        infer TError,\n        infer TData,\n        infer TQueryKey\n      >\n    >\n  ? Array<UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>>\n  : // Fallback\n    Array<UseSuspenseQueryOptions>\n\n/**\n * SuspenseQueriesResults reducer recursively maps type param to results\n */\nexport type SuspenseQueriesResults<\n  T extends Array<any>,\n  TResult extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryResult>\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...TResult, GetSuspenseResults<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? SuspenseQueriesResults<\n      [...Tail],\n      [...TResult, GetSuspenseResults<Head>],\n      [...TDepth, 1]\n    >\n  : T extends Array<\n      UseSuspenseQueryOptions<\n        infer TQueryFnData,\n        infer TError,\n        infer TData,\n        any\n      >\n    >\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    Array<\n      UseSuspenseQueryResult<\n        unknown extends TData ? TQueryFnData : TData,\n        TError\n      >\n    >\n  : // Fallback\n    Array<UseSuspenseQueryResult>\n\nexport function useSuspenseQueries<T extends any[]>({\n  queries,\n  context,\n}: {\n  queries: readonly [...SuspenseQueriesOptions<T>]\n  context?: UseQueryOptions['context']\n}): SuspenseQueriesResults<T> {\n  return useQueries({\n    queries: queries.map((query) => ({\n      ...query,\n      enabled: true,\n      useErrorBoundary: true,\n      suspense: true,\n      placeholderData: undefined,\n      networkMode: 'always' as NetworkMode,\n    })),\n    context,\n  }) as SuspenseQueriesResults<T>\n}\n", "import { QueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type { QueryKey } from '@tanstack/query-core'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\n\nexport function useSuspenseQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(options: UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>) {\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      useErrorBoundary: true,\n      suspense: true,\n      placeholderData: undefined,\n      networkMode: 'always',\n      onSuccess: undefined,\n      onError: undefined,\n      onSettled: undefined,\n    },\n    QueryObserver,\n  ) as UseSuspenseQueryResult<TData, TError>\n}\n"], "names": ["Subscribable", "constructor", "this", "listeners", "Set", "subscribe", "bind", "listener", "identity", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size", "isServer", "window", "noop", "isValidTimeout", "value", "Infinity", "difference", "array1", "array2", "filter", "x", "includes", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "queryFn", "parseMutationArgs", "<PERSON><PERSON><PERSON>", "mutationFn", "parseFilter<PERSON><PERSON>s", "parseMutationFilterArgs", "matchQuery", "filters", "query", "type", "exact", "fetchStatus", "predicate", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "isActive", "isStale", "state", "matchMutation", "mutation", "fetching", "hashQuery<PERSON>ey", "status", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "Object", "keys", "sort", "reduce", "result", "key", "a", "b", "partialDeepEqual", "some", "replaceEqualDeep", "array", "is<PERSON><PERSON>A<PERSON>y", "aSize", "length", "bItems", "bSize", "copy", "equalItems", "i", "shallowEqualObjects", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "prot", "prototype", "hasOwnProperty", "toString", "call", "sleep", "timeout", "Promise", "resolve", "setTimeout", "scheduleMicrotask", "callback", "then", "replaceData", "prevData", "data", "isDataEqual", "structuralSharing", "focusManager", "super", "setup", "onFocus", "addEventListener", "removeEventListener", "cleanup", "setEventListener", "_this$cleanup", "undefined", "_this$cleanup2", "focused", "setFocused", "for<PERSON>ach", "isFocused", "document", "visibilityState", "onlineEvents", "onlineManager", "onOnline", "event", "online", "setOnline", "isOnline", "navigator", "onLine", "defaultRetryDelay", "failureCount", "min", "canFetch", "networkMode", "CancelledError", "revert", "silent", "isCancelledError", "createRetryer", "config", "continueFn", "promiseResolve", "promiseReject", "isRetryCancelled", "isResolved", "promise", "outerResolve", "outerReject", "shouldP<PERSON>e", "onSuccess", "reject", "onError", "pause", "continueResolve", "canContinue", "onPause", "onContinue", "run", "promiseOrValue", "fn", "error", "catch", "_config$retry", "_config$retryDelay", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "cancel", "cancelOptions", "abort", "continue", "cancelRetry", "continueRetry", "defaultLogger", "console", "notify<PERSON><PERSON>ger", "queue", "transactions", "notifyFn", "batchNotifyFn", "schedule", "push", "flush", "originalQueue", "batch", "batchCalls", "args", "setNotifyFunction", "setBatchNotifyFunction", "createNotifyManager", "Removable", "destroy", "clearGcTimeout", "scheduleGc", "cacheTime", "gcTimeout", "optionalRemove", "updateCacheTime", "newCacheTime", "clearTimeout", "Query", "abortSignalConsumed", "defaultOptions", "setOptions", "observers", "cache", "logger", "initialState", "initialData", "hasData", "initialDataUpdatedAt", "dataUpdateCount", "dataUpdatedAt", "errorUpdateCount", "errorUpdatedAt", "fetchFailureCount", "fetchFailureReason", "fetchMeta", "isInvalidated", "getDefaultState", "meta", "remove", "setData", "newData", "dispatch", "manual", "setState", "setStateOptions", "_this$retryer", "retryer", "reset", "observer", "enabled", "isDisabled", "getObserversCount", "getCurrentResult", "isStaleByTime", "_this$retryer2", "find", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "_this$retryer3", "shouldFetchOnReconnect", "addObserver", "notify", "removeObserver", "invalidate", "fetch", "fetchOptions", "_this$options$behavio", "_context$fetchOptions", "_this$retryer4", "abortController", "AbortController", "getAbortController", "queryFnContext", "pageParam", "addSignalProperty", "object", "defineProperty", "enumerable", "get", "signal", "context", "fetchFn", "_context$fetchOptions2", "behavior", "onFetch", "revertState", "_this$cache$config$on", "_this$cache$config", "_this$cache$config$on2", "_this$cache$config2", "onSettled", "isFetchingOptimistic", "_this$cache$config$on3", "_this$cache$config3", "_this$cache$config$on4", "_this$cache$config4", "Error", "action", "_action$meta", "_action$dataUpdatedAt", "reducer", "onQueryUpdate", "Query<PERSON>ache", "queries", "queriesMap", "build", "client", "_options$queryHash", "<PERSON><PERSON><PERSON><PERSON>", "defaultQueryOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "queryInMap", "clear", "getAll", "findAll", "Mutation", "mutationId", "mutationCache", "_this$retryer$continu", "execute", "async", "executeMutation", "_this$options$retry", "variables", "restored", "_this$mutationCache$c3", "_this$mutationCache$c4", "_this$options$onSucce", "_this$options2", "_this$mutationCache$c5", "_this$mutationCache$c6", "_this$options$onSettl", "_this$options3", "_this$mutationCache$c", "_this$mutationCache$c2", "_this$options$onMutat", "_this$options", "onMutate", "_this$mutationCache$c7", "_this$mutationCache$c8", "_this$options$onError", "_this$options4", "_this$mutationCache$c9", "_this$mutationCache$c10", "_this$options$onSettl2", "_this$options5", "failureReason", "isPaused", "onMutationUpdate", "MutationCache", "mutations", "defaultMutationOptions", "getMutationDefaults", "resumePausedMutations", "_this$resuming", "resuming", "pausedMutations", "infiniteQueryBehavior", "_context$fetchOptions3", "_context$fetchOptions4", "_context$state$data", "_context$state$data2", "refetchPage", "fetchMore", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "pages", "oldPageParams", "pageParams", "newPageParams", "cancelled", "buildNewPages", "param", "page", "previous", "fetchPage", "_context$signal", "_context$signal2", "aborted", "queryFnResult", "getNextPageParam", "getPreviousPageParam", "hasNextPage", "nextPageParam", "hasPreviousPage", "previousPageParam", "QueryObserver", "trackedProps", "selectError", "bindMethods", "<PERSON><PERSON><PERSON><PERSON>", "shouldFetchOnMount", "executeFetch", "updateTimers", "shouldFetchOn", "refetchOnReconnect", "refetchOnWindowFocus", "clearStaleTimeout", "clearRefetchInterval", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON>ache", "updateQuery", "mounted", "shouldFetchOptionally", "updateResult", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "createResult", "optimisticResult", "keepPreviousData", "placeholderData", "isPlaceholderData", "shouldAssignObserverCurrentProperties", "currentResult", "currentResultOptions", "currentResultState", "trackResult", "trackedResult", "configurable", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchOptimistic", "defaultedOptions", "_fetchOptions$cancelR", "throwOnError", "staleTimeoutId", "_this$options$refetch", "refetchInterval", "nextInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "clearInterval", "prevResult", "prevResultState", "prevResultOptions", "query<PERSON>hange", "queryInitialState", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "isPreviousData", "_optimisticResults", "fetchOnMount", "fetchOptionally", "isSuccess", "select", "selectFn", "selectResult", "isFetching", "isLoading", "isError", "isInitialLoading", "isFetched", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isRefetchError", "nextResult", "defaultNotifyOptions", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "useErrorBoundary", "<PERSON><PERSON><PERSON>", "has", "shouldNotifyListeners", "retryOnMount", "shouldLoadOnMount", "refetchOnMount", "field", "suspense", "QueriesObserver", "observersMap", "setQueries", "onUpdate", "prevObservers", "newObserverMatches", "findMatchingObservers", "match", "defaultedQueryOptions", "newObservers", "map", "newObserversMap", "fromEntries", "newResult", "hasIndexChange", "index", "getQueries", "getObservers", "prevObserversMap", "Map", "matchingObservers", "flatMap", "matchedQueryHashes", "unmatchedQueries", "matchingObserversSet", "unmatchedObservers", "prevObserver", "getObserver", "currentObserver", "newOrReusedObservers", "previouslyUsedObserver", "concat", "indexOf", "slice", "replaceAt", "InfiniteQueryObserver", "fetchNextPage", "fetchPreviousPage", "_state$fetchMeta", "_state$fetchMeta$fetc", "_state$fetchMeta2", "_state$fetchMeta2$fet", "_state$data", "_state$data2", "MutationObserver", "mutate", "_this$currentMutation", "getMutationCache", "currentMutation", "_this$currentMutation2", "mutateOptions", "isPending", "isIdle", "_this$mutateOptions$o", "_this$mutateOptions", "_this$mutateOptions$o2", "_this$mutateOptions2", "_this$mutateOptions$o3", "_this$mutateOptions3", "_this$mutateOptions$o4", "_this$mutateOptions4", "defaultShouldDehydrateMutation", "defaultShouldDehydrateQuery", "hydrate", "dehydratedState", "queryCache", "dehydratedMutation", "_options$defaultOptio", "_options$defaultOptio2", "_ignored", "dehydratedQueryState", "unstable_batchedUpdates", "ReactDOM", "module", "exports", "e", "require$$0", "k", "is", "l", "useState", "m", "useEffect", "n", "useLayoutEffect", "p", "useDebugValue", "r", "getSnapshot", "d", "f", "u", "createElement", "inst", "c", "g", "useSyncExternalStoreShim_production_min", "useSyncExternalStore", "uSES", "defaultContext", "React", "createContext", "QueryClientSharingContext", "getQueryClientContext", "contextSharing", "ReactQueryClientContext", "useQueryClient", "queryClient", "useContext", "IsRestoringContext", "useIsRestoring", "IsRestoringProvider", "Provider", "createValue", "isReset", "clear<PERSON><PERSON>t", "QueryErrorResetBoundaryContext", "useQueryErrorResetBoundary", "shouldThrowError", "_useErrorBoundary", "params", "ensurePreventErrorBoundaryRetry", "errorResetBoundary", "useClearResetErrorBoundary", "getHasError", "ensureStaleTime", "<PERSON><PERSON><PERSON><PERSON>", "isRestoring", "shouldSuspend", "useQueries", "defaultedQueries", "useMemo", "useCallback", "onStoreChange", "suspensePromises", "queryObserver", "all", "observerQueries", "firstSingleResultWhichShouldThrow", "_defaultedQueries$ind", "_defaultedQueries$ind2", "useBaseQuery", "Observer", "unsubscribe", "useHydrate", "optionsRef", "useRef", "current", "children", "queryDefaults", "mutationDefaults", "mountCount", "mount", "unsubscribeFocus", "unsubscribeOnline", "unmount", "_this$unsubscribeFocu", "_this$unsubscribeOnli", "isMutating", "getQueryData", "_this$queryCache$find", "ensureQueryData", "parsedOptions", "cachedData", "<PERSON><PERSON><PERSON><PERSON>", "getQueriesData", "query<PERSON>eyOrFilters", "setQueryData", "updater", "input", "functionalUpdate", "setQueriesData", "getQueryState", "_this$queryCache$find2", "removeQueries", "resetQueries", "refetchFilters", "refetchQueries", "cancelQueries", "promises", "invalidateQueries", "_ref", "_filters$refetchType", "refetchType", "_options$cancelRefetc", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "prefetchInfiniteQuery", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstMatchingDefaults", "setMutationDefaults", "_defaulted", "Context", "dehydrateMutations", "shouldDehydrateMutation", "dehydrateMutation", "dehydrateQueries", "shouldDehydrateQuery", "dehydrate<PERSON><PERSON>y", "mutateAsync"], "mappings": "msBAAA,MAAMA,EACJC,cACEC,KAAKC,UAAY,IAAIC,IACrBF,KAAKG,UAAYH,KAAKG,UAAUC,KAAKJ,MAGvCG,UAAUE,GACR,MAAMC,EAAW,CACfD,YAIF,OAFAL,KAAKC,UAAUM,IAAID,GACnBN,KAAKQ,cACE,KACLR,KAAKC,UAAUQ,OAAOH,GACtBN,KAAKU,iBAITC,eACE,OAAOX,KAAKC,UAAUW,KAAO,EAG/BJ,eAGAE,kBCvBI,MAAAG,EAA6B,oBAAXC,QAA0B,SAAUA,OAC5D,SAASC,KAMT,SAASC,EAAeC,GACtB,MAAwB,iBAAVA,GAAsBA,GAAS,GAAKA,IAAUC,IAE9D,SAASC,EAAWC,EAAQC,GAC1B,OAAOD,EAAOE,QAAOC,IAAMF,EAAOG,SAASD,KAO7C,SAASE,EAAeC,EAAWC,GACjC,OAAOC,KAAKC,IAAIH,GAAaC,GAAa,GAAKG,KAAKC,MAAO,GAE7D,SAASC,EAAeC,EAAMC,EAAMC,GAClC,OAAKC,EAAWH,GAII,mBAATC,EACF,IAAKC,EACVE,SAAUJ,EACVK,QAASJ,GAIN,IAAKA,EACVG,SAAUJ,GAXHA,EAcX,SAASM,EAAkBN,EAAMC,EAAMC,GACrC,OAAIC,EAAWH,GACO,mBAATC,EACF,IAAKC,EACVK,YAAaP,EACbQ,WAAYP,GAIT,IAAKA,EACVM,YAAaP,GAIG,mBAATA,EACF,IAAKC,EACVO,WAAYR,GAIT,IAAKA,GAGd,SAASS,EAAgBT,EAAMC,EAAMC,GACnC,OAAOC,EAAWH,GAAQ,CAAC,IAAKC,EAC9BG,SAAUJ,GACTE,GAAQ,CAACF,GAAQ,GAAIC,GAE1B,SAASS,EAAwBV,EAAMC,EAAMC,GAC3C,OAAOC,EAAWH,GAAQ,CAAC,IAAKC,EAC9BM,YAAaP,GACZE,GAAQ,CAACF,GAAQ,GAAIC,GAE1B,SAASU,EAAWC,EAASC,GAC3B,MAAMC,KACJA,EAAO,MAAKC,MACZA,EAAKC,YACLA,EAAWC,UACXA,EAASb,SACTA,EAAQc,MACRA,GACEN,EAEJ,GAAIT,EAAWC,GACb,GAAIW,GACF,GAAIF,EAAMM,YAAcC,EAAsBhB,EAAUS,EAAMQ,SAC5D,OAAO,OAEJ,IAAKC,EAAgBT,EAAMT,SAAUA,GAC1C,OAAO,EAIX,GAAa,QAATU,EAAgB,CAClB,MAAMS,EAAWV,EAAMU,WAEvB,GAAa,WAATT,IAAsBS,EACxB,OAAO,EAGT,GAAa,aAATT,GAAuBS,EACzB,OAAO,EAIX,OAAqB,kBAAVL,GAAuBL,EAAMW,YAAcN,WAI3B,IAAhBF,GAA+BA,IAAgBH,EAAMY,MAAMT,gBAIlEC,IAAcA,EAAUJ,KAM9B,SAASa,EAAcd,EAASe,GAC9B,MAAMZ,MACJA,EAAKa,SACLA,EAAQX,UACRA,EAASV,YACTA,GACEK,EAEJ,GAAIT,EAAWI,GAAc,CAC3B,IAAKoB,EAASN,QAAQd,YACpB,OAAO,EAGT,GAAIQ,GACF,GAAIc,EAAaF,EAASN,QAAQd,eAAiBsB,EAAatB,GAC9D,OAAO,OAEJ,IAAKe,EAAgBK,EAASN,QAAQd,YAAaA,GACxD,OAAO,EAIX,OAAwB,kBAAbqB,GAAoD,YAA1BD,EAASF,MAAMK,SAAyBF,MAIzEX,IAAcA,EAAUU,IAM9B,SAASP,EAAsBhB,EAAUiB,GAEvC,QAD2B,MAAXA,OAAkB,EAASA,EAAQU,iBAAmBF,GACxDzB,GAOhB,SAASyB,EAAazB,GACpB,OAAO4B,KAAKC,UAAU7B,GAAU,CAAC8B,EAAGC,IAAQC,EAAcD,GAAOE,OAAOC,KAAKH,GAAKI,OAAOC,QAAO,CAACC,EAAQC,KACvGD,EAAOC,GAAOP,EAAIO,GACXD,IACN,IAAMN,IAMX,SAASb,EAAgBqB,EAAGC,GAC1B,OAAOC,EAAiBF,EAAGC,GAM7B,SAASC,EAAiBF,EAAGC,GAC3B,OAAID,IAAMC,UAICD,UAAaC,OAIpBD,IAAKC,GAAkB,iBAAND,GAA+B,iBAANC,KACpCP,OAAOC,KAAKM,GAAGE,MAAKJ,IAAQG,EAAiBF,EAAED,GAAME,EAAEF,OAWnE,SAASK,EAAiBJ,EAAGC,GAC3B,GAAID,IAAMC,EACR,OAAOD,EAGT,MAAMK,EAAQC,EAAaN,IAAMM,EAAaL,GAE9C,GAAII,GAASZ,EAAcO,IAAMP,EAAcQ,GAAI,CACjD,MAAMM,EAAQF,EAAQL,EAAEQ,OAASd,OAAOC,KAAKK,GAAGQ,OAC1CC,EAASJ,EAAQJ,EAAIP,OAAOC,KAAKM,GACjCS,EAAQD,EAAOD,OACfG,EAAON,EAAQ,GAAK,GAC1B,IAAIO,EAAa,EAEjB,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAOG,IAAK,CAC9B,MAAMd,EAAMM,EAAQQ,EAAIJ,EAAOI,GAC/BF,EAAKZ,GAAOK,EAAiBJ,EAAED,GAAME,EAAEF,IAEnCY,EAAKZ,KAASC,EAAED,IAClBa,IAIJ,OAAOL,IAAUG,GAASE,IAAeL,EAAQP,EAAIW,EAGvD,OAAOV,EAMT,SAASa,EAAoBd,EAAGC,GAC9B,GAAID,IAAMC,GAAKA,IAAMD,EACnB,OAAO,EAGT,IAAK,MAAMD,KAAOC,EAChB,GAAIA,EAAED,KAASE,EAAEF,GACf,OAAO,EAIX,OAAO,EAET,SAASO,EAAajE,GACpB,OAAO0E,MAAMC,QAAQ3E,IAAUA,EAAMmE,SAAWd,OAAOC,KAAKtD,GAAOmE,OAGrE,SAASf,EAAcwB,GACrB,IAAKC,EAAmBD,GACtB,OAAO,EAIT,MAAME,EAAOF,EAAE9F,YAEf,QAAoB,IAATgG,EACT,OAAO,EAIT,MAAMC,EAAOD,EAAKE,UAElB,QAAKH,EAAmBE,MAKnBA,EAAKE,eAAe,iBAQ3B,SAASJ,EAAmBD,GAC1B,MAA6C,oBAAtCvB,OAAO2B,UAAUE,SAASC,KAAKP,GAGxC,SAASzD,EAAWnB,GAClB,OAAO0E,MAAMC,QAAQ3E,GAKvB,SAASoF,EAAMC,GACb,OAAO,IAAIC,SAAQC,IACjBC,WAAWD,EAASF,MAQxB,SAASI,EAAkBC,GACzBN,EAAM,GAAGO,KAAKD,GAShB,SAASE,EAAYC,EAAUC,EAAMzD,GAEnC,OAA2B,MAAvBA,EAAQ0D,aAAuB1D,EAAQ0D,YAAYF,EAAUC,GACxDD,EACuC,mBAA9BxD,EAAQ2D,kBACjB3D,EAAQ2D,kBAAkBH,EAAUC,IACJ,IAA9BzD,EAAQ2D,kBAEVjC,EAAiB8B,EAAUC,GAG7BA,ECpOH,MAAAG,EAAe,IApFrB,cAA2BpH,EACzBC,cACEoH,QAEAnH,KAAKoH,MAAQC,IAGX,IAAKxG,GAAYC,OAAOwG,iBAAkB,CACxC,MAAMjH,EAAW,IAAMgH,IAKvB,OAFAvG,OAAOwG,iBAAiB,mBAAoBjH,GAAU,GACtDS,OAAOwG,iBAAiB,QAASjH,GAAU,GACpC,KAELS,OAAOyG,oBAAoB,mBAAoBlH,GAC/CS,OAAOyG,oBAAoB,QAASlH,MAQ5CG,cACOR,KAAKwH,SACRxH,KAAKyH,iBAAiBzH,KAAKoH,OAI/B1G,gBAEI,IAAIgH,EADD1H,KAAKW,iBAG0B,OAAjC+G,EAAgB1H,KAAKwH,UAA4BE,EAActB,KAAKpG,MACrEA,KAAKwH,aAAUG,GAInBF,iBAAiBL,GACf,IAAIQ,EAEJ5H,KAAKoH,MAAQA,EACsB,OAAlCQ,EAAiB5H,KAAKwH,UAA4BI,EAAexB,KAAKpG,MACvEA,KAAKwH,QAAUJ,GAAMS,IACI,kBAAZA,EACT7H,KAAK8H,WAAWD,GAEhB7H,KAAKqH,aAKXS,WAAWD,GACO7H,KAAK6H,UAAYA,IAG/B7H,KAAK6H,QAAUA,EACf7H,KAAKqH,WAITA,UACErH,KAAKC,UAAU8H,SAAQ,EACrB1H,eAEAA,OAIJ2H,YACE,MAA4B,kBAAjBhI,KAAK6H,QACP7H,KAAK6H,QAIU,oBAAbI,UAIJ,MAACN,EAAW,UAAW,aAAanG,SAASyG,SAASC,mBChF3DC,EAAe,CAAC,SAAU,WAsF1B,MAAAC,EAAgB,IArFtB,cAA4BtI,EAC1BC,cACEoH,QAEAnH,KAAKoH,MAAQiB,IAGX,IAAKxH,GAAYC,OAAOwG,iBAAkB,CACxC,MAAMjH,EAAW,IAAMgI,IAMvB,OAHAF,EAAaJ,SAAQO,IACnBxH,OAAOwG,iBAAiBgB,EAAOjI,GAAU,MAEpC,KAEL8H,EAAaJ,SAAQO,IACnBxH,OAAOyG,oBAAoBe,EAAOjI,SAS5CG,cACOR,KAAKwH,SACRxH,KAAKyH,iBAAiBzH,KAAKoH,OAI/B1G,gBAEI,IAAIgH,EADD1H,KAAKW,iBAG0B,OAAjC+G,EAAgB1H,KAAKwH,UAA4BE,EAActB,KAAKpG,MACrEA,KAAKwH,aAAUG,GAInBF,iBAAiBL,GACf,IAAIQ,EAEJ5H,KAAKoH,MAAQA,EACsB,OAAlCQ,EAAiB5H,KAAKwH,UAA4BI,EAAexB,KAAKpG,MACvEA,KAAKwH,QAAUJ,GAAMmB,IACG,kBAAXA,EACTvI,KAAKwI,UAAUD,GAEfvI,KAAKqI,cAKXG,UAAUD,GACQvI,KAAKuI,SAAWA,IAG9BvI,KAAKuI,OAASA,EACdvI,KAAKqI,YAITA,WACErI,KAAKC,UAAU8H,SAAQ,EACrB1H,eAEAA,OAIJoI,WACE,MAA2B,kBAAhBzI,KAAKuI,OACPvI,KAAKuI,OAGW,oBAAdG,gBAAyD,IAArBA,UAAUC,QAIlDD,UAAUC,SCjFrB,SAASC,EAAkBC,GACzB,OAAOjH,KAAKkH,IAAI,IAAO,GAAKD,EAAc,KAG5C,SAASE,EAASC,GAChB,MAA0D,YAAnC,MAAfA,EAAsBA,EAAc,WAAyBZ,EAAcK,WAErF,MAAMQ,EACJlJ,YAAYuD,GACVtD,KAAKkJ,OAAoB,MAAX5F,OAAkB,EAASA,EAAQ4F,OACjDlJ,KAAKmJ,OAAoB,MAAX7F,OAAkB,EAASA,EAAQ6F,QAIrD,SAASC,EAAiBnI,GACxB,OAAOA,aAAiBgI,EAE1B,SAASI,EAAcC,GACrB,IAGIC,EACAC,EACAC,EALAC,GAAmB,EACnBb,EAAe,EACfc,GAAa,EAIjB,MAAMC,EAAU,IAAIrD,SAAQ,CAACsD,EAAcC,KACzCN,EAAiBK,EACjBJ,EAAgBK,KAkBZC,EAAc,KAAO7C,EAAac,aAAsC,WAAvBsB,EAAON,cAA6BZ,EAAcK,WAEnGjC,EAAUvF,IACT0I,IACHA,GAAa,EACO,MAApBL,EAAOU,WAA6BV,EAAOU,UAAU/I,GACvC,MAAdsI,GAA8BA,IAC9BC,EAAevI,KAIbgJ,EAAShJ,IACR0I,IACHA,GAAa,EACK,MAAlBL,EAAOY,SAA2BZ,EAAOY,QAAQjJ,GACnC,MAAdsI,GAA8BA,IAC9BE,EAAcxI,KAIZkJ,EAAQ,IACL,IAAI5D,SAAQ6D,IACjBb,EAAatI,IACX,MAAMoJ,EAAcV,IAAeI,IAMnC,OAJIM,GACFD,EAAgBnJ,GAGXoJ,GAGS,MAAlBf,EAAOgB,SAA2BhB,EAAOgB,aACxC1D,MAAK,KACN2C,OAAa5B,EAERgC,GACkB,MAArBL,EAAOiB,YAA8BjB,EAAOiB,gBAM5CC,EAAM,KAEV,GAAIb,EACF,OAGF,IAAIc,EAEJ,IACEA,EAAiBnB,EAAOoB,KACxB,MAAOC,GACPF,EAAiBlE,QAAQ0D,OAAOU,GAGlCpE,QAAQC,QAAQiE,GAAgB7D,KAAKJ,GAASoE,OAAMD,IAClD,IAAIE,EAAeC,EAGnB,GAAInB,EACF,OAIF,MAAMoB,EAA0C,OAAjCF,EAAgBvB,EAAOyB,OAAiBF,EAAgB,EACjEG,EAAyD,OAA3CF,EAAqBxB,EAAO0B,YAAsBF,EAAqBlC,EACrFqC,EAA8B,mBAAfD,EAA4BA,EAAWnC,EAAc8B,GAASK,EAC7EE,GAAwB,IAAVH,GAAmC,iBAAVA,GAAsBlC,EAAekC,GAA0B,mBAAVA,GAAwBA,EAAMlC,EAAc8B,IAE1IjB,GAAqBwB,GAMzBrC,IAEiB,MAAjBS,EAAO6B,QAA0B7B,EAAO6B,OAAOtC,EAAc8B,GAE7DtE,EAAM4E,GACLrE,MAAK,KACJ,GAAImD,IACF,OAAOI,OAIRvD,MAAK,KACF8C,EACFO,EAAOU,GAEPH,QAnBFP,EAAOU,OAgCb,OANI5B,EAASO,EAAON,aAClBwB,IAEAL,IAAQvD,KAAK4D,GAGR,CACLZ,UACAwB,OA1HaC,IACR1B,IACHM,EAAO,IAAIhB,EAAeoC,IACV,MAAhB/B,EAAOgC,OAAyBhC,EAAOgC,UAwHzCC,SAAU,KAC0B,MAAdhC,OAAqB,EAASA,KAC7BK,EAAUrD,QAAQC,UAEzCgF,YAxHkB,KAClB9B,GAAmB,GAwHnB+B,cArHoB,KACpB/B,GAAmB,IC7CvB,MAAMgC,EAAgBC,QC+FhB,MAAAC,EA7FN,WACE,IAAIC,EAAQ,GACRC,EAAe,EAEfC,EAAWpF,IACbA,KAGEqF,EAAgBrF,IAClBA,KAGF,MAiBMsF,EAAWtF,IACXmF,EACFD,EAAMK,KAAKvF,GAEXD,GAAkB,KAChBqF,EAASpF,OAiBTwF,EAAQ,KACZ,MAAMC,EAAgBP,EACtBA,EAAQ,GAEJO,EAAchH,QAChBsB,GAAkB,KAChBsF,GAAc,KACZI,EAAcrE,SAAQpB,IACpBoF,EAASpF,aAyBnB,MAAO,CACL0F,MAzEY1F,IACZ,IAAIjC,EACJoH,IAEA,IACEpH,EAASiC,IACD,QACRmF,IAEKA,GACHK,IAIJ,OAAOzH,GA4DP4H,WA3CiB3F,GACV,IAAI4F,KACTN,GAAS,KACPtF,KAAY4F,OAyChBN,WACAO,kBAjBwB9B,IACxBqB,EAAWrB,GAiBX+B,uBAT6B/B,IAC7BsB,EAAgBtB,IAYEgC,GC7FtB,MAAMC,EACJC,UACE5M,KAAK6M,iBAGPC,aACE9M,KAAK6M,iBAED7L,EAAehB,KAAK+M,aACtB/M,KAAKgN,UAAYvG,YAAW,KAC1BzG,KAAKiN,mBACJjN,KAAK+M,YAIZG,gBAAgBC,GAEdnN,KAAK+M,UAAYnL,KAAKC,IAAI7B,KAAK+M,WAAa,EAAmB,MAAhBI,EAAuBA,EAAetM,EAAWK,IAAW,KAG7G2L,iBACM7M,KAAKgN,YACPI,aAAapN,KAAKgN,WAClBhN,KAAKgN,eAAYrF,IClBvB,MAAM0F,UAAcV,EAClB5M,YAAYuJ,GACVnC,QACAnH,KAAKsN,qBAAsB,EAC3BtN,KAAKuN,eAAiBjE,EAAOiE,eAC7BvN,KAAKwN,WAAWlE,EAAOhG,SACvBtD,KAAKyN,UAAY,GACjBzN,KAAK0N,MAAQpE,EAAOoE,MACpB1N,KAAK2N,OAASrE,EAAOqE,QAAUjC,EAC/B1L,KAAKqC,SAAWiH,EAAOjH,SACvBrC,KAAKoD,UAAYkG,EAAOlG,UACxBpD,KAAK4N,aAAetE,EAAO5F,OAmb/B,SAAyBJ,GACvB,MAAMyD,EAAsC,mBAAxBzD,EAAQuK,YAA6BvK,EAAQuK,cAAgBvK,EAAQuK,YACnFC,OAA0B,IAAT/G,EACjBgH,EAAuBD,EAAkD,mBAAjCxK,EAAQyK,qBAAsCzK,EAAQyK,uBAAyBzK,EAAQyK,qBAAuB,EAC5J,MAAO,CACLhH,OACAiH,gBAAiB,EACjBC,cAAeH,EAAkC,MAAxBC,EAA+BA,EAAuBjM,KAAKC,MAAQ,EAC5F4I,MAAO,KACPuD,iBAAkB,EAClBC,eAAgB,EAChBC,kBAAmB,EACnBC,mBAAoB,KACpBC,UAAW,KACXC,eAAe,EACfxK,OAAQ+J,EAAU,UAAY,UAC9B7K,YAAa,QAncuBuL,CAAgBxO,KAAKsD,SACzDtD,KAAK0D,MAAQ1D,KAAK4N,aAClB5N,KAAK8M,aAGH2B,WACF,OAAOzO,KAAKsD,QAAQmL,KAGtBjB,WAAWlK,GACTtD,KAAKsD,QAAU,IAAKtD,KAAKuN,kBACpBjK,GAELtD,KAAKkN,gBAAgBlN,KAAKsD,QAAQyJ,WAGpCE,iBACOjN,KAAKyN,UAAUrI,QAAqC,SAA3BpF,KAAK0D,MAAMT,aACvCjD,KAAK0N,MAAMgB,OAAO1O,MAItB2O,QAAQC,EAAStL,GACf,MAAMyD,EAAOF,EAAY7G,KAAK0D,MAAMqD,KAAM6H,EAAS5O,KAAKsD,SAQxD,OANAtD,KAAK6O,SAAS,CACZ9H,OACAhE,KAAM,UACNkL,cAA0B,MAAX3K,OAAkB,EAASA,EAAQ5B,UAClDoN,OAAmB,MAAXxL,OAAkB,EAASA,EAAQwL,SAEtC/H,EAGTgI,SAASrL,EAAOsL,GACdhP,KAAK6O,SAAS,CACZ9L,KAAM,WACNW,QACAsL,oBAIJ5D,OAAO9H,GACL,IAAI2L,EAEJ,MAAMrF,EAAU5J,KAAK4J,QAErB,OADkC,OAAjCqF,EAAgBjP,KAAKkP,UAA4BD,EAAc7D,OAAO9H,GAChEsG,EAAUA,EAAQhD,KAAK7F,GAAM6J,MAAM7J,GAAQwF,QAAQC,UAG5DoG,UACEzF,MAAMyF,UACN5M,KAAKoL,OAAO,CACVjC,QAAQ,IAIZgG,QACEnP,KAAK4M,UACL5M,KAAK+O,SAAS/O,KAAK4N,cAGrBpK,WACE,OAAOxD,KAAKyN,UAAU1I,MAAKqK,IAAyC,IAA7BA,EAAS9L,QAAQ+L,UAG1DC,aACE,OAAOtP,KAAKuP,oBAAsB,IAAMvP,KAAKwD,WAG/CC,UACE,OAAOzD,KAAK0D,MAAM6K,gBAAkBvO,KAAK0D,MAAMuK,eAAiBjO,KAAKyN,UAAU1I,MAAKqK,GAAYA,EAASI,mBAAmB/L,UAG9HgM,cAAc9N,EAAY,GACxB,OAAO3B,KAAK0D,MAAM6K,gBAAkBvO,KAAK0D,MAAMuK,gBAAkBxM,EAAezB,KAAK0D,MAAMuK,cAAetM,GAG5G0F,UACE,IAAIqI,EAEJ,MAAMN,EAAWpP,KAAKyN,UAAUkC,MAAKpO,GAAKA,EAAEqO,6BAExCR,GACFA,EAASS,QAAQ,CACfC,eAAe,IAKgB,OAAlCJ,EAAiB1P,KAAKkP,UAA4BQ,EAAenE,WAGpElD,WACE,IAAI0H,EAEJ,MAAMX,EAAWpP,KAAKyN,UAAUkC,MAAKpO,GAAKA,EAAEyO,2BAExCZ,GACFA,EAASS,QAAQ,CACfC,eAAe,IAKgB,OAAlCC,EAAiB/P,KAAKkP,UAA4Ba,EAAexE,WAGpE0E,YAAYb,GACLpP,KAAKyN,UAAUjM,SAAS4N,KAC3BpP,KAAKyN,UAAUvB,KAAKkD,GAEpBpP,KAAK6M,iBACL7M,KAAK0N,MAAMwC,OAAO,CAChBnN,KAAM,gBACND,MAAO9C,KACPoP,cAKNe,eAAef,GACTpP,KAAKyN,UAAUjM,SAAS4N,KAC1BpP,KAAKyN,UAAYzN,KAAKyN,UAAUnM,QAAOC,GAAKA,IAAM6N,IAE7CpP,KAAKyN,UAAUrI,SAGdpF,KAAKkP,UACHlP,KAAKsN,oBACPtN,KAAKkP,QAAQ9D,OAAO,CAClBlC,QAAQ,IAGVlJ,KAAKkP,QAAQ1D,eAIjBxL,KAAK8M,cAGP9M,KAAK0N,MAAMwC,OAAO,CAChBnN,KAAM,kBACND,MAAO9C,KACPoP,cAKNG,oBACE,OAAOvP,KAAKyN,UAAUrI,OAGxBgL,aACOpQ,KAAK0D,MAAM6K,eACdvO,KAAK6O,SAAS,CACZ9L,KAAM,eAKZsN,MAAM/M,EAASgN,GACb,IAAIC,EAAuBC,EAE3B,GAA+B,SAA3BxQ,KAAK0D,MAAMT,YACb,GAAIjD,KAAK0D,MAAMuK,eAAiC,MAAhBqC,GAAwBA,EAAaR,cAEnE9P,KAAKoL,OAAO,CACVjC,QAAQ,SAEL,GAAInJ,KAAK4J,QAAS,CACvB,IAAI6G,EAKJ,OAFmC,OAAlCA,EAAiBzQ,KAAKkP,UAA4BuB,EAAehF,gBAE3DzL,KAAK4J,QAWhB,GANItG,GACFtD,KAAKwN,WAAWlK,IAKbtD,KAAKsD,QAAQhB,QAAS,CACzB,MAAM8M,EAAWpP,KAAKyN,UAAUkC,MAAKpO,GAAKA,EAAE+B,QAAQhB,UAEhD8M,GACFpP,KAAKwN,WAAW4B,EAAS9L,SAU7B,MAAMoN,EP8EV,WACE,GAA+B,mBAApBC,gBACT,OAAO,IAAIA,gBOhFaC,GAElBC,EAAiB,CACrBxO,SAAUrC,KAAKqC,SACfyO,eAAWnJ,EACX8G,KAAMzO,KAAKyO,MAKPsC,EAAoBC,IACxB1M,OAAO2M,eAAeD,EAAQ,SAAU,CACtCE,YAAY,EACZC,IAAK,KACH,GAAIT,EAEF,OADA1Q,KAAKsN,qBAAsB,EACpBoD,EAAgBU,WAQ/BL,EAAkBF,GAElB,MAUMQ,EAAU,CACdf,eACAhN,QAAStD,KAAKsD,QACdjB,SAAUrC,KAAKqC,SACfqB,MAAO1D,KAAK0D,MACZ4N,QAfc,IACTtR,KAAKsD,QAAQhB,SAIlBtC,KAAKsN,qBAAsB,EACpBtN,KAAKsD,QAAQhB,QAAQuO,IAJnBtK,QAAQ0D,OAAO,iCAAmCjK,KAAKsD,QAAQF,UAAY,MAqBpF,IAAImO,GANNR,EAAkBM,GACiC,OAAlDd,EAAwBvQ,KAAKsD,QAAQkO,WAA6BjB,EAAsBkB,QAAQJ,GAEjGrR,KAAK0R,YAAc1R,KAAK0D,MAEO,SAA3B1D,KAAK0D,MAAMT,aAA0BjD,KAAK0D,MAAM4K,aAAiE,OAAjDkC,EAAwBa,EAAQf,mBAAwB,EAASE,EAAsB/B,QAGzJzO,KAAK6O,SAAS,CACZ9L,KAAM,QACN0L,KAAyD,OAAlD8C,EAAyBF,EAAQf,mBAAwB,EAASiB,EAAuB9C,OAIpG,MAAMvE,EAAUS,IAUZ,IAAIgH,EAAuBC,EAAoBC,EAAwBC,GARnE1I,EAAiBuB,IAAUA,EAAMxB,QACrCnJ,KAAK6O,SAAS,CACZ9L,KAAM,QACN4H,MAAOA,IAINvB,EAAiBuB,MAI0D,OAA7EgH,GAAyBC,EAAqB5R,KAAK0N,MAAMpE,QAAQY,UAA4ByH,EAAsBvL,KAAKwL,EAAoBjH,EAAO3K,MAClE,OAAjF6R,GAA0BC,EAAsB9R,KAAK0N,MAAMpE,QAAQyI,YAA8BF,EAAuBzL,KAAK0L,EAAqB9R,KAAK0D,MAAMqD,KAAM4D,EAAO3K,OAOxKA,KAAKgS,sBAERhS,KAAK8M,aAGP9M,KAAKgS,sBAAuB,GAsD9B,OAlDAhS,KAAKkP,QAAU7F,EAAc,CAC3BqB,GAAI2G,EAAQC,QACZhG,MAA0B,MAAnBoF,OAA0B,EAASA,EAAgBpF,MAAMlL,KAAKsQ,GACrE1G,UAAWjD,IACT,IAAIkL,EAAwBC,EAAqBC,EAAwBC,OAErD,IAATrL,GASX/G,KAAK2O,QAAQ5H,GAEqE,OAAjFkL,GAA0BC,EAAsBlS,KAAK0N,MAAMpE,QAAQU,YAA8BiI,EAAuB7L,KAAK8L,EAAqBnL,EAAM/G,MACvE,OAAjFmS,GAA0BC,EAAsBpS,KAAK0N,MAAMpE,QAAQyI,YAA8BI,EAAuB/L,KAAKgM,EAAqBrL,EAAM/G,KAAK0D,MAAMiH,MAAO3K,MAEtKA,KAAKgS,sBAERhS,KAAK8M,aAGP9M,KAAKgS,sBAAuB,GAd1B9H,EAAQ,IAAImI,MAAMrS,KAAKoD,UAAY,wBAgBvC8G,UACAiB,OAAQ,CAACtC,EAAc8B,KACrB3K,KAAK6O,SAAS,CACZ9L,KAAM,SACN8F,eACA8B,WAGJL,QAAS,KACPtK,KAAK6O,SAAS,CACZ9L,KAAM,WAGVwH,WAAY,KACVvK,KAAK6O,SAAS,CACZ9L,KAAM,cAGVgI,MAAOsG,EAAQ/N,QAAQyH,MACvBC,WAAYqG,EAAQ/N,QAAQ0H,WAC5BhC,YAAaqI,EAAQ/N,QAAQ0F,cAE/BhJ,KAAK4J,QAAU5J,KAAKkP,QAAQtF,QACrB5J,KAAK4J,QAGdiF,SAASyD,GA+EPtS,KAAK0D,MA9EWA,KACd,IAAI6O,EAAcC,EAElB,OAAQF,EAAOvP,MACb,IAAK,SACH,MAAO,IAAKW,EACV0K,kBAAmBkE,EAAOzJ,aAC1BwF,mBAAoBiE,EAAO3H,OAG/B,IAAK,QACH,MAAO,IAAKjH,EACVT,YAAa,UAGjB,IAAK,WACH,MAAO,IAAKS,EACVT,YAAa,YAGjB,IAAK,QACH,MAAO,IAAKS,EACV0K,kBAAmB,EACnBC,mBAAoB,KACpBC,UAA2C,OAA/BiE,EAAeD,EAAO7D,MAAgB8D,EAAe,KACjEtP,YAAa8F,EAAS/I,KAAKsD,QAAQ0F,aAAe,WAAa,aAC1DtF,EAAMuK,eAAiB,CAC1BtD,MAAO,KACP5G,OAAQ,YAId,IAAK,UACH,MAAO,IAAKL,EACVqD,KAAMuL,EAAOvL,KACbiH,gBAAiBtK,EAAMsK,gBAAkB,EACzCC,cAAiE,OAAjDuE,EAAwBF,EAAOrE,eAAyBuE,EAAwB1Q,KAAKC,MACrG4I,MAAO,KACP4D,eAAe,EACfxK,OAAQ,cACHuO,EAAOxD,QAAU,CACpB7L,YAAa,OACbmL,kBAAmB,EACnBC,mBAAoB,OAI1B,IAAK,QACH,MAAM1D,EAAQ2H,EAAO3H,MAErB,OAAIvB,EAAiBuB,IAAUA,EAAMzB,QAAUlJ,KAAK0R,YAC3C,IAAK1R,KAAK0R,YACfzO,YAAa,QAIV,IAAKS,EACViH,MAAOA,EACPuD,iBAAkBxK,EAAMwK,iBAAmB,EAC3CC,eAAgBrM,KAAKC,MACrBqM,kBAAmB1K,EAAM0K,kBAAoB,EAC7CC,mBAAoB1D,EACpB1H,YAAa,OACbc,OAAQ,SAGZ,IAAK,aACH,MAAO,IAAKL,EACV6K,eAAe,GAGnB,IAAK,WACH,MAAO,IAAK7K,KACP4O,EAAO5O,SAKL+O,CAAQzS,KAAK0D,OAC1BkI,EAAcS,OAAM,KAClBrM,KAAKyN,UAAU1F,SAAQqH,IACrBA,EAASsD,cAAcJ,MAEzBtS,KAAK0N,MAAMwC,OAAO,CAChBpN,MAAO9C,KACP+C,KAAM,UACNuP,eCxbR,MAAMK,UAAmB7S,EACvBC,YAAYuJ,GACVnC,QACAnH,KAAKsJ,OAASA,GAAU,GACxBtJ,KAAK4S,QAAU,GACf5S,KAAK6S,WAAa,GAGpBC,MAAMC,EAAQzP,EAASI,GACrB,IAAIsP,EAEJ,MAAM3Q,EAAWiB,EAAQjB,SACnBe,EAAwD,OAA3C4P,EAAqB1P,EAAQF,WAAqB4P,EAAqB3P,EAAsBhB,EAAUiB,GAC1H,IAAIR,EAAQ9C,KAAKmR,IAAI/N,GAerB,OAbKN,IACHA,EAAQ,IAAIuK,EAAM,CAChBK,MAAO1N,KACP2N,OAAQoF,EAAOE,YACf5Q,WACAe,YACAE,QAASyP,EAAOG,oBAAoB5P,GACpCI,QACA6J,eAAgBwF,EAAOI,iBAAiB9Q,KAE1CrC,KAAKO,IAAIuC,IAGJA,EAGTvC,IAAIuC,GACG9C,KAAK6S,WAAW/P,EAAMM,aACzBpD,KAAK6S,WAAW/P,EAAMM,WAAaN,EACnC9C,KAAK4S,QAAQ1G,KAAKpJ,GAClB9C,KAAKkQ,OAAO,CACVnN,KAAM,QACND,WAKN4L,OAAO5L,GACL,MAAMsQ,EAAapT,KAAK6S,WAAW/P,EAAMM,WAErCgQ,IACFtQ,EAAM8J,UACN5M,KAAK4S,QAAU5S,KAAK4S,QAAQtR,QAAOC,GAAKA,IAAMuB,IAE1CsQ,IAAetQ,UACV9C,KAAK6S,WAAW/P,EAAMM,WAG/BpD,KAAKkQ,OAAO,CACVnN,KAAM,UACND,WAKNuQ,QACEzH,EAAcS,OAAM,KAClBrM,KAAK4S,QAAQ7K,SAAQjF,IACnB9C,KAAK0O,OAAO5L,SAKlBqO,IAAI/N,GACF,OAAOpD,KAAK6S,WAAWzP,GAGzBkQ,SACE,OAAOtT,KAAK4S,QAMdjD,KAAK1N,EAAMC,GACT,MAAOW,GAAWH,EAAgBT,EAAMC,GAMxC,YAJ6B,IAAlBW,EAAQG,QACjBH,EAAQG,OAAQ,GAGXhD,KAAK4S,QAAQjD,MAAK7M,GAASF,EAAWC,EAASC,KAMxDyQ,QAAQtR,EAAMC,GACZ,MAAOW,GAAWH,EAAgBT,EAAMC,GACxC,OAAOoC,OAAOC,KAAK1B,GAASuC,OAAS,EAAIpF,KAAK4S,QAAQtR,QAAOwB,GAASF,EAAWC,EAASC,KAAU9C,KAAK4S,QAG3G1C,OAAO5H,GACLsD,EAAcS,OAAM,KAClBrM,KAAKC,UAAU8H,SAAQ,EACrB1H,eAEAA,EAASiI,SAKfjB,UACEuE,EAAcS,OAAM,KAClBrM,KAAK4S,QAAQ7K,SAAQjF,IACnBA,EAAMuE,gBAKZgB,WACEuD,EAAcS,OAAM,KAClBrM,KAAK4S,QAAQ7K,SAAQjF,IACnBA,EAAMuF,kBCtHd,MAAMmL,UAAiB7G,EACrB5M,YAAYuJ,GACVnC,QACAnH,KAAKuN,eAAiBjE,EAAOiE,eAC7BvN,KAAKyT,WAAanK,EAAOmK,WACzBzT,KAAK0T,cAAgBpK,EAAOoK,cAC5B1T,KAAK2N,OAASrE,EAAOqE,QAAUjC,EAC/B1L,KAAKyN,UAAY,GACjBzN,KAAK0D,MAAQ4F,EAAO5F,OAAS8K,IAC7BxO,KAAKwN,WAAWlE,EAAOhG,SACvBtD,KAAK8M,aAGPU,WAAWlK,GACTtD,KAAKsD,QAAU,IAAKtD,KAAKuN,kBACpBjK,GAELtD,KAAKkN,gBAAgBlN,KAAKsD,QAAQyJ,WAGhC0B,WACF,OAAOzO,KAAKsD,QAAQmL,KAGtBM,SAASrL,GACP1D,KAAK6O,SAAS,CACZ9L,KAAM,WACNW,UAIJuM,YAAYb,GACLpP,KAAKyN,UAAUjM,SAAS4N,KAC3BpP,KAAKyN,UAAUvB,KAAKkD,GAEpBpP,KAAK6M,iBACL7M,KAAK0T,cAAcxD,OAAO,CACxBnN,KAAM,gBACNa,SAAU5D,KACVoP,cAKNe,eAAef,GACbpP,KAAKyN,UAAYzN,KAAKyN,UAAUnM,QAAOC,GAAKA,IAAM6N,IAClDpP,KAAK8M,aACL9M,KAAK0T,cAAcxD,OAAO,CACxBnN,KAAM,kBACNa,SAAU5D,KACVoP,aAIJnC,iBACOjN,KAAKyN,UAAUrI,SACQ,YAAtBpF,KAAK0D,MAAMK,OACb/D,KAAK8M,aAEL9M,KAAK0T,cAAchF,OAAO1O,OAKhCuL,WACE,IAAIoI,EAAuB1E,EAE3B,OAA+G,OAAvG0E,EAA0D,OAAjC1E,EAAgBjP,KAAKkP,cAAmB,EAASD,EAAc1D,YAAsBoI,EAAwB3T,KAAK4T,UAGrJC,gBACE,MAAMC,EAAkB,KACtB,IAAIC,EA+BJ,OA7BA/T,KAAKkP,QAAU7F,EAAc,CAC3BqB,GAAI,IACG1K,KAAKsD,QAAQb,WAIXzC,KAAKsD,QAAQb,WAAWzC,KAAK0D,MAAMsQ,WAHjCzN,QAAQ0D,OAAO,uBAK1BkB,OAAQ,CAACtC,EAAc8B,KACrB3K,KAAK6O,SAAS,CACZ9L,KAAM,SACN8F,eACA8B,WAGJL,QAAS,KACPtK,KAAK6O,SAAS,CACZ9L,KAAM,WAGVwH,WAAY,KACVvK,KAAK6O,SAAS,CACZ9L,KAAM,cAGVgI,MAAqD,OAA7CgJ,EAAsB/T,KAAKsD,QAAQyH,OAAiBgJ,EAAsB,EAClF/I,WAAYhL,KAAKsD,QAAQ0H,WACzBhC,YAAahJ,KAAKsD,QAAQ0F,cAErBhJ,KAAKkP,QAAQtF,SAGhBqK,EAAiC,YAAtBjU,KAAK0D,MAAMK,OAE5B,IACE,IAAImQ,EAAwBC,EAAwBC,EAAuBC,EAAgBC,EAAwBC,EAAwBC,EAAuBC,EAElK,IAAKR,EAAU,CACb,IAAIS,EAAuBC,EAAwBC,EAAuBC,EAE1E7U,KAAK6O,SAAS,CACZ9L,KAAM,UACNiR,UAAWhU,KAAKsD,QAAQ0Q,kBAGwE,OAA1FU,GAAyBC,EAAyB3U,KAAK0T,cAAcpK,QAAQwL,eAAoB,EAASJ,EAAsBtO,KAAKuO,EAAwB3U,KAAK0D,MAAMsQ,UAAWhU,OAC3L,MAAMqR,QAAsF,OAApEuD,GAAyBC,EAAgB7U,KAAKsD,SAASwR,eAAoB,EAASF,EAAsBxO,KAAKyO,EAAe7U,KAAK0D,MAAMsQ,YAE7J3C,IAAYrR,KAAK0D,MAAM2N,SACzBrR,KAAK6O,SAAS,CACZ9L,KAAM,UACNsO,UACA2C,UAAWhU,KAAK0D,MAAMsQ,YAK5B,MAAMjN,QAAa+M,IAWnB,aAToG,OAA5FI,GAA0BC,EAAyBnU,KAAK0T,cAAcpK,QAAQU,gBAAqB,EAASkK,EAAuB9N,KAAK+N,EAAwBpN,EAAM/G,KAAK0D,MAAMsQ,UAAWhU,KAAK0D,MAAM2N,QAASrR,aAC1I,OAAtEoU,GAAyBC,EAAiBrU,KAAKsD,SAAS0G,gBAAqB,EAASoK,EAAsBhO,KAAKiO,EAAgBtN,EAAM/G,KAAK0D,MAAMsQ,UAAWhU,KAAK0D,MAAM2N,gBAE5E,OAA5FiD,GAA0BC,EAAyBvU,KAAK0T,cAAcpK,QAAQyI,gBAAqB,EAASuC,EAAuBlO,KAAKmO,EAAwBxN,EAAM,KAAM/G,KAAK0D,MAAMsQ,UAAWhU,KAAK0D,MAAM2N,QAASrR,aAChJ,OAAtEwU,GAAyBC,EAAiBzU,KAAKsD,SAASyO,gBAAqB,EAASyC,EAAsBpO,KAAKqO,EAAgB1N,EAAM,KAAM/G,KAAK0D,MAAMsQ,UAAWhU,KAAK0D,MAAM2N,UACtLrR,KAAK6O,SAAS,CACZ9L,KAAM,UACNgE,SAEKA,EACP,MAAO4D,GACP,IACE,IAAIoK,EAAwBC,EAAwBC,EAAuBC,EAAgBC,EAAwBC,EAAyBC,EAAwBC,EAapK,YAVkG,OAA1FP,GAA0BC,EAAyBhV,KAAK0T,cAAcpK,QAAQY,cAAmB,EAAS6K,EAAuB3O,KAAK4O,EAAwBrK,EAAO3K,KAAK0D,MAAMsQ,UAAWhU,KAAK0D,MAAM2N,QAASrR,aAM3I,OAApEiV,GAAyBC,EAAiBlV,KAAKsD,SAAS4G,cAAmB,EAAS+K,EAAsB7O,KAAK8O,EAAgBvK,EAAO3K,KAAK0D,MAAMsQ,UAAWhU,KAAK0D,MAAM2N,gBAE1E,OAA7F8D,GAA0BC,EAA0BpV,KAAK0T,cAAcpK,QAAQyI,gBAAqB,EAASoD,EAAuB/O,KAAKgP,OAAyBzN,EAAWgD,EAAO3K,KAAK0D,MAAMsQ,UAAWhU,KAAK0D,MAAM2N,QAASrR,aACvJ,OAAvEqV,GAA0BC,EAAiBtV,KAAKsD,SAASyO,gBAAqB,EAASsD,EAAuBjP,KAAKkP,OAAgB3N,EAAWgD,EAAO3K,KAAK0D,MAAMsQ,UAAWhU,KAAK0D,MAAM2N,UACxL1G,EACE,QACR3K,KAAK6O,SAAS,CACZ9L,KAAM,QACN4H,MAAOA,MAMfkE,SAASyD,GA0DPtS,KAAK0D,MAzDWA,KACd,OAAQ4O,EAAOvP,MACb,IAAK,SACH,MAAO,IAAKW,EACVmF,aAAcyJ,EAAOzJ,aACrB0M,cAAejD,EAAO3H,OAG1B,IAAK,QACH,MAAO,IAAKjH,EACV8R,UAAU,GAGd,IAAK,WACH,MAAO,IAAK9R,EACV8R,UAAU,GAGd,IAAK,UACH,MAAO,IAAK9R,EACV2N,QAASiB,EAAOjB,QAChBtK,UAAMY,EACNkB,aAAc,EACd0M,cAAe,KACf5K,MAAO,KACP6K,UAAWzM,EAAS/I,KAAKsD,QAAQ0F,aACjCjF,OAAQ,UACRiQ,UAAW1B,EAAO0B,WAGtB,IAAK,UACH,MAAO,IAAKtQ,EACVqD,KAAMuL,EAAOvL,KACb8B,aAAc,EACd0M,cAAe,KACf5K,MAAO,KACP5G,OAAQ,UACRyR,UAAU,GAGd,IAAK,QACH,MAAO,IAAK9R,EACVqD,UAAMY,EACNgD,MAAO2H,EAAO3H,MACd9B,aAAcnF,EAAMmF,aAAe,EACnC0M,cAAejD,EAAO3H,MACtB6K,UAAU,EACVzR,OAAQ,SAGZ,IAAK,WACH,MAAO,IAAKL,KACP4O,EAAO5O,SAKL+O,CAAQzS,KAAK0D,OAC1BkI,EAAcS,OAAM,KAClBrM,KAAKyN,UAAU1F,SAAQqH,IACrBA,EAASqG,iBAAiBnD,MAE5BtS,KAAK0T,cAAcxD,OAAO,CACxBtM,SAAU5D,KACV+C,KAAM,UACNuP,eAMR,SAAS9D,IACP,MAAO,CACL6C,aAAS1J,EACTZ,UAAMY,EACNgD,MAAO,KACP9B,aAAc,EACd0M,cAAe,KACfC,UAAU,EACVzR,OAAQ,OACRiQ,eAAWrM,GCzPf,MAAM+N,UAAsB5V,EAC1BC,YAAYuJ,GACVnC,QACAnH,KAAKsJ,OAASA,GAAU,GACxBtJ,KAAK2V,UAAY,GACjB3V,KAAKyT,WAAa,EAGpBX,MAAMC,EAAQzP,EAASI,GACrB,MAAME,EAAW,IAAI4P,EAAS,CAC5BE,cAAe1T,KACf2N,OAAQoF,EAAOE,YACfQ,aAAczT,KAAKyT,WACnBnQ,QAASyP,EAAO6C,uBAAuBtS,GACvCI,QACA6J,eAAgBjK,EAAQd,YAAcuQ,EAAO8C,oBAAoBvS,EAAQd,kBAAemF,IAG1F,OADA3H,KAAKO,IAAIqD,GACFA,EAGTrD,IAAIqD,GACF5D,KAAK2V,UAAUzJ,KAAKtI,GACpB5D,KAAKkQ,OAAO,CACVnN,KAAM,QACNa,aAIJ8K,OAAO9K,GACL5D,KAAK2V,UAAY3V,KAAK2V,UAAUrU,QAAOC,GAAKA,IAAMqC,IAClD5D,KAAKkQ,OAAO,CACVnN,KAAM,UACNa,aAIJyP,QACEzH,EAAcS,OAAM,KAClBrM,KAAK2V,UAAU5N,SAAQnE,IACrB5D,KAAK0O,OAAO9K,SAKlB0P,SACE,OAAOtT,KAAK2V,UAGdhG,KAAK9M,GAKH,YAJ6B,IAAlBA,EAAQG,QACjBH,EAAQG,OAAQ,GAGXhD,KAAK2V,UAAUhG,MAAK/L,GAAYD,EAAcd,EAASe,KAGhE2P,QAAQ1Q,GACN,OAAO7C,KAAK2V,UAAUrU,QAAOsC,GAAYD,EAAcd,EAASe,KAGlEsM,OAAO5H,GACLsD,EAAcS,OAAM,KAClBrM,KAAKC,UAAU8H,SAAQ,EACrB1H,eAEAA,EAASiI,SAKfwN,wBACE,IAAIC,EAQJ,OANA/V,KAAKgW,UAAgD,OAAnCD,EAAiB/V,KAAKgW,UAAoBD,EAAiBxP,QAAQC,WAAWI,MAAK,KACnG,MAAMqP,EAAkBjW,KAAK2V,UAAUrU,QAAOC,GAAKA,EAAEmC,MAAM8R,WAC3D,OAAO5J,EAAcS,OAAM,IAAM4J,EAAgBxR,QAAO,CAACmF,EAAShG,IAAagG,EAAQhD,MAAK,IAAMhD,EAAS2H,WAAWX,MAAM7J,MAAQwF,QAAQC,gBAC3II,MAAK,KACN5G,KAAKgW,cAAWrO,KAEX3H,KAAKgW,UCtFhB,SAASE,IACP,MAAO,CACLzE,QAASJ,IACPA,EAAQC,QAAU,KAChB,IAAId,EAAuBe,EAAwB4E,EAAwBC,EAAwBC,EAAqBC,EAExH,MAAMC,EAAgE,OAAjD/F,EAAwBa,EAAQf,eAA0F,OAAxDiB,EAAyBf,EAAsB/B,WAAzD,EAAkF8C,EAAuBgF,YAChLC,EAA+D,OAAlDL,EAAyB9E,EAAQf,eAA2F,OAAzD8F,EAAyBD,EAAuB1H,WAA1D,EAAmF2H,EAAuBI,UAChL1F,EAAyB,MAAb0F,OAAoB,EAASA,EAAU1F,UACnD2F,EAA4E,aAAzC,MAAbD,OAAoB,EAASA,EAAUE,WAC7DC,EAAgF,cAAzC,MAAbH,OAAoB,EAASA,EAAUE,WACjEE,GAA0D,OAA7CP,EAAsBhF,EAAQ3N,MAAMqD,WAAgB,EAASsP,EAAoBQ,QAAU,GACxGC,GAAgE,OAA9CR,EAAuBjF,EAAQ3N,MAAMqD,WAAgB,EAASuP,EAAqBS,aAAe,GAC1H,IAAIC,EAAgBF,EAChBG,GAAY,EAEhB,MAsBM3U,EAAU+O,EAAQ/N,QAAQhB,SAAY,KAAMiE,QAAQ0D,OAAO,iCAAmCoH,EAAQ/N,QAAQF,UAAY,MAE1H8T,EAAgB,CAACL,EAAOM,EAAOC,EAAMC,KACzCL,EAAgBK,EAAW,CAACF,KAAUH,GAAiB,IAAIA,EAAeG,GACnEE,EAAW,CAACD,KAASP,GAAS,IAAIA,EAAOO,IAI5CE,EAAY,CAACT,EAAO/H,EAAQqI,EAAOE,KACvC,GAAIJ,EACF,OAAO1Q,QAAQ0D,OAAO,aAGxB,QAAqB,IAAVkN,IAA0BrI,GAAU+H,EAAMzR,OACnD,OAAOmB,QAAQC,QAAQqQ,GAGzB,MAAMhG,EAAiB,CACrBxO,SAAUgP,EAAQhP,SAClByO,UAAWqG,EACX1I,KAAM4C,EAAQ/N,QAAQmL,MA1CAuC,QA4CNH,EA3ClBvM,OAAO2M,eAAeD,EAAQ,SAAU,CACtCE,YAAY,EACZC,IAAK,KACH,IAAIoG,EAKEC,EAON,OAV0C,OAArCD,EAAkBlG,EAAQD,SAAmBmG,EAAgBE,QAChER,GAAY,EAI2B,OAAtCO,EAAmBnG,EAAQD,SAA2BoG,EAAiBlQ,iBAAiB,SAAS,KAChG2P,GAAY,KAIT5F,EAAQD,UA6BnB,MAAMsG,EAAgBpV,EAAQuO,GAE9B,OADgBtK,QAAQC,QAAQkR,GAAe9Q,MAAKwQ,GAAQF,EAAcL,EAAOM,EAAOC,EAAMC,MAIhG,IAAIzN,EAEJ,GAAKgN,EAASxR,OAGT,GAAIqR,EAAoB,CAC3B,MAAM3H,OAA8B,IAAdgC,EAChBqG,EAAQrI,EAASgC,EAAY6G,EAAiBtG,EAAQ/N,QAASsT,GACrEhN,EAAU0N,EAAUV,EAAU9H,EAAQqI,QAEnC,GAAIR,EAAwB,CAC/B,MAAM7H,OAA8B,IAAdgC,EAChBqG,EAAQrI,EAASgC,EAAY8G,EAAqBvG,EAAQ/N,QAASsT,GACzEhN,EAAU0N,EAAUV,EAAU9H,EAAQqI,GAAO,OAE1C,CACHH,EAAgB,GAChB,MAAMlI,OAAqD,IAArCuC,EAAQ/N,QAAQqU,iBAGtC/N,GAF6B2M,IAAeK,EAAS,IAAKL,EAAYK,EAAS,GAAI,EAAGA,GAErDU,EAAU,GAAIxI,EAAQgI,EAAc,IAAMvQ,QAAQC,QAAQ0Q,EAAc,GAAIJ,EAAc,GAAIF,EAAS,KAExI,IAAK,IAAInR,EAAI,EAAGA,EAAImR,EAASxR,OAAQK,IACnCmE,EAAUA,EAAQhD,MAAKiQ,IAGrB,IAF4BN,IAAeK,EAASnR,IAAK8Q,EAAYK,EAASnR,GAAIA,EAAGmR,GAE5D,CACvB,MAAMO,EAAQrI,EAASgI,EAAcrR,GAAKkS,EAAiBtG,EAAQ/N,QAASuT,GAC5E,OAAOS,EAAUT,EAAO/H,EAAQqI,GAGlC,OAAO5Q,QAAQC,QAAQ0Q,EAAcL,EAAOC,EAAcrR,GAAImR,EAASnR,aA5B3EmE,EAAU0N,EAAU,IAqCtB,OAJqB1N,EAAQhD,MAAKiQ,IAAU,CAC1CA,QACAE,WAAYC,SAOtB,SAASW,EAAiBrU,EAASuT,GACjC,OAAmC,MAA5BvT,EAAQqU,sBAA2B,EAASrU,EAAQqU,iBAAiBd,EAAMA,EAAMzR,OAAS,GAAIyR,GAEvG,SAASe,EAAqBtU,EAASuT,GACrC,OAAuC,MAAhCvT,EAAQsU,0BAA+B,EAAStU,EAAQsU,qBAAqBf,EAAM,GAAIA,GAOhG,SAASgB,EAAYvU,EAASuT,GAC5B,GAAIvT,EAAQqU,kBAAoBhS,MAAMC,QAAQiR,GAAQ,CACpD,MAAMiB,EAAgBH,EAAiBrU,EAASuT,GAChD,OAAO,MAAOiB,IAA6E,IAAlBA,GAU7E,SAASC,EAAgBzU,EAASuT,GAChC,GAAIvT,EAAQsU,sBAAwBjS,MAAMC,QAAQiR,GAAQ,CACxD,MAAMmB,EAAoBJ,EAAqBtU,EAASuT,GACxD,OAAO,MAAOmB,IAAyF,IAAtBA,GCpIrF,MAAMC,WAAsBnY,EAC1BC,YAAYgT,EAAQzP,GAClB6D,QACAnH,KAAK+S,OAASA,EACd/S,KAAKsD,QAAUA,EACftD,KAAKkY,aAAe,IAAIhY,IACxBF,KAAKmY,YAAc,KACnBnY,KAAKoY,cACLpY,KAAKwN,WAAWlK,GAGlB8U,cACEpY,KAAK0O,OAAS1O,KAAK0O,OAAOtO,KAAKJ,MAC/BA,KAAK6P,QAAU7P,KAAK6P,QAAQzP,KAAKJ,MAGnCQ,cAC8B,IAAxBR,KAAKC,UAAUW,OACjBZ,KAAKqY,aAAapI,YAAYjQ,MAE1BsY,GAAmBtY,KAAKqY,aAAcrY,KAAKsD,UAC7CtD,KAAKuY,eAGPvY,KAAKwY,gBAIT9X,gBACOV,KAAKW,gBACRX,KAAK4M,UAIToD,yBACE,OAAOyI,GAAczY,KAAKqY,aAAcrY,KAAKsD,QAAStD,KAAKsD,QAAQoV,oBAGrE9I,2BACE,OAAO6I,GAAczY,KAAKqY,aAAcrY,KAAKsD,QAAStD,KAAKsD,QAAQqV,sBAGrE/L,UACE5M,KAAKC,UAAY,IAAIC,IACrBF,KAAK4Y,oBACL5Y,KAAK6Y,uBACL7Y,KAAKqY,aAAalI,eAAenQ,MAGnCwN,WAAWlK,EAASwV,GAClB,MAAMC,EAAc/Y,KAAKsD,QACnB0V,EAAYhZ,KAAKqY,aAevB,GAdArY,KAAKsD,QAAUtD,KAAK+S,OAAOG,oBAAoB5P,GAM1CoC,EAAoBqT,EAAa/Y,KAAKsD,UACzCtD,KAAK+S,OAAOkG,gBAAgB/I,OAAO,CACjCnN,KAAM,yBACND,MAAO9C,KAAKqY,aACZjJ,SAAUpP,YAIsB,IAAzBA,KAAKsD,QAAQ+L,SAA2D,kBAAzBrP,KAAKsD,QAAQ+L,QACrE,MAAM,IAAIgD,MAAM,oCAIbrS,KAAKsD,QAAQjB,WAChBrC,KAAKsD,QAAQjB,SAAW0W,EAAY1W,UAGtCrC,KAAKkZ,cACL,MAAMC,EAAUnZ,KAAKW,eAEjBwY,GAAWC,GAAsBpZ,KAAKqY,aAAcW,EAAWhZ,KAAKsD,QAASyV,IAC/E/Y,KAAKuY,eAIPvY,KAAKqZ,aAAaP,IAEdK,GAAYnZ,KAAKqY,eAAiBW,GAAahZ,KAAKsD,QAAQ+L,UAAY0J,EAAY1J,SAAWrP,KAAKsD,QAAQ3B,YAAcoX,EAAYpX,WACxI3B,KAAKsZ,qBAGP,MAAMC,EAAsBvZ,KAAKwZ,0BAE7BL,GAAYnZ,KAAKqY,eAAiBW,GAAahZ,KAAKsD,QAAQ+L,UAAY0J,EAAY1J,SAAWkK,IAAwBvZ,KAAKyZ,wBAC9HzZ,KAAK0Z,sBAAsBH,GAI/BI,oBAAoBrW,GAClB,MAAMR,EAAQ9C,KAAK+S,OAAOkG,gBAAgBnG,MAAM9S,KAAK+S,OAAQzP,GACvDoB,EAAS1E,KAAK4Z,aAAa9W,EAAOQ,GAuBxC,OAqaJ,SAA+C8L,EAAUyK,EAAkBvW,GAOzE,GAAIA,EAAQwW,iBACV,OAAO,EAKT,QAAgCnS,IAA5BrE,EAAQyW,gBAIV,OAAOF,EAAiBG,kBAK1B,IAAKtU,EAAoB0J,EAASI,mBAAoBqK,GACpD,OAAO,EAIT,OAAO,EArdDI,CAAsCja,KAAM0E,EAAQpB,KAgBtDtD,KAAKka,cAAgBxV,EACrB1E,KAAKma,qBAAuBna,KAAKsD,QACjCtD,KAAKoa,mBAAqBpa,KAAKqY,aAAa3U,OAGvCgB,EAGT8K,mBACE,OAAOxP,KAAKka,cAGdG,YAAY3V,GACV,MAAM4V,EAAgB,GAWtB,OAVAhW,OAAOC,KAAKG,GAAQqD,SAAQpD,IAC1BL,OAAO2M,eAAeqJ,EAAe3V,EAAK,CACxC4V,cAAc,EACdrJ,YAAY,EACZC,IAAK,KACHnR,KAAKkY,aAAa3X,IAAIoE,GACfD,EAAOC,SAIb2V,EAGTE,kBACE,OAAOxa,KAAKqY,aAGd3J,SACE1O,KAAK+S,OAAOkG,gBAAgBvK,OAAO1O,KAAKqY,cAG1CxI,SAAQ0G,YACNA,KACGjT,GACD,IACF,OAAOtD,KAAKqQ,MAAM,IAAK/M,EACrBmL,KAAM,CACJ8H,iBAKNkE,gBAAgBnX,GACd,MAAMoX,EAAmB1a,KAAK+S,OAAOG,oBAAoB5P,GACnDR,EAAQ9C,KAAK+S,OAAOkG,gBAAgBnG,MAAM9S,KAAK+S,OAAQ2H,GAE7D,OADA5X,EAAMkP,sBAAuB,EACtBlP,EAAMuN,QAAQzJ,MAAK,IAAM5G,KAAK4Z,aAAa9W,EAAO4X,KAG3DrK,MAAMC,GACJ,IAAIqK,EAEJ,OAAO3a,KAAKuY,aAAa,IAAKjI,EAC5BR,cAAuE,OAAvD6K,EAAwBrK,EAAaR,gBAAyB6K,IAC7E/T,MAAK,KACN5G,KAAKqZ,eACErZ,KAAKka,iBAIhB3B,aAAajI,GAEXtQ,KAAKkZ,cAEL,IAAItP,EAAU5J,KAAKqY,aAAahI,MAAMrQ,KAAKsD,QAASgN,GAMpD,OAJsB,MAAhBA,GAAwBA,EAAasK,eACzChR,EAAUA,EAAQgB,MAAM7J,IAGnB6I,EAGT0P,qBAGE,GAFAtZ,KAAK4Y,oBAED/X,GAAYb,KAAKka,cAAczW,UAAYzC,EAAehB,KAAKsD,QAAQ3B,WACzE,OAGF,MAGM2E,EAHO7E,EAAezB,KAAKka,cAAcjM,cAAejO,KAAKsD,QAAQ3B,WAGpD,EACvB3B,KAAK6a,eAAiBpU,YAAW,KAC1BzG,KAAKka,cAAczW,SACtBzD,KAAKqZ,iBAEN/S,GAGLkT,yBACE,IAAIsB,EAEJ,MAA+C,mBAAjC9a,KAAKsD,QAAQyX,gBAAiC/a,KAAKsD,QAAQyX,gBAAgB/a,KAAKka,cAAcnT,KAAM/G,KAAKqY,cAA0E,OAAzDyC,EAAwB9a,KAAKsD,QAAQyX,kBAA2BD,EAG1MpB,sBAAsBsB,GACpBhb,KAAK6Y,uBACL7Y,KAAKyZ,uBAAyBuB,GAE1Bna,IAAqC,IAAzBb,KAAKsD,QAAQ+L,SAAsBrO,EAAehB,KAAKyZ,yBAA2D,IAAhCzZ,KAAKyZ,yBAIvGzZ,KAAKib,kBAAoBC,aAAY,MAC/Blb,KAAKsD,QAAQ6X,6BAA+BjU,EAAac,cAC3DhI,KAAKuY,iBAENvY,KAAKyZ,yBAGVjB,eACExY,KAAKsZ,qBACLtZ,KAAK0Z,sBAAsB1Z,KAAKwZ,0BAGlCZ,oBACM5Y,KAAK6a,iBACPzN,aAAapN,KAAK6a,gBAClB7a,KAAK6a,oBAAiBlT,GAI1BkR,uBACM7Y,KAAKib,oBACPG,cAAcpb,KAAKib,mBACnBjb,KAAKib,uBAAoBtT,GAI7BiS,aAAa9W,EAAOQ,GAClB,MAAM0V,EAAYhZ,KAAKqY,aACjBU,EAAc/Y,KAAKsD,QACnB+X,EAAarb,KAAKka,cAClBoB,EAAkBtb,KAAKoa,mBACvBmB,EAAoBvb,KAAKma,qBACzBqB,EAAc1Y,IAAUkW,EACxByC,EAAoBD,EAAc1Y,EAAMY,MAAQ1D,KAAK0b,yBACrDC,EAAkBH,EAAcxb,KAAKka,cAAgBla,KAAK4b,qBAC1DlY,MACJA,GACEZ,EACJ,IASIiE,GATAkH,cACFA,EAAatD,MACbA,EAAKwD,eACLA,EAAclL,YACdA,EAAWc,OACXA,GACEL,EACAmY,GAAiB,EACjB7B,GAAoB,EAGxB,GAAI1W,EAAQwY,mBAAoB,CAC9B,MAAM3C,EAAUnZ,KAAKW,eACfob,GAAgB5C,GAAWb,GAAmBxV,EAAOQ,GACrD0Y,EAAkB7C,GAAWC,GAAsBtW,EAAOkW,EAAW1V,EAASyV,IAEhFgD,GAAgBC,KAClB/Y,EAAc8F,EAASjG,EAAMQ,QAAQ0F,aAAe,WAAa,SAE5DiF,IACHlK,EAAS,YAIsB,gBAA/BT,EAAQwY,qBACV7Y,EAAc,QAKlB,GAAIK,EAAQwW,mBAAqBpW,EAAMuK,eAAoC,MAAnB0N,GAA2BA,EAAgBM,WAAwB,UAAXlY,EAC9GgD,EAAO4U,EAAgB5U,KACvBkH,EAAgB0N,EAAgB1N,cAChClK,EAAS4X,EAAgB5X,OACzB8X,GAAiB,OAEd,GAAIvY,EAAQ4Y,aAAgC,IAAfxY,EAAMqD,KAEtC,GAAIsU,GAAc3X,EAAMqD,QAA6B,MAAnBuU,OAA0B,EAASA,EAAgBvU,OAASzD,EAAQ4Y,SAAWlc,KAAKmc,SACpHpV,EAAO/G,KAAKoc,kBAEZ,IACEpc,KAAKmc,SAAW7Y,EAAQ4Y,OACxBnV,EAAOzD,EAAQ4Y,OAAOxY,EAAMqD,MAC5BA,EAAOF,EAA0B,MAAdwU,OAAqB,EAASA,EAAWtU,KAAMA,EAAMzD,GACxEtD,KAAKoc,aAAerV,EACpB/G,KAAKmY,YAAc,KACnB,MAAOA,GAKPnY,KAAKmY,YAAcA,OAKvBpR,EAAOrD,EAAMqD,KAIf,QAAuC,IAA5BzD,EAAQyW,sBAAmD,IAAThT,GAAmC,YAAXhD,EAAsB,CACzG,IAAIgW,EAEJ,GAAkB,MAAdsB,GAAsBA,EAAWrB,mBAAqB1W,EAAQyW,mBAA0C,MAArBwB,OAA4B,EAASA,EAAkBxB,iBAC5IA,EAAkBsB,EAAWtU,UAI7B,GAFAgT,EAAqD,mBAA5BzW,EAAQyW,gBAAiCzW,EAAQyW,kBAAoBzW,EAAQyW,gBAElGzW,EAAQ4Y,aAAqC,IAApBnC,EAC3B,IACEA,EAAkBzW,EAAQ4Y,OAAOnC,GACjC/Z,KAAKmY,YAAc,KACnB,MAAOA,GAKPnY,KAAKmY,YAAcA,OAKM,IAApB4B,IACThW,EAAS,UACTgD,EAAOF,EAA0B,MAAdwU,OAAqB,EAASA,EAAWtU,KAAMgT,EAAiBzW,GACnF0W,GAAoB,GAIpBha,KAAKmY,cACPxN,EAAQ3K,KAAKmY,YACbpR,EAAO/G,KAAKoc,aACZjO,EAAiBrM,KAAKC,MACtBgC,EAAS,SAGX,MAAMsY,EAA6B,aAAhBpZ,EACbqZ,EAAuB,YAAXvY,EACZwY,EAAqB,UAAXxY,EA4BhB,MA3Be,CACbA,SACAd,cACAqZ,YACAL,UAAsB,YAAXlY,EACXwY,UACAC,iBAAkBF,GAAaD,EAC/BtV,OACAkH,gBACAtD,QACAwD,iBACAtF,aAAcnF,EAAM0K,kBACpBmH,cAAe7R,EAAM2K,mBACrBH,iBAAkBxK,EAAMwK,iBACxBuO,UAAW/Y,EAAMsK,gBAAkB,GAAKtK,EAAMwK,iBAAmB,EACjEwO,oBAAqBhZ,EAAMsK,gBAAkByN,EAAkBzN,iBAAmBtK,EAAMwK,iBAAmBuN,EAAkBvN,iBAC7HmO,aACAM,aAAcN,IAAeC,EAC7BM,eAAgBL,GAAmC,IAAxB7Y,EAAMuK,cACjCuH,SAA0B,WAAhBvS,EACV+W,oBACA6B,iBACAgB,eAAgBN,GAAmC,IAAxB7Y,EAAMuK,cACjCxK,QAASA,GAAQX,EAAOQ,GACxBuM,QAAS7P,KAAK6P,QACdnB,OAAQ1O,KAAK0O,QAKjB2K,aAAaP,GACX,MAAMuC,EAAarb,KAAKka,cAClB4C,EAAa9c,KAAK4Z,aAAa5Z,KAAKqY,aAAcrY,KAAKsD,SAI7D,GAHAtD,KAAKoa,mBAAqBpa,KAAKqY,aAAa3U,MAC5C1D,KAAKma,qBAAuBna,KAAKsD,QAE7BoC,EAAoBoX,EAAYzB,GAClC,OAGFrb,KAAKka,cAAgB4C,EAErB,MAAMC,EAAuB,CAC3BrP,OAAO,IA8B0D,KAA7C,MAAjBoL,OAAwB,EAASA,EAAc7Y,YA3BtB,MAC5B,IAAKob,EACH,OAAO,EAGT,MAAM2B,oBACJA,GACEhd,KAAKsD,QACH2Z,EAA0D,mBAAxBD,EAAqCA,IAAwBA,EAErG,GAAiC,QAA7BC,IAAuCA,IAA6Bjd,KAAKkY,aAAatX,KACxF,OAAO,EAGT,MAAMsc,EAAgB,IAAIhd,IAAgC,MAA5B+c,EAAmCA,EAA2Bjd,KAAKkY,cAMjG,OAJIlY,KAAKsD,QAAQ6Z,kBACfD,EAAc3c,IAAI,SAGb+D,OAAOC,KAAKvE,KAAKka,eAAenV,MAAKJ,IAC1C,MAAMyY,EAAWzY,EAEjB,OADgB3E,KAAKka,cAAckD,KAAc/B,EAAW+B,IAC1CF,EAAcG,IAAID,OAIoCE,KAC1EP,EAAqB9c,WAAY,GAGnCD,KAAKkQ,OAAO,IAAK6M,KACZjE,IAIPI,cACE,MAAMpW,EAAQ9C,KAAK+S,OAAOkG,gBAAgBnG,MAAM9S,KAAK+S,OAAQ/S,KAAKsD,SAElE,GAAIR,IAAU9C,KAAKqY,aACjB,OAGF,MAAMW,EAAYhZ,KAAKqY,aACvBrY,KAAKqY,aAAevV,EACpB9C,KAAK0b,yBAA2B5Y,EAAMY,MACtC1D,KAAK4b,oBAAsB5b,KAAKka,cAE5Bla,KAAKW,iBACM,MAAbqY,GAA6BA,EAAU7I,eAAenQ,MACtD8C,EAAMmN,YAAYjQ,OAItB0S,cAAcJ,GACZ,MAAMwG,EAAgB,GAEF,YAAhBxG,EAAOvP,KACT+V,EAAc9O,WAAasI,EAAOxD,OACT,UAAhBwD,EAAOvP,MAAqBqG,EAAiBkJ,EAAO3H,SAC7DmO,EAAc5O,SAAU,GAG1BlK,KAAKqZ,aAAaP,GAEd9Y,KAAKW,gBACPX,KAAKwY,eAITtI,OAAO4I,GACLlN,EAAcS,OAAM,KAGhB,IAAI+H,EAAuBS,EAAeL,EAAuBH,EADnE,GAAIyE,EAAc9O,UAGsD,OAArEoK,GAAyBS,EAAgB7U,KAAKsD,SAAS0G,YAA8BoK,EAAsBhO,KAAKyO,EAAe7U,KAAKka,cAAcnT,MAC5E,OAAtEyN,GAAyBH,EAAiBrU,KAAKsD,SAASyO,YAA8ByC,EAAsBpO,KAAKiO,EAAgBrU,KAAKka,cAAcnT,KAAM,WACtJ,GAAI+R,EAAc5O,QAAS,CAChC,IAAI+K,EAAuBR,EAAgBY,EAAwBH,EAEE,OAApED,GAAyBR,EAAiBzU,KAAKsD,SAAS4G,UAA4B+K,EAAsB7O,KAAKqO,EAAgBzU,KAAKka,cAAcvP,OAC3E,OAAvE0K,GAA0BH,EAAiBlV,KAAKsD,SAASyO,YAA8BsD,EAAuBjP,KAAK8O,OAAgBvN,EAAW3H,KAAKka,cAAcvP,OAIhKmO,EAAc7Y,WAChBD,KAAKC,UAAU8H,SAAQ,EACrB1H,eAEAA,EAASL,KAAKka,kBAKdpB,EAAcpL,OAChB1N,KAAK+S,OAAOkG,gBAAgB/I,OAAO,CACjCpN,MAAO9C,KAAKqY,aACZtV,KAAM,+BAYhB,SAASuV,GAAmBxV,EAAOQ,GACjC,OALF,SAA2BR,EAAOQ,GAChC,SAA2B,IAApBA,EAAQ+L,SAAsBvM,EAAMY,MAAMuK,eAA0C,UAAvBnL,EAAMY,MAAMK,SAA+C,IAAzBT,EAAQia,cAIvGC,CAAkB1a,EAAOQ,IAAYR,EAAMY,MAAMuK,cAAgB,GAAKwK,GAAc3V,EAAOQ,EAASA,EAAQma,gBAGrH,SAAShF,GAAc3V,EAAOQ,EAASoa,GACrC,IAAwB,IAApBpa,EAAQ+L,QAAmB,CAC7B,MAAMpO,EAAyB,mBAAVyc,EAAuBA,EAAM5a,GAAS4a,EAC3D,MAAiB,WAAVzc,IAAgC,IAAVA,GAAmBwC,GAAQX,EAAOQ,GAGjE,OAAO,EAGT,SAAS8V,GAAsBtW,EAAOkW,EAAW1V,EAASyV,GACxD,OAA2B,IAApBzV,EAAQ+L,UAAsBvM,IAAUkW,IAAqC,IAAxBD,EAAY1J,YAAwB/L,EAAQqa,UAAmC,UAAvB7a,EAAMY,MAAMK,SAAuBN,GAAQX,EAAOQ,GAGxK,SAASG,GAAQX,EAAOQ,GACtB,OAAOR,EAAM2M,cAAcnM,EAAQ3B,WC1hBrC,MAAMic,WAAwB9d,EAC5BC,YAAYgT,EAAQH,GAClBzL,QACAnH,KAAK+S,OAASA,EACd/S,KAAK4S,QAAU,GACf5S,KAAK0E,OAAS,GACd1E,KAAKyN,UAAY,GACjBzN,KAAK6d,aAAe,GAEhBjL,GACF5S,KAAK8d,WAAWlL,GAIpBpS,cAC8B,IAAxBR,KAAKC,UAAUW,MACjBZ,KAAKyN,UAAU1F,SAAQqH,IACrBA,EAASjP,WAAUuE,IACjB1E,KAAK+d,SAAS3O,EAAU1K,SAMhChE,gBACOV,KAAKC,UAAUW,MAClBZ,KAAK4M,UAITA,UACE5M,KAAKC,UAAY,IAAIC,IACrBF,KAAKyN,UAAU1F,SAAQqH,IACrBA,EAASxC,aAIbkR,WAAWlL,EAASkG,GAClB9Y,KAAK4S,QAAUA,EACfhH,EAAcS,OAAM,KAClB,MAAM2R,EAAgBhe,KAAKyN,UACrBwQ,EAAqBje,KAAKke,sBAAsBle,KAAK4S,SAE3DqL,EAAmBlW,SAAQoW,GAASA,EAAM/O,SAAS5B,WAAW2Q,EAAMC,sBAAuBtF,KAC3F,MAAMuF,EAAeJ,EAAmBK,KAAIH,GAASA,EAAM/O,WACrDmP,EAAkBja,OAAOka,YAAYH,EAAaC,KAAIlP,GAAY,CAACA,EAAS9L,QAAQF,UAAWgM,MAC/FqP,EAAYJ,EAAaC,KAAIlP,GAAYA,EAASI,qBAClDkP,EAAiBL,EAAatZ,MAAK,CAACqK,EAAUuP,IAAUvP,IAAa4O,EAAcW,MAErFX,EAAc5Y,SAAWiZ,EAAajZ,QAAWsZ,KAIrD1e,KAAKyN,UAAY4Q,EACjBre,KAAK6d,aAAeU,EACpBve,KAAK0E,OAAS+Z,EAETze,KAAKW,iBAIVQ,EAAW6c,EAAeK,GAActW,SAAQqH,IAC9CA,EAASxC,aAEXzL,EAAWkd,EAAcL,GAAejW,SAAQqH,IAC9CA,EAASjP,WAAUuE,IACjB1E,KAAK+d,SAAS3O,EAAU1K,SAG5B1E,KAAKkQ,cAITV,mBACE,OAAOxP,KAAK0E,OAGdka,aACE,OAAO5e,KAAKyN,UAAU6Q,KAAIlP,GAAYA,EAASoL,oBAGjDqE,eACE,OAAO7e,KAAKyN,UAGdkM,oBAAoB/G,GAClB,OAAO5S,KAAKke,sBAAsBtL,GAAS0L,KAAIH,GAASA,EAAM/O,SAASuK,oBAAoBwE,EAAMC,yBAGnGF,sBAAsBtL,GACpB,MAAMoL,EAAgBhe,KAAKyN,UACrBqR,EAAmB,IAAIC,IAAIf,EAAcM,KAAIlP,GAAY,CAACA,EAAS9L,QAAQF,UAAWgM,MACtFgP,EAAwBxL,EAAQ0L,KAAIhb,GAAWtD,KAAK+S,OAAOG,oBAAoB5P,KAC/E0b,EAAoBZ,EAAsBa,SAAQvE,IACtD,MAAMyD,EAAQW,EAAiB3N,IAAIuJ,EAAiBtX,WAEpD,OAAa,MAAT+a,EACK,CAAC,CACNC,sBAAuB1D,EACvBtL,SAAU+O,IAIP,MAEHe,EAAqB,IAAIhf,IAAI8e,EAAkBV,KAAIH,GAASA,EAAMC,sBAAsBhb,aACxF+b,EAAmBf,EAAsB9c,QAAOoZ,IAAqBwE,EAAmB7B,IAAI3C,EAAiBtX,aAC7Ggc,EAAuB,IAAIlf,IAAI8e,EAAkBV,KAAIH,GAASA,EAAM/O,YACpEiQ,EAAqBrB,EAAc1c,QAAOge,IAAiBF,EAAqB/B,IAAIiC,KAEpFC,EAAcjc,IAClB,MAAMoX,EAAmB1a,KAAK+S,OAAOG,oBAAoB5P,GACnDkc,EAAkBxf,KAAK6d,aAAanD,EAAiBtX,WAC3D,OAA0B,MAAnBoc,EAA0BA,EAAkB,IAAIvH,GAAcjY,KAAK+S,OAAQ2H,IAG9E+E,EAAuBN,EAAiBb,KAAI,CAAChb,EAASqb,KAC1D,GAAIrb,EAAQwW,iBAAkB,CAE5B,MAAM4F,EAAyBL,EAAmBV,GAElD,QAA+BhX,IAA3B+X,EACF,MAAO,CACLtB,sBAAuB9a,EACvB8L,SAAUsQ,GAKhB,MAAO,CACLtB,sBAAuB9a,EACvB8L,SAAUmQ,EAAYjc,OAM1B,OAAO0b,EAAkBW,OAAOF,GAAsBjb,MAFlB,CAACI,EAAGC,IAAMuZ,EAAsBwB,QAAQhb,EAAEwZ,uBAAyBA,EAAsBwB,QAAQ/a,EAAEuZ,yBAKzIL,SAAS3O,EAAU1K,GACjB,MAAMia,EAAQ3e,KAAKyN,UAAUmS,QAAQxQ,IAEtB,IAAXuP,IACF3e,KAAK0E,ObtIX,SAAmBO,EAAO0Z,EAAO1d,GAC/B,MAAMsE,EAAON,EAAM4a,MAAM,GAEzB,OADAta,EAAKoZ,GAAS1d,EACPsE,EamIWua,CAAU9f,KAAK0E,OAAQia,EAAOja,GAC5C1E,KAAKkQ,UAITA,SACEtE,EAAcS,OAAM,KAClBrM,KAAKC,UAAU8H,SAAQ,EACrB1H,eAEAA,EAASL,KAAK0E,eC5JtB,MAAMqb,WAA8B9H,GAKlClY,YAAYgT,EAAQzP,GAClB6D,MAAM4L,EAAQzP,GAGhB8U,cACEjR,MAAMiR,cACNpY,KAAKggB,cAAgBhgB,KAAKggB,cAAc5f,KAAKJ,MAC7CA,KAAKigB,kBAAoBjgB,KAAKigB,kBAAkB7f,KAAKJ,MAGvDwN,WAAWlK,EAASwV,GAClB3R,MAAMqG,WAAW,IAAKlK,EACpBkO,SAAU0E,KACT4C,GAGLa,oBAAoBrW,GAElB,OADAA,EAAQkO,SAAW0E,IACZ/O,MAAMwS,oBAAoBrW,GAGnC0c,eAAclP,UACZA,KACGxN,GACD,IACF,OAAOtD,KAAKqQ,MAAM,IAAK/M,EACrBmL,KAAM,CACJ+H,UAAW,CACTE,UAAW,UACX5F,gBAMRmP,mBAAkBnP,UAChBA,KACGxN,GACD,IACF,OAAOtD,KAAKqQ,MAAM,IAAK/M,EACrBmL,KAAM,CACJ+H,UAAW,CACTE,UAAW,WACX5F,gBAMR8I,aAAa9W,EAAOQ,GAClB,IAAI4c,EAAkBC,EAAuBC,EAAmBC,EAAuBC,EAAaC,EAEpG,MAAM7c,MACJA,GACEZ,EACE4B,EAASyC,MAAMyS,aAAa9W,EAAOQ,IACnC+Y,WACJA,EAAUM,aACVA,GACEjY,EACE+R,EAAqB4F,GAAoL,aAA7H,OAAvC6D,EAAmBxc,EAAM4K,YAAsF,OAAvD6R,EAAwBD,EAAiB1J,gBAAnD,EAAiF2J,EAAsBzJ,WAC1LC,EAAyB0F,GAAsL,cAA9H,OAAxC+D,EAAoB1c,EAAM4K,YAAuF,OAAxD+R,EAAwBD,EAAkB5J,gBAApD,EAAkF6J,EAAsB3J,WACtM,MAAO,IAAKhS,EACVsb,cAAehgB,KAAKggB,cACpBC,kBAAmBjgB,KAAKigB,kBACxBpI,YAAaA,EAAYvU,EAAuC,OAA7Bgd,EAAc5c,EAAMqD,WAAgB,EAASuZ,EAAYzJ,OAC5FkB,gBAAiBA,EAAgBzU,EAAwC,OAA9Bid,EAAe7c,EAAMqD,WAAgB,EAASwZ,EAAa1J,OACtGJ,qBACAE,yBACAgG,aAAcA,IAAiBlG,IAAuBE,ICvE5D,MAAM6J,WAAyB1gB,EAC7BC,YAAYgT,EAAQzP,GAClB6D,QACAnH,KAAK+S,OAASA,EACd/S,KAAKwN,WAAWlK,GAChBtD,KAAKoY,cACLpY,KAAKqZ,eAGPjB,cACEpY,KAAKygB,OAASzgB,KAAKygB,OAAOrgB,KAAKJ,MAC/BA,KAAKmP,MAAQnP,KAAKmP,MAAM/O,KAAKJ,MAG/BwN,WAAWlK,GACT,IAAIod,EAEJ,MAAM3H,EAAc/Y,KAAKsD,QACzBtD,KAAKsD,QAAUtD,KAAK+S,OAAO6C,uBAAuBtS,GAE7CoC,EAAoBqT,EAAa/Y,KAAKsD,UACzCtD,KAAK+S,OAAO4N,mBAAmBzQ,OAAO,CACpCnN,KAAM,yBACNa,SAAU5D,KAAK4gB,gBACfxR,SAAUpP,OAIoC,OAAjD0gB,EAAwB1gB,KAAK4gB,kBAAoCF,EAAsBlT,WAAWxN,KAAKsD,SAG1G5C,gBAEI,IAAImgB,EADD7gB,KAAKW,iBAG2C,OAAlDkgB,EAAyB7gB,KAAK4gB,kBAAoCC,EAAuB1Q,eAAenQ,OAI7GyV,iBAAiBnD,GACftS,KAAKqZ,eAEL,MAAMP,EAAgB,CACpB7Y,WAAW,GAGO,YAAhBqS,EAAOvP,KACT+V,EAAc9O,WAAY,EACD,UAAhBsI,EAAOvP,OAChB+V,EAAc5O,SAAU,GAG1BlK,KAAKkQ,OAAO4I,GAGdtJ,mBACE,OAAOxP,KAAKka,cAGd/K,QACEnP,KAAK4gB,qBAAkBjZ,EACvB3H,KAAKqZ,eACLrZ,KAAKkQ,OAAO,CACVjQ,WAAW,IAIfwgB,OAAOzM,EAAW1Q,GAWhB,OAVAtD,KAAK8gB,cAAgBxd,EAEjBtD,KAAK4gB,iBACP5gB,KAAK4gB,gBAAgBzQ,eAAenQ,MAGtCA,KAAK4gB,gBAAkB5gB,KAAK+S,OAAO4N,mBAAmB7N,MAAM9S,KAAK+S,OAAQ,IAAK/S,KAAKsD,QACjF0Q,eAAgC,IAAdA,EAA4BA,EAAYhU,KAAKsD,QAAQ0Q,YAEzEhU,KAAK4gB,gBAAgB3Q,YAAYjQ,MAC1BA,KAAK4gB,gBAAgBhN,UAG9ByF,eACE,MAAM3V,EAAQ1D,KAAK4gB,gBAAkB5gB,KAAK4gB,gBAAgBld,MN+JrD,CACL2N,aAAS1J,EACTZ,UAAMY,EACNgD,MAAO,KACP9B,aAAc,EACd0M,cAAe,KACfC,UAAU,EACVzR,OAAQ,OACRiQ,eAAWrM,GMtKL2U,EAA6B,YAAjB5Y,EAAMK,OAClBW,EAAS,IAAKhB,EAClB4Y,YACAyE,UAAWzE,EACXL,UAA4B,YAAjBvY,EAAMK,OACjBwY,QAA0B,UAAjB7Y,EAAMK,OACfid,OAAyB,SAAjBtd,EAAMK,OACd0c,OAAQzgB,KAAKygB,OACbtR,MAAOnP,KAAKmP,OAEdnP,KAAKka,cAAgBxV,EAGvBwL,OAAO5M,GACLsI,EAAcS,OAAM,KAId,IAAI4U,EAAuBC,EAAqBC,EAAwBC,EAF5E,GAAIphB,KAAK8gB,eAAiB9gB,KAAKW,eAC7B,GAAI2C,EAAQ0G,UAGwE,OAAjFiX,GAAyBC,EAAsBlhB,KAAK8gB,eAAe9W,YAA8BiX,EAAsB7a,KAAK8a,EAAqBlhB,KAAKka,cAAcnT,KAAM/G,KAAKka,cAAclG,UAAWhU,KAAKka,cAAc7I,SACxI,OAAnF8P,GAA0BC,EAAuBphB,KAAK8gB,eAAe/O,YAA8BoP,EAAuB/a,KAAKgb,EAAsBphB,KAAKka,cAAcnT,KAAM,KAAM/G,KAAKka,cAAclG,UAAWhU,KAAKka,cAAc7I,cACjO,GAAI/N,EAAQ4G,QAAS,CAC1B,IAAImX,EAAwBC,EAAsBC,EAAwBC,EAEQ,OAAjFH,GAA0BC,EAAuBthB,KAAK8gB,eAAe5W,UAA4BmX,EAAuBjb,KAAKkb,EAAsBthB,KAAKka,cAAcvP,MAAO3K,KAAKka,cAAclG,UAAWhU,KAAKka,cAAc7I,SAC3I,OAAnFkQ,GAA0BC,EAAuBxhB,KAAK8gB,eAAe/O,YAA8BwP,EAAuBnb,KAAKob,OAAsB7Z,EAAW3H,KAAKka,cAAcvP,MAAO3K,KAAKka,cAAclG,UAAWhU,KAAKka,cAAc7I,SAK5O/N,EAAQrD,WACVD,KAAKC,UAAU8H,SAAQ,EACrB1H,eAEAA,EAASL,KAAKka,sBCvGxB,SAASuH,GAA+B7d,GACtC,OAAOA,EAASF,MAAM8R,SAExB,SAASkM,GAA4B5e,GACnC,MAA8B,YAAvBA,EAAMY,MAAMK,OA6BrB,SAAS4d,GAAQ5O,EAAQ6O,EAAiBte,GACxC,GAA+B,iBAApBse,GAAoD,OAApBA,EACzC,OAGF,MAAMlO,EAAgBX,EAAO4N,mBACvBkB,EAAa9O,EAAOkG,gBAEpBtD,EAAYiM,EAAgBjM,WAAa,GAEzC/C,EAAUgP,EAAgBhP,SAAW,GAC3C+C,EAAU5N,SAAQ+Z,IAChB,IAAIC,EAEJrO,EAAcZ,MAAMC,EAAQ,IAAiB,MAAXzP,GAA+E,OAAnDye,EAAwBze,EAAQiK,qBAA1C,EAA6EwU,EAAsBpM,UACrJnT,YAAasf,EAAmBtf,aAC/Bsf,EAAmBpe,UAExBkP,EAAQ7K,SAAQ,EACd1F,WACAqB,QACAN,gBAEA,IAAI4e,EAEJ,MAAMlf,EAAQ+e,EAAW1Q,IAAI/N,GAE7B,GAAIN,GACF,GAAIA,EAAMY,MAAMuK,cAAgBvK,EAAMuK,cAAe,CAGnD,MACEhL,YAAagf,KACVC,GACDxe,EACJZ,EAAMiM,SAASmT,SAOnBL,EAAW/O,MAAMC,EAAQ,IAAiB,MAAXzP,GAAgF,OAApD0e,EAAyB1e,EAAQiK,qBAA3C,EAA8EyU,EAAuBpP,QACpJvQ,WACAe,aAGF,IAAKM,EACHT,YAAa,YCnGZ,MAAMkf,GAA0BC,EAASD,wBCAhDvW,EAAca,uBAAuB0V,8CCAnCE,EAAAC,wCCMW,IAAIC,EAAEC,EAAgB,QAAoEC,EAAE,mBAAoBne,OAAOoe,GAAGpe,OAAOoe,GAA1G,SAAW9d,EAAEC,GAAG,OAAOD,IAAIC,IAAI,IAAID,GAAG,EAAEA,GAAI,EAAEC,IAAID,GAAIA,GAAGC,GAAIA,GAAkD8d,EAAEJ,EAAEK,SAASC,EAAEN,EAAEO,UAAUC,EAAER,EAAES,gBAAgBC,EAAEV,EAAEW,cACtM,SAASC,EAAEve,GAAG,IAAIC,EAAED,EAAEwe,YAAYxe,EAAEA,EAAE3D,MAAM,IAAI,IAAIoiB,EAAExe,IAAI,OAAO4d,EAAE7d,EAAEye,GAAG,MAAMC,GAAG,OAAM,GAA+B,IAAIC,EAAE,oBAAqBziB,aAAQ,IAAqBA,OAAOmH,eAAU,IAAqBnH,OAAOmH,SAASub,cAAzI,SAAW5e,EAAEC,GAAG,OAAOA,KADkG,SAAWD,EAAEC,GAAG,IAAIwe,EAAExe,IAAIye,EAAEX,EAAE,CAACc,KAAK,CAACxiB,MAAMoiB,EAAED,YAAYve,KAAK6e,EAAEJ,EAAE,GAAGG,KAAKE,EAAEL,EAAE,GAAwJ,OAArJP,GAAE,WAAWW,EAAEziB,MAAMoiB,EAAEK,EAAEN,YAAYve,EAAEse,EAAEO,IAAIC,EAAE,CAACF,KAAKC,MAAK,CAAC9e,EAAEye,EAAExe,IAAIge,GAAE,WAA6B,OAAlBM,EAAEO,IAAIC,EAAE,CAACF,KAAKC,IAAW9e,GAAE,WAAWue,EAAEO,IAAIC,EAAE,CAACF,KAAKC,SAAO,CAAC9e,IAAIqe,EAAEI,GAAUA,UAC3MO,GAA4BC,0BAAC,IAAStB,EAAEsB,qBAAqBtB,EAAEsB,qBAAqBN,KDPvTf,QECZ,MAAMqB,GAAuBC,GAA7BxB,QAAAuB,qBCQME,GAAiBC,EAAMC,mBAClCtc,GAEIuc,GAA4BF,EAAMC,eAAuB,GAS/D,SAASE,GACP9S,EACA+S,GAEA,OAAI/S,IAGA+S,GAAoC,oBAAXtjB,QACtBA,OAAOujB,0BACVvjB,OAAOujB,wBAA0BN,IAG5BjjB,OAAOujB,yBAGTN,IAGIO,MAAAA,GAAiB,EAAGjT,WAA4B,MAC3D,MAAMkT,EAAcP,EAAMQ,WACxBL,GAAsB9S,EAAS2S,EAAMQ,WAAWN,MAGlD,IAAKK,EACH,MAAM,IAAIlS,MAAM,0DAGlB,OAAOkS,GChDHE,GAAqBT,EAAMC,eAAc,GAElCS,GAAiB,IAAMV,EAAMQ,WAAWC,IACxCE,GAAsBF,GAAmBG,SCKtD,SAASC,KACP,IAAIC,GAAU,EACd,MAAO,CACLC,WAAY,KACVD,GAAU,GAEZ3V,MAAO,KACL2V,GAAU,GAEZA,QAAS,IACAA,GAKb,MAAME,GAAiChB,EAAMC,cAAcY,MAI9CI,GAA6B,IACxCjB,EAAMQ,WAAWQ,IC/BZ,SAASE,GACdC,EACAC,GAGA,MAAiC,mBAAtBD,EACFA,KAAqBC,KAGrBD,ECGJ,MAAME,GAAkC,CAO7C/hB,EAOAgiB,MAEIhiB,EAAQqa,UAAYra,EAAQ6Z,oBAEzBmI,EAAmBR,YACtBxhB,EAAQia,cAAe,KAKhBgI,GACXD,IAEAtB,EAAMlB,WAAU,KACdwC,EAAmBP,eAClB,CAACO,KAGOE,GAAc,EAOzB9gB,SACA4gB,qBACAnI,mBACAra,WAaE4B,EAAO6X,UACN+I,EAAmBR,YACnBpgB,EAAO2X,YACR6I,GAAiB/H,EAAkB,CAACzY,EAAOiG,MAAO7H,IChEzC2iB,GACX/K,IAEIA,EAAiBiD,UAGuB,iBAA/BjD,EAAiB/Y,YAC1B+Y,EAAiB/Y,UAAY,MAKtB+jB,GAAY,CACvBhhB,EACAihB,IACGjhB,EAAO4X,WAAa5X,EAAO2X,aAAesJ,EAElCC,GAAgB,CAC3BlL,EAGAhW,EACAihB,KACG,MAAAjL,OAAA,EAAAA,EAAkBiD,WAAY+H,GAAUhhB,EAAQihB,GAExClL,GAAkB,CAO7BC,EAOAtL,EACAkW,IAEAlW,EACGqL,gBAAgBC,GAChB9T,MAAK,EAAGG,WACP,MAAA2T,EAAiB1Q,WAAjB0Q,EAAiB1Q,UAAYjD,GAC7B2T,MAAAA,EAAiB3I,WAAjB2I,EAAiB3I,UAAYhL,EAAM,SAEpC6D,OAAOD,IACN2a,EAAmBP,aACnB,MAAArK,EAAiBxQ,SAAjBwQ,EAAiBxQ,QAAUS,GAC3B+P,MAAAA,EAAiB3I,WAAjB2I,EAAiB3I,eAAYpK,EAAWgD,MCyHvC,SAASkb,IAA4BjT,QAC1CA,EAD0CvB,QAE1CA,IAKA,MAAMkT,EAAcD,GAAe,CAAEjT,YAC/BsU,EAAcjB,KACdY,EAAqBL,KAErBa,EAAmB9B,EAAM+B,SAC7B,IACEnT,EAAQ0L,KAAKhb,IACX,MAAMoX,EAAmB6J,EAAYrR,oBAAoB5P,GAOzD,OAJAoX,EAAiBoB,mBAAqB6J,EAClC,cACA,aAEGjL,MAEX,CAAC9H,EAAS2R,EAAaoB,IAGzBG,EAAiB/d,SAASjF,IACxB2iB,GAAgB3iB,GAChBuiB,GAAgCviB,EAAOwiB,MAGzCC,GAA2BD,GAE3B,MAAOlW,GAAY4U,EAAMpB,UACvB,IAAM,IAAIhF,GAAgB2G,EAAauB,KAGnCjM,EAAmBzK,EAASuK,oBAAoBmM,GAEtDjC,GACEG,EAAMgC,aACHC,GACCN,EACI,OACAvW,EAASjP,UAAUyL,EAAcU,WAAW2Z,KAClD,CAAC7W,EAAUuW,KAEb,IAAMvW,EAASI,qBACf,IAAMJ,EAASI,qBAGjBwU,EAAMlB,WAAU,KAGd1T,EAAS0O,WAAWgI,EAAkB,CAAE7lB,WAAW,MAClD,CAAC6lB,EAAkB1W,IAEtB,MAIM8W,EAJ0BrM,EAAiB9U,MAAK,CAACL,EAAQia,IAC7DiH,GAAcE,EAAiBnH,GAAQja,EAAQihB,KAI7C9L,EAAiBoF,SAAQ,CAACva,EAAQia,KAChC,MAAMrb,EAAUwiB,EAAiBnH,GAC3BwH,EAAgB/W,EAASyP,eAAeF,GAE9C,GAAIrb,GAAW6iB,EAAe,CAC5B,GAAIP,GAActiB,EAASoB,EAAQihB,GACjC,OAAOlL,GAAgBnX,EAAS6iB,EAAeb,GACtCI,GAAUhhB,EAAQihB,IACtBlL,GAAgBnX,EAAS6iB,EAAeb,GAGjD,MAAO,MAET,GAEJ,GAAIY,EAAiB9gB,OAAS,EAC5B,MAAMmB,QAAQ6f,IAAIF,GAEpB,MAAMG,EAAkBjX,EAASwP,aAC3B0H,EAAoCzM,EAAiBlK,MACzD,CAACjL,EAAQia,KAAT,IAAA4H,EAAAC,EAAA,OACEhB,GAAY,CACV9gB,SACA4gB,qBACAnI,iBAAgB,OAAAoJ,EAAE,OAAFC,EAAEV,EAAiBnH,SAAjB,EAAA6H,EAAyBrJ,mBAA3BoJ,EAChBzjB,MAAOujB,EAAgB1H,QAI7B,GAAA,MAAI2H,GAAAA,EAAmC3b,MACrC,MAAM2b,EAAkC3b,MAG1C,OAAOkP,EChQF,SAAS4M,GAOdnjB,EAOAojB,GAEA,MAAMnC,EAAcD,GAAe,CAAEjT,QAAS/N,EAAQ+N,UAChDsU,EAAcjB,KACdY,EAAqBL,KACrBvK,EAAmB6J,EAAYrR,oBAAoB5P,GAGzDoX,EAAiBoB,mBAAqB6J,EAClC,cACA,aAGAjL,EAAiBxQ,UACnBwQ,EAAiBxQ,QAAU0B,EAAcU,WACvCoO,EAAiBxQ,UAIjBwQ,EAAiB1Q,YACnB0Q,EAAiB1Q,UAAY4B,EAAcU,WACzCoO,EAAiB1Q,YAIjB0Q,EAAiB3I,YACnB2I,EAAiB3I,UAAYnG,EAAcU,WACzCoO,EAAiB3I,YAIrB0T,GAAgB/K,GAChB2K,GAAgC3K,EAAkB4K,GAElDC,GAA2BD,GAE3B,MAAOlW,GAAY4U,EAAMpB,UACvB,IACE,IAAI8D,EACFnC,EACA7J,KAIAhW,EAAS0K,EAASuK,oBAAoBe,GA4B5C,GA1BAmJ,GACEG,EAAMgC,aACHC,IACC,MAAMU,EAAchB,EAChB,OACAvW,EAASjP,UAAUyL,EAAcU,WAAW2Z,IAMhD,OAFA7W,EAASiK,eAEFsN,IAET,CAACvX,EAAUuW,KAEb,IAAMvW,EAASI,qBACf,IAAMJ,EAASI,qBAGjBwU,EAAMlB,WAAU,KAGd1T,EAAS5B,WAAWkN,EAAkB,CAAEza,WAAW,MAClD,CAACya,EAAkBtL,IAGlBwW,GAAclL,EAAkBhW,EAAQihB,GAC1C,MAAMlL,GAAgBC,EAAkBtL,EAAUkW,GAIpD,GACEE,GAAY,CACV9gB,SACA4gB,qBACAnI,iBAAkBzC,EAAiByC,iBACnCra,MAAOsM,EAASoL,oBAGlB,MAAM9V,EAAOiG,MAIf,OAAQ+P,EAAiBsC,oBAErBtY,EADA0K,EAASiL,YAAY3V,GCjHpB,SAASkiB,GACdljB,EACAJ,EAA2C,IAE3C,MAAMihB,EAAcD,GAAe,CAAEjT,QAAS/N,EAAQ+N,UAEhDwV,EAAa7C,EAAM8C,OAAOxjB,GAChCujB,EAAWE,QAAUzjB,EAMrB0gB,EAAM+B,SAAQ,KACRriB,GACFie,GAAQ4C,EAAa7gB,EAAOmjB,EAAWE,WAExC,CAACxC,EAAa7gB,ICoGnB,SAAS3C,mCD3Fc,EAAGimB,WAAU1jB,UAASI,YAC3CkjB,GAAWljB,EAAOJ,GACX0jB,2JE1BT,MACEjnB,YAAYuJ,EAAS,IACnBtJ,KAAK6hB,WAAavY,EAAOuY,YAAc,IAAIlP,EAC3C3S,KAAK0T,cAAgBpK,EAAOoK,eAAiB,IAAIgC,EACjD1V,KAAK2N,OAASrE,EAAOqE,QAAUjC,EAC/B1L,KAAKuN,eAAiBjE,EAAOiE,gBAAkB,GAC/CvN,KAAKinB,cAAgB,GACrBjnB,KAAKknB,iBAAmB,GACxBlnB,KAAKmnB,WAAa,EAOpBC,QACEpnB,KAAKmnB,aACmB,IAApBnnB,KAAKmnB,aACTnnB,KAAKqnB,iBAAmBngB,EAAa/G,WAAU,KACzC+G,EAAac,cACfhI,KAAK8V,wBACL9V,KAAK6hB,WAAWxa,cAGpBrH,KAAKsnB,kBAAoBlf,EAAcjI,WAAU,KAC3CiI,EAAcK,aAChBzI,KAAK8V,wBACL9V,KAAK6hB,WAAWxZ,gBAKtBkf,UACE,IAAIC,EAAuBC,EAE3BznB,KAAKmnB,aACmB,IAApBnnB,KAAKmnB,aAC0C,OAAlDK,EAAwBxnB,KAAKqnB,mBAAqCG,EAAsBphB,KAAKpG,MAC9FA,KAAKqnB,sBAAmB1f,EAC4B,OAAnD8f,EAAwBznB,KAAKsnB,oBAAsCG,EAAsBrhB,KAAKpG,MAC/FA,KAAKsnB,uBAAoB3f,GAM3B0U,WAAWpa,EAAMC,GACf,MAAOW,GAAWH,EAAgBT,EAAMC,GAExC,OADAW,EAAQI,YAAc,WACfjD,KAAK6hB,WAAWtO,QAAQ1Q,GAASuC,OAG1CsiB,WAAW7kB,GACT,OAAO7C,KAAK0T,cAAcH,QAAQ,IAAK1Q,EACrCgB,UAAU,IACTuB,OAMLuiB,aAAatlB,EAAUQ,GACrB,IAAI+kB,EAEJ,OAA4E,OAApEA,EAAwB5nB,KAAK6hB,WAAWlS,KAAKtN,EAAUQ,SAAoB,EAAS+kB,EAAsBlkB,MAAMqD,KAM1H8gB,gBAAgB5lB,EAAMC,EAAMC,GAC1B,MAAM2lB,EAAgB9lB,EAAeC,EAAMC,EAAMC,GAC3C4lB,EAAa/nB,KAAK2nB,aAAaG,EAAczlB,UACnD,OAAO0lB,EAAaxhB,QAAQC,QAAQuhB,GAAc/nB,KAAKgoB,WAAWF,GAMpEG,eAAeC,GACb,OAAOloB,KAAKiZ,gBAAgB1F,QAAQ2U,GAAmB5J,KAAI,EACzDjc,WACAqB,WAGO,CAACrB,EADKqB,EAAMqD,QAKvBohB,aAAa9lB,EAAU+lB,EAAS9kB,GAC9B,MAAMR,EAAQ9C,KAAK6hB,WAAWlS,KAAKtN,GAE7B0E,EhChGV,SAA0BqhB,EAASC,GACjC,MAA0B,mBAAZD,EAAyBA,EAAQC,GAASD,EgC+FzCE,CAAiBF,EADJ,MAATtlB,OAAgB,EAASA,EAAMY,MAAMqD,MAGtD,QAAoB,IAATA,EACT,OAGF,MAAM+gB,EAAgB9lB,EAAeK,GAC/BqY,EAAmB1a,KAAKkT,oBAAoB4U,GAClD,OAAO9nB,KAAK6hB,WAAW/O,MAAM9S,KAAM0a,GAAkB/L,QAAQ5H,EAAM,IAAKzD,EACtEwL,QAAQ,IAOZyZ,eAAeL,EAAmBE,EAAS9kB,GACzC,OAAOsI,EAAcS,OAAM,IAAMrM,KAAKiZ,gBAAgB1F,QAAQ2U,GAAmB5J,KAAI,EACnFjc,cACI,CAACA,EAAUrC,KAAKmoB,aAAa9lB,EAAU+lB,EAAS9kB,QAGxDklB,cAAcnmB,EAIdQ,GACE,IAAI4lB,EAEJ,OAA6E,OAArEA,EAAyBzoB,KAAK6hB,WAAWlS,KAAKtN,EAAUQ,SAAoB,EAAS4lB,EAAuB/kB,MAMtHglB,cAAczmB,EAAMC,GAClB,MAAOW,GAAWH,EAAgBT,EAAMC,GAClC2f,EAAa7hB,KAAK6hB,WACxBjW,EAAcS,OAAM,KAClBwV,EAAWtO,QAAQ1Q,GAASkF,SAAQjF,IAClC+e,EAAWnT,OAAO5L,SAQxB6lB,aAAa1mB,EAAMC,EAAMC,GACvB,MAAOU,EAASS,GAAWZ,EAAgBT,EAAMC,EAAMC,GACjD0f,EAAa7hB,KAAK6hB,WAClB+G,EAAiB,CACrB7lB,KAAM,YACHF,GAEL,OAAO+I,EAAcS,OAAM,KACzBwV,EAAWtO,QAAQ1Q,GAASkF,SAAQjF,IAClCA,EAAMqM,WAEDnP,KAAK6oB,eAAeD,EAAgBtlB,MAO/CwlB,cAAc7mB,EAAMC,EAAMC,GACxB,MAAOU,EAASwI,EAAgB,IAAM3I,EAAgBT,EAAMC,EAAMC,QAE9B,IAAzBkJ,EAAcnC,SACvBmC,EAAcnC,QAAS,GAGzB,MAAM6f,EAAWnd,EAAcS,OAAM,IAAMrM,KAAK6hB,WAAWtO,QAAQ1Q,GAASyb,KAAIxb,GAASA,EAAMsI,OAAOC,OACtG,OAAO9E,QAAQ6f,IAAI2C,GAAUniB,KAAK7F,GAAM6J,MAAM7J,GAMhDioB,kBAAkB/mB,EAAMC,EAAMC,GAC5B,MAAOU,EAASS,GAAWZ,EAAgBT,EAAMC,EAAMC,GACvD,OAAOyJ,EAAcS,OAAM,KACzB,IAAI4c,EAAMC,EAMV,GAJAlpB,KAAK6hB,WAAWtO,QAAQ1Q,GAASkF,SAAQjF,IACvCA,EAAMsN,gBAGoB,SAAxBvN,EAAQsmB,YACV,OAAO5iB,QAAQC,UAGjB,MAAMoiB,EAAiB,IAAK/lB,EAC1BE,KAA6G,OAAtGkmB,EAAuD,OAA/CC,EAAuBrmB,EAAQsmB,aAAuBD,EAAuBrmB,EAAQE,MAAgBkmB,EAAO,UAE7H,OAAOjpB,KAAK6oB,eAAeD,EAAgBtlB,MAO/CulB,eAAe5mB,EAAMC,EAAMC,GACzB,MAAOU,EAASS,GAAWZ,EAAgBT,EAAMC,EAAMC,GACjD4mB,EAAWnd,EAAcS,OAAM,IAAMrM,KAAK6hB,WAAWtO,QAAQ1Q,GAASvB,QAAOwB,IAAUA,EAAMwM,eAAcgP,KAAIxb,IACnH,IAAIsmB,EAEJ,OAAOtmB,EAAMuN,WAAM1I,EAAW,IAAKrE,EACjCwM,cAA6F,OAA7EsZ,EAAmC,MAAX9lB,OAAkB,EAASA,EAAQwM,gBAAyBsZ,EACpG3a,KAAM,CACJ8H,YAAa1T,EAAQ0T,oBAI3B,IAAI3M,EAAUrD,QAAQ6f,IAAI2C,GAAUniB,KAAK7F,GAMzC,OAJiB,MAAXuC,GAAmBA,EAAQsX,eAC/BhR,EAAUA,EAAQgB,MAAM7J,IAGnB6I,EAMToe,WAAW/lB,EAAMC,EAAMC,GACrB,MAAM2lB,EAAgB9lB,EAAeC,EAAMC,EAAMC,GAC3CuY,EAAmB1a,KAAKkT,oBAAoB4U,QAEZ,IAA3BpN,EAAiB3P,QAC1B2P,EAAiB3P,OAAQ,GAG3B,MAAMjI,EAAQ9C,KAAK6hB,WAAW/O,MAAM9S,KAAM0a,GAC1C,OAAO5X,EAAM2M,cAAciL,EAAiB/Y,WAAamB,EAAMuN,MAAMqK,GAAoBnU,QAAQC,QAAQ1D,EAAMY,MAAMqD,MAMvHsiB,cAAcpnB,EAAMC,EAAMC,GACxB,OAAOnC,KAAKgoB,WAAW/lB,EAAMC,EAAMC,GAAMyE,KAAK7F,GAAM6J,MAAM7J,GAM5DuoB,mBAAmBrnB,EAAMC,EAAMC,GAC7B,MAAM2lB,EAAgB9lB,EAAeC,EAAMC,EAAMC,GAEjD,OADA2lB,EAActW,SAAW0E,IAClBlW,KAAKgoB,WAAWF,GAMzByB,sBAAsBtnB,EAAMC,EAAMC,GAChC,OAAOnC,KAAKspB,mBAAmBrnB,EAAMC,EAAMC,GAAMyE,KAAK7F,GAAM6J,MAAM7J,GAGpE+U,wBACE,OAAO9V,KAAK0T,cAAcoC,wBAG5BmD,gBACE,OAAOjZ,KAAK6hB,WAGdlB,mBACE,OAAO3gB,KAAK0T,cAGdT,YACE,OAAOjT,KAAK2N,OAGd6b,oBACE,OAAOxpB,KAAKuN,eAGdkc,kBAAkBnmB,GAChBtD,KAAKuN,eAAiBjK,EAGxBomB,iBAAiBrnB,EAAUiB,GACzB,MAAMoB,EAAS1E,KAAKinB,cAActX,MAAKpO,GAAKuC,EAAazB,KAAcyB,EAAavC,EAAEc,YAElFqC,EACFA,EAAO6I,eAAiBjK,EAExBtD,KAAKinB,cAAc/a,KAAK,CACtB7J,WACAkL,eAAgBjK,IAKtB6P,iBAAiB9Q,GACf,IAAKA,EACH,OAIF,MAAMsnB,EAAwB3pB,KAAKinB,cAActX,MAAKpO,GAAKgC,EAAgBlB,EAAUd,EAAEc,YAWvF,OAAgC,MAAzBsnB,OAAgC,EAASA,EAAsBpc,eAGxEqc,oBAAoBpnB,EAAac,GAC/B,MAAMoB,EAAS1E,KAAKknB,iBAAiBvX,MAAKpO,GAAKuC,EAAatB,KAAiBsB,EAAavC,EAAEiB,eAExFkC,EACFA,EAAO6I,eAAiBjK,EAExBtD,KAAKknB,iBAAiBhb,KAAK,CACzB1J,cACA+K,eAAgBjK,IAKtBuS,oBAAoBrT,GAClB,IAAKA,EACH,OAIF,MAAMmnB,EAAwB3pB,KAAKknB,iBAAiBvX,MAAKpO,GAAKgC,EAAgBf,EAAajB,EAAEiB,eAW7F,OAAgC,MAAzBmnB,OAAgC,EAASA,EAAsBpc,eAGxE2F,oBAAoB5P,GAClB,GAAe,MAAXA,GAAmBA,EAAQumB,WAC7B,OAAOvmB,EAGT,MAAMoX,EAAmB,IAAK1a,KAAKuN,eAAeqF,WAC7C5S,KAAKmT,iBAA4B,MAAX7P,OAAkB,EAASA,EAAQjB,aACzDiB,EACHumB,YAAY,GAgBd,OAbKnP,EAAiBtX,WAAasX,EAAiBrY,WAClDqY,EAAiBtX,UAAYC,EAAsBqX,EAAiBrY,SAAUqY,SAI7B,IAAxCA,EAAiBhC,qBAC1BgC,EAAiBhC,mBAAsD,WAAjCgC,EAAiB1R,kBAGR,IAAtC0R,EAAiByC,mBAC1BzC,EAAiByC,mBAAqBzC,EAAiBiD,UAGlDjD,EAGT9E,uBAAuBtS,GACrB,OAAe,MAAXA,GAAmBA,EAAQumB,WACtBvmB,EAGF,IAAKtD,KAAKuN,eAAeoI,aAC3B3V,KAAK6V,oBAA+B,MAAXvS,OAAkB,EAASA,EAAQd,gBAC5Dc,EACHumB,YAAY,GAIhBxW,QACErT,KAAK6hB,WAAWxO,QAChBrT,KAAK0T,cAAcL,gCVrUY,EACjCN,SACAiU,WACA3V,UACA+S,kBAAiB,MAEjBJ,EAAMlB,WAAU,KACd/P,EAAOqU,QACA,KACLrU,EAAOwU,aAER,CAACxU,IAUJ,MAAM+W,EAAU3F,GAAsB9S,EAAS+S,GAE/C,OACEJ,EAAAR,cAACU,GAA0BU,SAA3B,CAAoC3jB,OAAQoQ,GAAW+S,GACrDJ,EAAAR,cAACsG,EAAQlF,SAAT,CAAkB3jB,MAAO8R,GAASiU,+BEtDD,EACrCA,eAEA,MAAO/lB,GAAS+iB,EAAMpB,UAAS,IAAMiC,OACrC,OACEb,EAAAR,cAACwB,GAA+BJ,SAAhC,CAAyC3jB,MAAOA,GACzB,mBAAb+lB,EACHA,EAAsB/lB,GACvB+lB,4HRtBV,SAAmBjU,EAAQzP,EAAU,IACnC,MAAMqS,EAAY,GACZ/C,EAAU,GAEhB,IAAmC,IAA/BtP,EAAQymB,mBAA8B,CACxC,MAAMC,EAA0B1mB,EAAQ0mB,yBAA2BvI,GACnE1O,EAAO4N,mBAAmBrN,SAASvL,SAAQnE,IACrComB,EAAwBpmB,IAC1B+R,EAAUzJ,KAjClB,SAA2BtI,GACzB,MAAO,CACLpB,YAAaoB,EAASN,QAAQd,YAC9BkB,MAAOE,EAASF,OA8BGumB,CAAkBrmB,OAKvC,IAAiC,IAA7BN,EAAQ4mB,iBAA4B,CACtC,MAAMC,EAAuB7mB,EAAQ6mB,sBAAwBzI,GAC7D3O,EAAOkG,gBAAgB3F,SAASvL,SAAQjF,IAClCqnB,EAAqBrnB,IACvB8P,EAAQ1G,KA/BhB,SAAwBpJ,GACtB,MAAO,CACLY,MAAOZ,EAAMY,MACbrB,SAAUS,EAAMT,SAChBe,UAAWN,EAAMM,WA2BAgnB,CAAetnB,OAKlC,MAAO,CACL6S,YACA/C,0FhBsOJ,SAAiB3R,GACf,OAAOA,aAAiBoR,+KiCvMnB,SAAsB/O,GAC3B,OAAOA,2DCrBF,SAMLrB,EASAC,EASAC,GASA,OAAOskB,GADSzkB,EAAeC,EAAMC,EAAMC,GAGzC4d,qBCjFG,SACL9d,EACAC,EACAC,GAEA,MAAOU,EAASS,EAAU,IAAMZ,EAAgBT,EAAMC,EAAMC,GACtDoiB,EAAcD,GAAe,CAAEjT,QAAS/N,EAAQ+N,UAChDwQ,EAAa0C,EAAYtL,gBAE/B,OAAO4K,GACLG,EAAMgC,aACHC,GACCpE,EAAW1hB,UAAUyL,EAAcU,WAAW2Z,KAChD,CAACpE,KAEH,IAAM0C,EAAYlI,WAAWxZ,KAC7B,IAAM0hB,EAAYlI,WAAWxZ,sBCb1B,SACLZ,EACAC,EACAC,GAEA,MAAOU,EAASS,EAAU,IAAMX,EAAwBV,EAAMC,EAAMC,GAE9DoiB,EAAcD,GAAe,CAAEjT,QAAS/N,EAAQ+N,UAChDqC,EAAgB6Q,EAAY5D,mBAElC,OAAOkD,GACLG,EAAMgC,aACHC,GACCvS,EAAcvT,UAAUyL,EAAcU,WAAW2Z,KACnD,CAACvS,KAEH,IAAM6Q,EAAYmD,WAAW7kB,KAC7B,IAAM0hB,EAAYmD,WAAW7kB,wCL4B1B,SAMLZ,EAIAC,EAGAC,GAEA,MAAMmB,EAAUf,EAAkBN,EAAMC,EAAMC,GACxCoiB,EAAcD,GAAe,CAAEjT,QAAS/N,EAAQ+N,WAE/CjC,GAAY4U,EAAMpB,UACvB,IACE,IAAIpC,GACF+D,EACAjhB,KAIN0gB,EAAMlB,WAAU,KACd1T,EAAS5B,WAAWlK,KACnB,CAAC8L,EAAU9L,IAEd,MAAMoB,EAASmf,GACbG,EAAMgC,aACHC,GACC7W,EAASjP,UAAUyL,EAAcU,WAAW2Z,KAC9C,CAAC7W,KAEH,IAAMA,EAASI,qBACf,IAAMJ,EAASI,qBAGXiR,EAASuD,EAAMgC,aAGnB,CAAChS,EAAW8M,KACV1R,EAASqR,OAAOzM,EAAW8M,GAAelW,MAAM7J,MAElD,CAACqO,IAGH,GACE1K,EAAOiG,OACPua,GAAiB9V,EAAS9L,QAAQ6Z,iBAAkB,CAACzY,EAAOiG,QAE5D,MAAMjG,EAAOiG,MAGf,MAAO,IAAKjG,EAAQ+b,SAAQ4J,YAAa3lB,EAAO+b,oCMiB3C,SAMLxe,EACAC,EAGAC,GAGA,OAAOskB,GADezkB,EAAeC,EAAMC,EAAMC,GACd8V,8ECR9B,UAA6CrF,QAClDA,EADkDvB,QAElDA,IAKA,OAAOwU,GAAW,CAChBjT,QAASA,EAAQ0L,KAAKxb,IAAD,IAChBA,EACHuM,SAAS,EACT8N,kBAAkB,EAClBQ,UAAU,EACV5D,qBAAiBpS,EACjBqB,YAAa,aAEfqI,gCC1JG,SAKL/N,GACA,OAAOmjB,GACL,IACKnjB,EACH+L,SAAS,EACT8N,kBAAkB,EAClBQ,UAAU,EACV5D,qBAAiBpS,EACjBqB,YAAa,SACbgB,eAAWrC,EACXuC,aAASvC,EACToK,eAAWpK,GAEbsQ"}