{"version": 3, "file": "index.development.js", "sources": ["../../../query-core/build/lib/subscribable.mjs", "../../../query-core/build/lib/utils.mjs", "../../../query-core/build/lib/focusManager.mjs", "../../../query-core/build/lib/onlineManager.mjs", "../../../query-core/build/lib/retryer.mjs", "../../../query-core/build/lib/logger.mjs", "../../../query-core/build/lib/notifyManager.mjs", "../../../query-core/build/lib/removable.mjs", "../../../query-core/build/lib/query.mjs", "../../../query-core/build/lib/queryCache.mjs", "../../../query-core/build/lib/mutation.mjs", "../../../query-core/build/lib/mutationCache.mjs", "../../../query-core/build/lib/infiniteQueryBehavior.mjs", "../../../query-core/build/lib/queryClient.mjs", "../../../query-core/build/lib/queryObserver.mjs", "../../../query-core/build/lib/queriesObserver.mjs", "../../../query-core/build/lib/infiniteQueryObserver.mjs", "../../../query-core/build/lib/mutationObserver.mjs", "../../../query-core/build/lib/hydration.mjs", "../../src/reactBatchedUpdates.ts", "../../src/setBatchUpdatesFn.ts", "../../../../node_modules/.pnpm/use-sync-external-store@1.2.0_react@18.2.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../../../node_modules/.pnpm/use-sync-external-store@1.2.0_react@18.2.0/node_modules/use-sync-external-store/shim/index.js", "../../src/useSyncExternalStore.ts", "../../src/QueryClientProvider.tsx", "../../src/isRestoring.tsx", "../../src/QueryErrorResetBoundary.tsx", "../../src/utils.ts", "../../src/errorBoundaryUtils.ts", "../../src/suspense.ts", "../../src/useQueries.ts", "../../src/useBaseQuery.ts", "../../src/useQuery.ts", "../../src/useSuspenseQuery.ts", "../../src/useSuspenseQueries.ts", "../../src/queryOptions.ts", "../../src/Hydrate.tsx", "../../src/useIsFetching.ts", "../../src/useIsMutating.ts", "../../src/useMutation.ts", "../../src/useInfiniteQuery.ts"], "sourcesContent": ["class Subscribable {\n  constructor() {\n    this.listeners = new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n\n  subscribe(listener) {\n    const identity = {\n      listener\n    };\n    this.listeners.add(identity);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(identity);\n      this.onUnsubscribe();\n    };\n  }\n\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n\n  onSubscribe() {// Do nothing\n  }\n\n  onUnsubscribe() {// Do nothing\n  }\n\n}\n\nexport { Subscribable };\n//# sourceMappingURL=subscribable.mjs.map\n", "// TYPES\n// UTILS\nconst isServer = typeof window === 'undefined' || 'Deno' in window;\nfunction noop() {\n  return undefined;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === 'number' && value >= 0 && value !== Infinity;\n}\nfunction difference(array1, array2) {\n  return array1.filter(x => !array2.includes(x));\n}\nfunction replaceAt(array, index, value) {\n  const copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQueryKey(arg1)) {\n    return arg1;\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3,\n      queryKey: arg1,\n      queryFn: arg2\n    };\n  }\n\n  return { ...arg2,\n    queryKey: arg1\n  };\n}\nfunction parseMutationArgs(arg1, arg2, arg3) {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3,\n        mutationKey: arg1,\n        mutationFn: arg2\n      };\n    }\n\n    return { ...arg2,\n      mutationKey: arg1\n    };\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2,\n      mutationFn: arg1\n    };\n  }\n\n  return { ...arg1\n  };\n}\nfunction parseFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [{ ...arg2,\n    queryKey: arg1\n  }, arg3] : [arg1 || {}, arg2];\n}\nfunction parseMutationFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [{ ...arg2,\n    mutationKey: arg1\n  }, arg3] : [arg1 || {}, arg2];\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive();\n\n    if (type === 'active' && !isActive) {\n      return false;\n    }\n\n    if (type === 'inactive' && isActive) {\n      return false;\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false;\n  }\n\n  if (typeof fetchStatus !== 'undefined' && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const {\n    exact,\n    fetching,\n    predicate,\n    mutationKey\n  } = filters;\n\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n\n    if (exact) {\n      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n\n  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;\n  return hashFn(queryKey);\n}\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\n\nfunction hashQueryKey(queryKey) {\n  return JSON.stringify(queryKey, (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n    result[key] = val[key];\n    return result;\n  }, {}) : val);\n}\n/**\n * Checks if key `b` partially matches with key `a`.\n */\n\nfunction partialMatchKey(a, b) {\n  return partialDeepEqual(a, b);\n}\n/**\n * Checks if `b` partially matches with `a`.\n */\n\nfunction partialDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== typeof b) {\n    return false;\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(key => !partialDeepEqual(a[key], b[key]));\n  }\n\n  return false;\n}\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\n\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b);\n\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aSize = array ? a.length : Object.keys(a).length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      copy[key] = replaceEqualDeep(a[key], b[key]);\n\n      if (copy[key] === a[key]) {\n        equalItems++;\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n\n  return b;\n}\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\n\nfunction shallowEqualObjects(a, b) {\n  if (a && !b || b && !a) {\n    return false;\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n} // Copied from: https://github.com/jonschlinkert/is-plain-object\n\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  } // If has modified constructor\n\n\n  const ctor = o.constructor;\n\n  if (typeof ctor === 'undefined') {\n    return true;\n  } // If has modified prototype\n\n\n  const prot = ctor.prototype;\n\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  } // If constructor does not have an Object-specific method\n\n\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false;\n  } // Most likely a plain Object\n\n\n  return true;\n}\n\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isQueryKey(value) {\n  return Array.isArray(value);\n}\nfunction isError(value) {\n  return value instanceof Error;\n}\nfunction sleep(timeout) {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout);\n  });\n}\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nfunction scheduleMicrotask(callback) {\n  sleep(0).then(callback);\n}\nfunction getAbortController() {\n  if (typeof AbortController === 'function') {\n    return new AbortController();\n  }\n\n  return;\n}\nfunction replaceData(prevData, data, options) {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual != null && options.isDataEqual(prevData, data)) {\n    return prevData;\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data);\n  }\n\n  return data;\n}\n\nexport { difference, functionalUpdate, getAbortController, hashQueryKey, hashQueryKeyByOptions, isError, isPlainArray, isPlainObject, isQueryKey, isServer, isValidTimeout, matchMutation, matchQuery, noop, parseFilterArgs, parseMutationArgs, parseMutationFilterArgs, parseQueryArgs, partialDeepEqual, partialMatchKey, replaceAt, replaceData, replaceEqualDeep, scheduleMicrotask, shallowEqualObjects, sleep, timeUntilStale };\n//# sourceMappingURL=utils.mjs.map\n", "import { Subscribable } from './subscribable.mjs';\nimport { isServer } from './utils.mjs';\n\nclass FocusManager extends Subscribable {\n  constructor() {\n    super();\n\n    this.setup = onFocus => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus(); // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n\n      return;\n    };\n  }\n\n  onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  }\n\n  setEventListener(setup) {\n    var _this$cleanup2;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(focused => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n\n  setFocused(focused) {\n    const changed = this.focused !== focused;\n\n    if (changed) {\n      this.focused = focused;\n      this.onFocus();\n    }\n  }\n\n  onFocus() {\n    this.listeners.forEach(({\n      listener\n    }) => {\n      listener();\n    });\n  }\n\n  isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  }\n\n}\nconst focusManager = new FocusManager();\n\nexport { FocusManager, focusManager };\n//# sourceMappingURL=focusManager.mjs.map\n", "import { Subscribable } from './subscribable.mjs';\nimport { isServer } from './utils.mjs';\n\nconst onlineEvents = ['online', 'offline'];\nclass OnlineManager extends Subscribable {\n  constructor() {\n    super();\n\n    this.setup = onOnline => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onOnline(); // Listen to online\n\n\n        onlineEvents.forEach(event => {\n          window.addEventListener(event, listener, false);\n        });\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach(event => {\n            window.removeEventListener(event, listener);\n          });\n        };\n      }\n\n      return;\n    };\n  }\n\n  onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  }\n\n  setEventListener(setup) {\n    var _this$cleanup2;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(online => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online);\n      } else {\n        this.onOnline();\n      }\n    });\n  }\n\n  setOnline(online) {\n    const changed = this.online !== online;\n\n    if (changed) {\n      this.online = online;\n      this.onOnline();\n    }\n  }\n\n  onOnline() {\n    this.listeners.forEach(({\n      listener\n    }) => {\n      listener();\n    });\n  }\n\n  isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  }\n\n}\nconst onlineManager = new OnlineManager();\n\nexport { OnlineManager, onlineManager };\n//# sourceMappingURL=onlineManager.mjs.map\n", "import { focusManager } from './focusManager.mjs';\nimport { onlineManager } from './onlineManager.mjs';\nimport { sleep } from './utils.mjs';\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * 2 ** failureCount, 30000);\n}\n\nfunction canFetch(networkMode) {\n  return (networkMode != null ? networkMode : 'online') === 'online' ? onlineManager.isOnline() : true;\n}\nclass CancelledError {\n  constructor(options) {\n    this.revert = options == null ? void 0 : options.revert;\n    this.silent = options == null ? void 0 : options.silent;\n  }\n\n}\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  let promiseResolve;\n  let promiseReject;\n  const promise = new Promise((outerResolve, outerReject) => {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  const cancel = cancelOptions => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort == null ? void 0 : config.abort();\n    }\n  };\n\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n\n  const shouldPause = () => !focusManager.isFocused() || config.networkMode !== 'always' && !onlineManager.isOnline();\n\n  const resolve = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  const reject = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  const pause = () => {\n    return new Promise(continueResolve => {\n      continueFn = value => {\n        const canContinue = isResolved || !shouldPause();\n\n        if (canContinue) {\n          continueResolve(value);\n        }\n\n        return canContinue;\n      };\n\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(() => {\n      continueFn = undefined;\n\n      if (!isResolved) {\n        config.onContinue == null ? void 0 : config.onContinue();\n      }\n    });\n  }; // Create loop function\n\n\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return;\n    }\n\n    let promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n\n    Promise.resolve(promiseOrValue).then(resolve).catch(error => {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      const retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      const retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      const delay = typeof retryDelay === 'function' ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === 'number' && failureCount < retry || typeof retry === 'function' && retry(failureCount, error);\n\n      if (isRetryCancelled || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(failureCount, error); // Delay\n\n      sleep(delay) // Pause if the document is not visible or when the device is offline\n      .then(() => {\n        if (shouldPause()) {\n          return pause();\n        }\n\n        return;\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  if (canFetch(config.networkMode)) {\n    run();\n  } else {\n    pause().then(run);\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn == null ? void 0 : continueFn();\n      return didContinue ? promise : Promise.resolve();\n    },\n    cancelRetry,\n    continueRetry\n  };\n}\n\nexport { CancelledError, canFetch, createRetryer, isCancelledError };\n//# sourceMappingURL=retryer.mjs.map\n", "const defaultLogger = console;\n\nexport { defaultLogger };\n//# sourceMappingURL=logger.mjs.map\n", "import { scheduleMicrotask } from './utils.mjs';\n\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n\n  let notifyFn = callback => {\n    callback();\n  };\n\n  let batchNotifyFn = callback => {\n    callback();\n  };\n\n  const batch = callback => {\n    let result;\n    transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      transactions--;\n\n      if (!transactions) {\n        flush();\n      }\n    }\n\n    return result;\n  };\n\n  const schedule = callback => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleMicrotask(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n\n\n  const batchCalls = callback => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args);\n      });\n    };\n  };\n\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n\n    if (originalQueue.length) {\n      scheduleMicrotask(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach(callback => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n\n\n  const setNotifyFunction = fn => {\n    notifyFn = fn;\n  };\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n\n\n  const setBatchNotifyFunction = fn => {\n    batchNotifyFn = fn;\n  };\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction\n  };\n} // SINGLETON\n\nconst notifyManager = createNotifyManager();\n\nexport { createNotifyManager, notifyManager };\n//# sourceMappingURL=notifyManager.mjs.map\n", "import { isValidTimeout, isServer } from './utils.mjs';\n\nclass Removable {\n  destroy() {\n    this.clearGcTimeout();\n  }\n\n  scheduleGc() {\n    this.clearGcTimeout();\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.cacheTime);\n    }\n  }\n\n  updateCacheTime(newCacheTime) {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(this.cacheTime || 0, newCacheTime != null ? newCacheTime : isServer ? Infinity : 5 * 60 * 1000);\n  }\n\n  clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  }\n\n}\n\nexport { Removable };\n//# sourceMappingURL=removable.mjs.map\n", "import { replaceData, noop, timeUntilStale, getAbortController } from './utils.mjs';\nimport { defaultLogger } from './logger.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { createRetryer, isCancelledError, canFetch } from './retryer.mjs';\nimport { Removable } from './removable.mjs';\n\n// CLASS\nclass Query extends Removable {\n  constructor(config) {\n    super();\n    this.abortSignalConsumed = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.logger = config.logger || defaultLogger;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || getDefaultState(this.options);\n    this.state = this.initialState;\n    this.scheduleGc();\n  }\n\n  get meta() {\n    return this.options.meta;\n  }\n\n  setOptions(options) {\n    this.options = { ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this);\n    }\n  }\n\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options); // Set data and mark it as cached\n\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt,\n      manual: options == null ? void 0 : options.manual\n    });\n    return data;\n  }\n\n  setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state,\n      setStateOptions\n    });\n  }\n\n  cancel(options) {\n    var _this$retryer;\n\n    const promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n\n  reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  }\n\n  isActive() {\n    return this.observers.some(observer => observer.options.enabled !== false);\n  }\n\n  isDisabled() {\n    return this.getObserversCount() > 0 && !this.isActive();\n  }\n\n  isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(observer => observer.getCurrentResult().isStale);\n  }\n\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n\n  onFocus() {\n    var _this$retryer2;\n\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  }\n\n  onOnline() {\n    var _this$retryer3;\n\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  }\n\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer\n      });\n    }\n  }\n\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        this.scheduleGc();\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer\n      });\n    }\n  }\n\n  getObserversCount() {\n    return this.observers.length;\n  }\n\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  }\n\n  fetch(options, fetchOptions) {\n    var _this$options$behavio, _context$fetchOptions;\n\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions != null && fetchOptions.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\"As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']\");\n      }\n    }\n\n    const abortController = getAbortController(); // Create query function context\n\n    const queryFnContext = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    }; // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n\n    const addSignalProperty = object => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true;\n            return abortController.signal;\n          }\n\n          return undefined;\n        }\n      });\n    };\n\n    addSignalProperty(queryFnContext); // Create fetch function\n\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\"Missing queryFn for queryKey '\" + this.options.queryHash + \"'\");\n      }\n\n      this.abortSignalConsumed = false;\n      return this.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    (_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch(context); // Store state in case the current fetch needs to be reverted\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (this.state.fetchStatus === 'idle' || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    }\n\n    const onError = error => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n\n      if (!isCancelledError(error)) {\n        var _this$cache$config$on, _this$cache$config, _this$cache$config$on2, _this$cache$config2;\n\n        // Notify cache callback\n        (_this$cache$config$on = (_this$cache$config = this.cache.config).onError) == null ? void 0 : _this$cache$config$on.call(_this$cache$config, error, this);\n        (_this$cache$config$on2 = (_this$cache$config2 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on2.call(_this$cache$config2, this.state.data, error, this);\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error);\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc();\n      }\n\n      this.isFetchingOptimistic = false;\n    }; // Try to fetch the data\n\n\n    this.retryer = createRetryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : abortController.abort.bind(abortController),\n      onSuccess: data => {\n        var _this$cache$config$on3, _this$cache$config3, _this$cache$config$on4, _this$cache$config4;\n\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\"Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: \" + this.queryHash);\n          }\n\n          onError(new Error(this.queryHash + \" data is undefined\"));\n          return;\n        }\n\n        this.setData(data); // Notify cache callback\n\n        (_this$cache$config$on3 = (_this$cache$config3 = this.cache.config).onSuccess) == null ? void 0 : _this$cache$config$on3.call(_this$cache$config3, data, this);\n        (_this$cache$config$on4 = (_this$cache$config4 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on4.call(_this$cache$config4, data, this.state.error, this);\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc();\n        }\n\n        this.isFetchingOptimistic = false;\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({\n          type: 'failed',\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: () => {\n        this.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  }\n\n  dispatch(action) {\n    const reducer = state => {\n      var _action$meta, _action$dataUpdatedAt;\n\n      switch (action.type) {\n        case 'failed':\n          return { ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n\n        case 'pause':\n          return { ...state,\n            fetchStatus: 'paused'\n          };\n\n        case 'continue':\n          return { ...state,\n            fetchStatus: 'fetching'\n          };\n\n        case 'fetch':\n          return { ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n            fetchStatus: canFetch(this.options.networkMode) ? 'fetching' : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading'\n            })\n          };\n\n        case 'success':\n          return { ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n\n        case 'error':\n          const error = action.error;\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState,\n              fetchStatus: 'idle'\n            };\n          }\n\n          return { ...state,\n            error: error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error'\n          };\n\n        case 'invalidate':\n          return { ...state,\n            isInvalidated: true\n          };\n\n        case 'setState':\n          return { ...state,\n            ...action.state\n          };\n      }\n    };\n\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate(action);\n      });\n      this.cache.notify({\n        query: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n\n}\n\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n  const hasData = typeof data !== 'undefined';\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle'\n  };\n}\n\nexport { Query };\n//# sourceMappingURL=query.mjs.map\n", "import { hashQueryKeyByOptions, parseFilterArgs, matchQuery } from './utils.mjs';\nimport { Query } from './query.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { Subscribable } from './subscribable.mjs';\n\n// CLASS\nclass QueryCache extends Subscribable {\n  constructor(config) {\n    super();\n    this.config = config || {};\n    this.queries = [];\n    this.queriesMap = {};\n  }\n\n  build(client, options, state) {\n    var _options$queryHash;\n\n    const queryKey = options.queryKey;\n    const queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n\n    return query;\n  }\n\n  add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'added',\n        query\n      });\n    }\n  }\n\n  remove(query) {\n    const queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(x => x !== query);\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'removed',\n        query\n      });\n    }\n  }\n\n  clear() {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        this.remove(query);\n      });\n    });\n  }\n\n  get(queryHash) {\n    return this.queriesMap[queryHash];\n  }\n\n  getAll() {\n    return this.queries;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(query => matchQuery(filters, query));\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n    return Object.keys(filters).length > 0 ? this.queries.filter(query => matchQuery(filters, query)) : this.queries;\n  }\n\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(event);\n      });\n    });\n  }\n\n  onFocus() {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onFocus();\n      });\n    });\n  }\n\n  onOnline() {\n    notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onOnline();\n      });\n    });\n  }\n\n}\n\nexport { QueryCache };\n//# sourceMappingURL=queryCache.mjs.map\n", "import { defaultLogger } from './logger.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { Removable } from './removable.mjs';\nimport { createRetryer, canFetch } from './retryer.mjs';\n\n// CLASS\nclass Mutation extends Removable {\n  constructor(config) {\n    super();\n    this.defaultOptions = config.defaultOptions;\n    this.mutationId = config.mutationId;\n    this.mutationCache = config.mutationCache;\n    this.logger = config.logger || defaultLogger;\n    this.observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n\n  setOptions(options) {\n    this.options = { ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n\n  get meta() {\n    return this.options.meta;\n  }\n\n  setState(state) {\n    this.dispatch({\n      type: 'setState',\n      state\n    });\n  }\n\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the mutation from being garbage collected\n\n      this.clearGcTimeout();\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer\n      });\n    }\n  }\n\n  removeObserver(observer) {\n    this.observers = this.observers.filter(x => x !== observer);\n    this.scheduleGc();\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer\n    });\n  }\n\n  optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc();\n      } else {\n        this.mutationCache.remove(this);\n      }\n    }\n  }\n\n  continue() {\n    var _this$retryer$continu, _this$retryer;\n\n    return (_this$retryer$continu = (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.continue()) != null ? _this$retryer$continu : this.execute();\n  }\n\n  async execute() {\n    const executeMutation = () => {\n      var _this$options$retry;\n\n      this.retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found');\n          }\n\n          return this.options.mutationFn(this.state.variables);\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({\n            type: 'failed',\n            failureCount,\n            error\n          });\n        },\n        onPause: () => {\n          this.dispatch({\n            type: 'pause'\n          });\n        },\n        onContinue: () => {\n          this.dispatch({\n            type: 'continue'\n          });\n        },\n        retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode\n      });\n      return this.retryer.promise;\n    };\n\n    const restored = this.state.status === 'loading';\n\n    try {\n      var _this$mutationCache$c3, _this$mutationCache$c4, _this$options$onSucce, _this$options2, _this$mutationCache$c5, _this$mutationCache$c6, _this$options$onSettl, _this$options3;\n\n      if (!restored) {\n        var _this$mutationCache$c, _this$mutationCache$c2, _this$options$onMutat, _this$options;\n\n        this.dispatch({\n          type: 'loading',\n          variables: this.options.variables\n        }); // Notify cache callback\n\n        await ((_this$mutationCache$c = (_this$mutationCache$c2 = this.mutationCache.config).onMutate) == null ? void 0 : _this$mutationCache$c.call(_this$mutationCache$c2, this.state.variables, this));\n        const context = await ((_this$options$onMutat = (_this$options = this.options).onMutate) == null ? void 0 : _this$options$onMutat.call(_this$options, this.state.variables));\n\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables\n          });\n        }\n      }\n\n      const data = await executeMutation(); // Notify cache callback\n\n      await ((_this$mutationCache$c3 = (_this$mutationCache$c4 = this.mutationCache.config).onSuccess) == null ? void 0 : _this$mutationCache$c3.call(_this$mutationCache$c4, data, this.state.variables, this.state.context, this));\n      await ((_this$options$onSucce = (_this$options2 = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options2, data, this.state.variables, this.state.context)); // Notify cache callback\n\n      await ((_this$mutationCache$c5 = (_this$mutationCache$c6 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c5.call(_this$mutationCache$c6, data, null, this.state.variables, this.state.context, this));\n      await ((_this$options$onSettl = (_this$options3 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options3, data, null, this.state.variables, this.state.context));\n      this.dispatch({\n        type: 'success',\n        data\n      });\n      return data;\n    } catch (error) {\n      try {\n        var _this$mutationCache$c7, _this$mutationCache$c8, _this$options$onError, _this$options4, _this$mutationCache$c9, _this$mutationCache$c10, _this$options$onSettl2, _this$options5;\n\n        // Notify cache callback\n        await ((_this$mutationCache$c7 = (_this$mutationCache$c8 = this.mutationCache.config).onError) == null ? void 0 : _this$mutationCache$c7.call(_this$mutationCache$c8, error, this.state.variables, this.state.context, this));\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error);\n        }\n\n        await ((_this$options$onError = (_this$options4 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options4, error, this.state.variables, this.state.context)); // Notify cache callback\n\n        await ((_this$mutationCache$c9 = (_this$mutationCache$c10 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c9.call(_this$mutationCache$c10, undefined, error, this.state.variables, this.state.context, this));\n        await ((_this$options$onSettl2 = (_this$options5 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options5, undefined, error, this.state.variables, this.state.context));\n        throw error;\n      } finally {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n    }\n  }\n\n  dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case 'failed':\n          return { ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n\n        case 'pause':\n          return { ...state,\n            isPaused: true\n          };\n\n        case 'continue':\n          return { ...state,\n            isPaused: false\n          };\n\n        case 'loading':\n          return { ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables\n          };\n\n        case 'success':\n          return { ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false\n          };\n\n        case 'error':\n          return { ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error'\n          };\n\n        case 'setState':\n          return { ...state,\n            ...action.state\n          };\n      }\n    };\n\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onMutationUpdate(action);\n      });\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n\n}\nfunction getDefaultState() {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined\n  };\n}\n\nexport { Mutation, getDefaultState };\n//# sourceMappingURL=mutation.mjs.map\n", "import { notify<PERSON>anager } from './notifyManager.mjs';\nimport { Mutation } from './mutation.mjs';\nimport { matchMutation, noop } from './utils.mjs';\nimport { Subscribable } from './subscribable.mjs';\n\n// CLASS\nclass MutationCache extends Subscribable {\n  constructor(config) {\n    super();\n    this.config = config || {};\n    this.mutations = [];\n    this.mutationId = 0;\n  }\n\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined\n    });\n    this.add(mutation);\n    return mutation;\n  }\n\n  add(mutation) {\n    this.mutations.push(mutation);\n    this.notify({\n      type: 'added',\n      mutation\n    });\n  }\n\n  remove(mutation) {\n    this.mutations = this.mutations.filter(x => x !== mutation);\n    this.notify({\n      type: 'removed',\n      mutation\n    });\n  }\n\n  clear() {\n    notifyManager.batch(() => {\n      this.mutations.forEach(mutation => {\n        this.remove(mutation);\n      });\n    });\n  }\n\n  getAll() {\n    return this.mutations;\n  }\n\n  find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(mutation => matchMutation(filters, mutation));\n  }\n\n  findAll(filters) {\n    return this.mutations.filter(mutation => matchMutation(filters, mutation));\n  }\n\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(event);\n      });\n    });\n  }\n\n  resumePausedMutations() {\n    var _this$resuming;\n\n    this.resuming = ((_this$resuming = this.resuming) != null ? _this$resuming : Promise.resolve()).then(() => {\n      const pausedMutations = this.mutations.filter(x => x.state.isPaused);\n      return notifyManager.batch(() => pausedMutations.reduce((promise, mutation) => promise.then(() => mutation.continue().catch(noop)), Promise.resolve()));\n    }).then(() => {\n      this.resuming = undefined;\n    });\n    return this.resuming;\n  }\n\n}\n\nexport { MutationCache };\n//# sourceMappingURL=mutationCache.mjs.map\n", "function infiniteQueryBehavior() {\n  return {\n    onFetch: context => {\n      context.fetchFn = () => {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n\n        const refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        const fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        const pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        const isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        const isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        const oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        const oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        let newPageParams = oldPageParams;\n        let cancelled = false;\n\n        const addSignalProperty = object => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              var _context$signal;\n\n              if ((_context$signal = context.signal) != null && _context$signal.aborted) {\n                cancelled = true;\n              } else {\n                var _context$signal2;\n\n                (_context$signal2 = context.signal) == null ? void 0 : _context$signal2.addEventListener('abort', () => {\n                  cancelled = true;\n                });\n              }\n\n              return context.signal;\n            }\n          });\n        }; // Get query function\n\n\n        const queryFn = context.options.queryFn || (() => Promise.reject(\"Missing queryFn for queryKey '\" + context.options.queryHash + \"'\"));\n\n        const buildNewPages = (pages, param, page, previous) => {\n          newPageParams = previous ? [param, ...newPageParams] : [...newPageParams, param];\n          return previous ? [page, ...pages] : [...pages, page];\n        }; // Create function to fetch a page\n\n\n        const fetchPage = (pages, manual, param, previous) => {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n\n          const queryFnContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const queryFnResult = queryFn(queryFnContext);\n          const promise = Promise.resolve(queryFnResult).then(page => buildNewPages(pages, param, page, previous));\n          return promise;\n        };\n\n        let promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param);\n        } // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param, true);\n        } // Refetch pages\n        else {\n          newPageParams = [];\n          const manual = typeof context.options.getNextPageParam === 'undefined';\n          const shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n          promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then(pages => {\n              const shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n\n              if (shouldFetchNextPage) {\n                const param = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n                return fetchPage(pages, manual, param);\n              }\n\n              return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n            });\n          }\n        }\n\n        const finalPromise = promise.then(pages => ({\n          pages,\n          pageParams: newPageParams\n        }));\n        return finalPromise;\n      };\n    }\n  };\n}\nfunction getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nfunction getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n\n  return;\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n\n  return;\n}\n\nexport { getNextPageParam, getPreviousPageParam, hasNextPage, hasPreviousPage, infiniteQueryBehavior };\n//# sourceMappingURL=infiniteQueryBehavior.mjs.map\n", "import { parseFilter<PERSON>rgs, parseQueryArgs, functionalUpdate, noop, hashQ<PERSON>y<PERSON><PERSON>, partialMatch<PERSON>ey, hashQueryKeyByOptions } from './utils.mjs';\nimport { QueryCache } from './queryCache.mjs';\nimport { MutationCache } from './mutationCache.mjs';\nimport { focusManager } from './focusManager.mjs';\nimport { onlineManager } from './onlineManager.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior.mjs';\nimport { defaultLogger } from './logger.mjs';\n\n// CLASS\nclass QueryClient {\n  constructor(config = {}) {\n    this.queryCache = config.queryCache || new QueryCache();\n    this.mutationCache = config.mutationCache || new MutationCache();\n    this.logger = config.logger || defaultLogger;\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n    this.mountCount = 0;\n\n    if (process.env.NODE_ENV !== 'production' && config.logger) {\n      this.logger.error(\"Passing a custom logger has been deprecated and will be removed in the next major version.\");\n    }\n  }\n\n  mount() {\n    this.mountCount++;\n    if (this.mountCount !== 1) return;\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused()) {\n        this.resumePausedMutations();\n        this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (onlineManager.isOnline()) {\n        this.resumePausedMutations();\n        this.queryCache.onOnline();\n      }\n    });\n  }\n\n  unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    this.mountCount--;\n    if (this.mountCount !== 0) return;\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    this.unsubscribeFocus = undefined;\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n    this.unsubscribeOnline = undefined;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n    filters.fetchStatus = 'fetching';\n    return this.queryCache.findAll(filters).length;\n  }\n\n  isMutating(filters) {\n    return this.mutationCache.findAll({ ...filters,\n      fetching: true\n    }).length;\n  }\n\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData(arg1, arg2, arg3) {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    const cachedData = this.getQueryData(parsedOptions.queryKey);\n    return cachedData ? Promise.resolve(cachedData) : this.fetchQuery(parsedOptions);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(({\n      queryKey,\n      state\n    }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n\n  setQueryData(queryKey, updater, options) {\n    const query = this.queryCache.find(queryKey);\n    const prevData = query == null ? void 0 : query.state.data;\n    const data = functionalUpdate(updater, prevData);\n\n    if (typeof data === 'undefined') {\n      return undefined;\n    }\n\n    const parsedOptions = parseQueryArgs(queryKey);\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(data, { ...options,\n      manual: true\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData(queryKeyOrFilters, updater, options) {\n    return notifyManager.batch(() => this.getQueryCache().findAll(queryKeyOrFilters).map(({\n      queryKey\n    }) => [queryKey, this.setQueryData(queryKey, updater, options)]));\n  }\n\n  getQueryState(queryKey,\n  /**\n   * @deprecated This filters will be removed in the next major version.\n   */\n  filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(arg1, arg2) {\n    const [filters] = parseFilterArgs(arg1, arg2);\n    const queryCache = this.queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        queryCache.remove(query);\n      });\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(arg1, arg2, arg3) {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);\n    const queryCache = this.queryCache;\n    const refetchFilters = {\n      type: 'active',\n      ...filters\n    };\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        query.reset();\n      });\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(arg1, arg2, arg3) {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3);\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    const promises = notifyManager.batch(() => this.queryCache.findAll(filters).map(query => query.cancel(cancelOptions)));\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(arg1, arg2, arg3) {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);\n    return notifyManager.batch(() => {\n      var _ref, _filters$refetchType;\n\n      this.queryCache.findAll(filters).forEach(query => {\n        query.invalidate();\n      });\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve();\n      }\n\n      const refetchFilters = { ...filters,\n        type: (_ref = (_filters$refetchType = filters.refetchType) != null ? _filters$refetchType : filters.type) != null ? _ref : 'active'\n      };\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(arg1, arg2, arg3) {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3);\n    const promises = notifyManager.batch(() => this.queryCache.findAll(filters).filter(query => !query.isDisabled()).map(query => {\n      var _options$cancelRefetc;\n\n      return query.fetch(undefined, { ...options,\n        cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n        meta: {\n          refetchPage: filters.refetchPage\n        }\n      });\n    }));\n    let promise = Promise.all(promises).then(noop);\n\n    if (!(options != null && options.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery(arg1, arg2, arg3) {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery(arg1, arg2, arg3) {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    parsedOptions.behavior = infiniteQueryBehavior();\n    return this.fetchQuery(parsedOptions);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  }\n\n  resumePausedMutations() {\n    return this.mutationCache.resumePausedMutations();\n  }\n\n  getQueryCache() {\n    return this.queryCache;\n  }\n\n  getMutationCache() {\n    return this.mutationCache;\n  }\n\n  getLogger() {\n    return this.logger;\n  }\n\n  getDefaultOptions() {\n    return this.defaultOptions;\n  }\n\n  setDefaultOptions(options) {\n    this.defaultOptions = options;\n  }\n\n  setQueryDefaults(queryKey, options) {\n    const result = this.queryDefaults.find(x => hashQueryKey(queryKey) === hashQueryKey(x.queryKey));\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey,\n        defaultOptions: options\n      });\n    }\n  }\n\n  getQueryDefaults(queryKey) {\n    if (!queryKey) {\n      return undefined;\n    } // Get the first matching defaults\n\n\n    const firstMatchingDefaults = this.queryDefaults.find(x => partialMatchKey(queryKey, x.queryKey)); // Additional checks and error in dev mode\n\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter(x => partialMatchKey(queryKey, x.queryKey)); // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\"[QueryClient] Several query defaults match with key '\" + JSON.stringify(queryKey) + \"'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.\");\n      }\n    }\n\n    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;\n  }\n\n  setMutationDefaults(mutationKey, options) {\n    const result = this.mutationDefaults.find(x => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey));\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey,\n        defaultOptions: options\n      });\n    }\n  }\n\n  getMutationDefaults(mutationKey) {\n    if (!mutationKey) {\n      return undefined;\n    } // Get the first matching defaults\n\n\n    const firstMatchingDefaults = this.mutationDefaults.find(x => partialMatchKey(mutationKey, x.mutationKey)); // Additional checks and error in dev mode\n\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter(x => partialMatchKey(mutationKey, x.mutationKey)); // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\"[QueryClient] Several mutation defaults match with key '\" + JSON.stringify(mutationKey) + \"'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.\");\n      }\n    }\n\n    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;\n  }\n\n  defaultQueryOptions(options) {\n    if (options != null && options._defaulted) {\n      return options;\n    }\n\n    const defaultedOptions = { ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options == null ? void 0 : options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(defaultedOptions.queryKey, defaultedOptions);\n    } // dependent default values\n\n\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== 'always';\n    }\n\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense;\n    }\n\n    return defaultedOptions;\n  }\n\n  defaultMutationOptions(options) {\n    if (options != null && options._defaulted) {\n      return options;\n    }\n\n    return { ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options == null ? void 0 : options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n\n  clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  }\n\n}\n\nexport { QueryClient };\n//# sourceMappingURL=queryClient.mjs.map\n", "import { shallowEqualObjects, noop, isServer, isValidTimeout, timeUntilStale, replaceData } from './utils.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { focusManager } from './focusManager.mjs';\nimport { Subscribable } from './subscribable.mjs';\nimport { canFetch, isCancelledError } from './retryer.mjs';\n\nclass QueryObserver extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.client = client;\n    this.options = options;\n    this.trackedProps = new Set();\n    this.selectError = null;\n    this.bindMethods();\n    this.setOptions(options);\n  }\n\n  bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  }\n\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  }\n\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  }\n\n  destroy() {\n    this.listeners = new Set();\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n    this.currentQuery.removeObserver(this);\n  }\n\n  setOptions(options, notifyOptions) {\n    const prevOptions = this.options;\n    const prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryOptions(options);\n\n    if (process.env.NODE_ENV !== 'production' && typeof (options == null ? void 0 : options.isDataEqual) !== 'undefined') {\n      this.client.getLogger().error(\"The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option\");\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this\n      });\n    }\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    const mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n\n  getOptimisticResult(options) {\n    const query = this.client.getQueryCache().build(this.client, options);\n    const result = this.createResult(query, options);\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result;\n      this.currentResultOptions = this.options;\n      this.currentResultState = this.currentQuery.state;\n    }\n\n    return result;\n  }\n\n  getCurrentResult() {\n    return this.currentResult;\n  }\n\n  trackResult(result) {\n    const trackedResult = {};\n    Object.keys(result).forEach(key => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key);\n          return result[key];\n        }\n      });\n    });\n    return trackedResult;\n  }\n\n  getCurrentQuery() {\n    return this.currentQuery;\n  }\n\n  remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  }\n\n  refetch({\n    refetchPage,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        refetchPage\n      }\n    });\n  }\n\n  fetchOptimistic(options) {\n    const defaultedOptions = this.client.defaultQueryOptions(options);\n    const query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    query.isFetchingOptimistic = true;\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n\n  fetch(fetchOptions) {\n    var _fetchOptions$cancelR;\n\n    return this.executeFetch({ ...fetchOptions,\n      cancelRefetch: (_fetchOptions$cancelR = fetchOptions.cancelRefetch) != null ? _fetchOptions$cancelR : true\n    }).then(() => {\n      this.updateResult();\n      return this.currentResult;\n    });\n  }\n\n  executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    let promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions != null && fetchOptions.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  }\n\n  updateStaleTimeout() {\n    this.clearStaleTimeout();\n\n    if (isServer || this.currentResult.isStale || !isValidTimeout(this.options.staleTime)) {\n      return;\n    }\n\n    const time = timeUntilStale(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    const timeout = time + 1;\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n\n  computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  }\n\n  updateRefetchInterval(nextInterval) {\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (isServer || this.options.enabled === false || !isValidTimeout(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        this.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  }\n\n  updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  }\n\n  clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  }\n\n  clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  }\n\n  createResult(query, options) {\n    const prevQuery = this.currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.currentResult;\n    const prevResultState = this.currentResultState;\n    const prevResultOptions = this.currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    const prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    const {\n      state\n    } = query;\n    let {\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      fetchStatus,\n      status\n    } = state;\n    let isPreviousData = false;\n    let isPlaceholderData = false;\n    let data; // Optimistically set result in fetching state if needed\n\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode) ? 'fetching' : 'paused';\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle';\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdatedAt && prevQueryResult != null && prevQueryResult.isSuccess && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n        data = this.selectResult;\n      } else {\n        try {\n          this.selectFn = options.select;\n          data = options.select(state.data);\n          data = replaceData(prevResult == null ? void 0 : prevResult.data, data, options);\n          this.selectResult = data;\n          this.selectError = null;\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError);\n          }\n\n          this.selectError = selectError;\n        }\n      }\n    } // Use query data\n    else {\n      data = state.data;\n    } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && status === 'loading') {\n      let placeholderData; // Memoize placeholder data\n\n      if (prevResult != null && prevResult.isPlaceholderData && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n            this.selectError = null;\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError);\n            }\n\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = replaceData(prevResult == null ? void 0 : prevResult.data, placeholderData, options);\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    const isFetching = fetchStatus === 'fetching';\n    const isLoading = status === 'loading';\n    const isError = status === 'error';\n    const result = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  }\n\n  updateResult(notifyOptions) {\n    const prevResult = this.currentResult;\n    const nextResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify and update result if something has changed\n\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return;\n    }\n\n    this.currentResult = nextResult; // Determine which callbacks to trigger\n\n    const defaultNotifyOptions = {\n      cache: true\n    };\n\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n\n      const {\n        notifyOnChangeProps\n      } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === 'function' ? notifyOnChangeProps() : notifyOnChangeProps;\n\n      if (notifyOnChangePropsValue === 'all' || !notifyOnChangePropsValue && !this.trackedProps.size) {\n        return true;\n      }\n\n      const includedProps = new Set(notifyOnChangePropsValue != null ? notifyOnChangePropsValue : this.trackedProps);\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error');\n      }\n\n      return Object.keys(this.currentResult).some(key => {\n        const typedKey = key;\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify({ ...defaultNotifyOptions,\n      ...notifyOptions\n    });\n  }\n\n  updateQuery() {\n    const query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    const prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n\n  onQueryUpdate(action) {\n    const notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual;\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  }\n\n  notify(notifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        var _this$options$onSucce, _this$options, _this$options$onSettl, _this$options2;\n\n        (_this$options$onSucce = (_this$options = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options, this.currentResult.data);\n        (_this$options$onSettl = (_this$options2 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options2, this.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        var _this$options$onError, _this$options3, _this$options$onSettl2, _this$options4;\n\n        (_this$options$onError = (_this$options3 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options3, this.currentResult.error);\n        (_this$options$onSettl2 = (_this$options4 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options4, undefined, this.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({\n          listener\n        }) => {\n          listener(this.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  }\n\n}\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n} // this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\n\n\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult, options) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false;\n  } // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n\n\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData;\n  } // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n\n\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  } // basically, just keep previous properties if nothing changed\n\n\n  return false;\n}\n\nexport { QueryObserver };\n//# sourceMappingURL=queryObserver.mjs.map\n", "import { difference, replaceAt } from './utils.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { QueryObserver } from './queryObserver.mjs';\nimport { Subscribable } from './subscribable.mjs';\n\nclass QueriesObserver extends Subscribable {\n  constructor(client, queries) {\n    super();\n    this.client = client;\n    this.queries = [];\n    this.result = [];\n    this.observers = [];\n    this.observersMap = {};\n\n    if (queries) {\n      this.setQueries(queries);\n    }\n  }\n\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.observers.forEach(observer => {\n        observer.subscribe(result => {\n          this.onUpdate(observer, result);\n        });\n      });\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.listeners.size) {\n      this.destroy();\n    }\n  }\n\n  destroy() {\n    this.listeners = new Set();\n    this.observers.forEach(observer => {\n      observer.destroy();\n    });\n  }\n\n  setQueries(queries, notifyOptions) {\n    this.queries = queries;\n    notifyManager.batch(() => {\n      const prevObservers = this.observers;\n      const newObserverMatches = this.findMatchingObservers(this.queries); // set options for the new observers to notify of changes\n\n      newObserverMatches.forEach(match => match.observer.setOptions(match.defaultedQueryOptions, notifyOptions));\n      const newObservers = newObserverMatches.map(match => match.observer);\n      const newObserversMap = Object.fromEntries(newObservers.map(observer => [observer.options.queryHash, observer]));\n      const newResult = newObservers.map(observer => observer.getCurrentResult());\n      const hasIndexChange = newObservers.some((observer, index) => observer !== prevObservers[index]);\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n\n      this.observers = newObservers;\n      this.observersMap = newObserversMap;\n      this.result = newResult;\n\n      if (!this.hasListeners()) {\n        return;\n      }\n\n      difference(prevObservers, newObservers).forEach(observer => {\n        observer.destroy();\n      });\n      difference(newObservers, prevObservers).forEach(observer => {\n        observer.subscribe(result => {\n          this.onUpdate(observer, result);\n        });\n      });\n      this.notify();\n    });\n  }\n\n  getCurrentResult() {\n    return this.result;\n  }\n\n  getQueries() {\n    return this.observers.map(observer => observer.getCurrentQuery());\n  }\n\n  getObservers() {\n    return this.observers;\n  }\n\n  getOptimisticResult(queries) {\n    return this.findMatchingObservers(queries).map(match => match.observer.getOptimisticResult(match.defaultedQueryOptions));\n  }\n\n  findMatchingObservers(queries) {\n    const prevObservers = this.observers;\n    const prevObserversMap = new Map(prevObservers.map(observer => [observer.options.queryHash, observer]));\n    const defaultedQueryOptions = queries.map(options => this.client.defaultQueryOptions(options));\n    const matchingObservers = defaultedQueryOptions.flatMap(defaultedOptions => {\n      const match = prevObserversMap.get(defaultedOptions.queryHash);\n\n      if (match != null) {\n        return [{\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        }];\n      }\n\n      return [];\n    });\n    const matchedQueryHashes = new Set(matchingObservers.map(match => match.defaultedQueryOptions.queryHash));\n    const unmatchedQueries = defaultedQueryOptions.filter(defaultedOptions => !matchedQueryHashes.has(defaultedOptions.queryHash));\n    const matchingObserversSet = new Set(matchingObservers.map(match => match.observer));\n    const unmatchedObservers = prevObservers.filter(prevObserver => !matchingObserversSet.has(prevObserver));\n\n    const getObserver = options => {\n      const defaultedOptions = this.client.defaultQueryOptions(options);\n      const currentObserver = this.observersMap[defaultedOptions.queryHash];\n      return currentObserver != null ? currentObserver : new QueryObserver(this.client, defaultedOptions);\n    };\n\n    const newOrReusedObservers = unmatchedQueries.map((options, index) => {\n      if (options.keepPreviousData) {\n        // return previous data from one of the observers that no longer match\n        const previouslyUsedObserver = unmatchedObservers[index];\n\n        if (previouslyUsedObserver !== undefined) {\n          return {\n            defaultedQueryOptions: options,\n            observer: previouslyUsedObserver\n          };\n        }\n      }\n\n      return {\n        defaultedQueryOptions: options,\n        observer: getObserver(options)\n      };\n    });\n\n    const sortMatchesByOrderOfQueries = (a, b) => defaultedQueryOptions.indexOf(a.defaultedQueryOptions) - defaultedQueryOptions.indexOf(b.defaultedQueryOptions);\n\n    return matchingObservers.concat(newOrReusedObservers).sort(sortMatchesByOrderOfQueries);\n  }\n\n  onUpdate(observer, result) {\n    const index = this.observers.indexOf(observer);\n\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result);\n      this.notify();\n    }\n  }\n\n  notify() {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(this.result);\n      });\n    });\n  }\n\n}\n\nexport { QueriesObserver };\n//# sourceMappingURL=queriesObserver.mjs.map\n", "import { QueryObserver } from './queryObserver.mjs';\nimport { infiniteQueryBehavior, hasNextPage, hasPreviousPage } from './infiniteQueryBehavior.mjs';\n\nclass InfiniteQueryObserver extends QueryObserver {\n  // Type override\n  // Type override\n  // Type override\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(client, options) {\n    super(client, options);\n  }\n\n  bindMethods() {\n    super.bindMethods();\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  }\n\n  setOptions(options, notifyOptions) {\n    super.setOptions({ ...options,\n      behavior: infiniteQueryBehavior()\n    }, notifyOptions);\n  }\n\n  getOptimisticResult(options) {\n    options.behavior = infiniteQueryBehavior();\n    return super.getOptimisticResult(options);\n  }\n\n  fetchNextPage({\n    pageParam,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        fetchMore: {\n          direction: 'forward',\n          pageParam\n        }\n      }\n    });\n  }\n\n  fetchPreviousPage({\n    pageParam,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        fetchMore: {\n          direction: 'backward',\n          pageParam\n        }\n      }\n    });\n  }\n\n  createResult(query, options) {\n    var _state$fetchMeta, _state$fetchMeta$fetc, _state$fetchMeta2, _state$fetchMeta2$fet, _state$data, _state$data2;\n\n    const {\n      state\n    } = query;\n    const result = super.createResult(query, options);\n    const {\n      isFetching,\n      isRefetching\n    } = result;\n    const isFetchingNextPage = isFetching && ((_state$fetchMeta = state.fetchMeta) == null ? void 0 : (_state$fetchMeta$fetc = _state$fetchMeta.fetchMore) == null ? void 0 : _state$fetchMeta$fetc.direction) === 'forward';\n    const isFetchingPreviousPage = isFetching && ((_state$fetchMeta2 = state.fetchMeta) == null ? void 0 : (_state$fetchMeta2$fet = _state$fetchMeta2.fetchMore) == null ? void 0 : _state$fetchMeta2$fet.direction) === 'backward';\n    return { ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, (_state$data = state.data) == null ? void 0 : _state$data.pages),\n      hasPreviousPage: hasPreviousPage(options, (_state$data2 = state.data) == null ? void 0 : _state$data2.pages),\n      isFetchingNextPage,\n      isFetchingPreviousPage,\n      isRefetching: isRefetching && !isFetchingNextPage && !isFetchingPreviousPage\n    };\n  }\n\n}\n\nexport { InfiniteQueryObserver };\n//# sourceMappingURL=infiniteQueryObserver.mjs.map\n", "import { getDefaultState } from './mutation.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { Subscribable } from './subscribable.mjs';\nimport { shallowEqualObjects } from './utils.mjs';\n\n// CLASS\nclass MutationObserver extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.updateResult();\n  }\n\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n\n  setOptions(options) {\n    var _this$currentMutation;\n\n    const prevOptions = this.options;\n    this.options = this.client.defaultMutationOptions(options);\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this\n      });\n    }\n\n    (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.setOptions(this.options);\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$currentMutation2;\n\n      (_this$currentMutation2 = this.currentMutation) == null ? void 0 : _this$currentMutation2.removeObserver(this);\n    }\n  }\n\n  onMutationUpdate(action) {\n    this.updateResult(); // Determine which callbacks to trigger\n\n    const notifyOptions = {\n      listeners: true\n    };\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true;\n    }\n\n    this.notify(notifyOptions);\n  }\n\n  getCurrentResult() {\n    return this.currentResult;\n  }\n\n  reset() {\n    this.currentMutation = undefined;\n    this.updateResult();\n    this.notify({\n      listeners: true\n    });\n  }\n\n  mutate(variables, options) {\n    this.mutateOptions = options;\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this);\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, { ...this.options,\n      variables: typeof variables !== 'undefined' ? variables : this.options.variables\n    });\n    this.currentMutation.addObserver(this);\n    return this.currentMutation.execute();\n  }\n\n  updateResult() {\n    const state = this.currentMutation ? this.currentMutation.state : getDefaultState();\n    const isLoading = state.status === 'loading';\n    const result = { ...state,\n      isLoading,\n      isPending: isLoading,\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset\n    };\n    this.currentResult = result;\n  }\n\n  notify(options) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          var _this$mutateOptions$o, _this$mutateOptions, _this$mutateOptions$o2, _this$mutateOptions2;\n\n          (_this$mutateOptions$o = (_this$mutateOptions = this.mutateOptions).onSuccess) == null ? void 0 : _this$mutateOptions$o.call(_this$mutateOptions, this.currentResult.data, this.currentResult.variables, this.currentResult.context);\n          (_this$mutateOptions$o2 = (_this$mutateOptions2 = this.mutateOptions).onSettled) == null ? void 0 : _this$mutateOptions$o2.call(_this$mutateOptions2, this.currentResult.data, null, this.currentResult.variables, this.currentResult.context);\n        } else if (options.onError) {\n          var _this$mutateOptions$o3, _this$mutateOptions3, _this$mutateOptions$o4, _this$mutateOptions4;\n\n          (_this$mutateOptions$o3 = (_this$mutateOptions3 = this.mutateOptions).onError) == null ? void 0 : _this$mutateOptions$o3.call(_this$mutateOptions3, this.currentResult.error, this.currentResult.variables, this.currentResult.context);\n          (_this$mutateOptions$o4 = (_this$mutateOptions4 = this.mutateOptions).onSettled) == null ? void 0 : _this$mutateOptions$o4.call(_this$mutateOptions4, undefined, this.currentResult.error, this.currentResult.variables, this.currentResult.context);\n        }\n      } // Then trigger the listeners\n\n\n      if (options.listeners) {\n        this.listeners.forEach(({\n          listener\n        }) => {\n          listener(this.currentResult);\n        });\n      }\n    });\n  }\n\n}\n\nexport { MutationObserver };\n//# sourceMappingURL=mutationObserver.mjs.map\n", "// TYPES\n// FUNCTIONS\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state\n  };\n} // Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\n\n\nfunction dehydrateQuery(query) {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash\n  };\n}\n\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === 'success';\n}\nfunction dehydrate(client, options = {}) {\n  const mutations = [];\n  const queries = [];\n\n  if (options.dehydrateMutations !== false) {\n    const shouldDehydrateMutation = options.shouldDehydrateMutation || defaultShouldDehydrateMutation;\n    client.getMutationCache().getAll().forEach(mutation => {\n      if (shouldDehydrateMutation(mutation)) {\n        mutations.push(dehydrateMutation(mutation));\n      }\n    });\n  }\n\n  if (options.dehydrateQueries !== false) {\n    const shouldDehydrateQuery = options.shouldDehydrateQuery || defaultShouldDehydrateQuery;\n    client.getQueryCache().getAll().forEach(query => {\n      if (shouldDehydrateQuery(query)) {\n        queries.push(dehydrateQuery(query));\n      }\n    });\n  }\n\n  return {\n    mutations,\n    queries\n  };\n}\nfunction hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return;\n  }\n\n  const mutationCache = client.getMutationCache();\n  const queryCache = client.getQueryCache(); // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n\n  const mutations = dehydratedState.mutations || []; // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n\n  const queries = dehydratedState.queries || [];\n  mutations.forEach(dehydratedMutation => {\n    var _options$defaultOptio;\n\n    mutationCache.build(client, { ...(options == null ? void 0 : (_options$defaultOptio = options.defaultOptions) == null ? void 0 : _options$defaultOptio.mutations),\n      mutationKey: dehydratedMutation.mutationKey\n    }, dehydratedMutation.state);\n  });\n  queries.forEach(({\n    queryKey,\n    state,\n    queryHash\n  }) => {\n    var _options$defaultOptio2;\n\n    const query = queryCache.get(queryHash); // Do not hydrate if an existing query exists with newer data\n\n    if (query) {\n      if (query.state.dataUpdatedAt < state.dataUpdatedAt) {\n        // omit fetchStatus from dehydrated state\n        // so that query stays in its current fetchStatus\n        const {\n          fetchStatus: _ignored,\n          ...dehydratedQueryState\n        } = state;\n        query.setState(dehydratedQueryState);\n      }\n\n      return;\n    } // Restore query\n\n\n    queryCache.build(client, { ...(options == null ? void 0 : (_options$defaultOptio2 = options.defaultOptions) == null ? void 0 : _options$defaultOptio2.queries),\n      queryKey,\n      queryHash\n    }, // Reset fetch status to idle to avoid\n    // query being stuck in fetching state upon hydration\n    { ...state,\n      fetchStatus: 'idle'\n    });\n  });\n}\n\nexport { defaultShouldDehydrateMutation, defaultShouldDehydrateQuery, dehydrate, hydrate };\n//# sourceMappingURL=hydration.mjs.map\n", "'use client'\nimport * as ReactDOM from 'react-dom'\n\nexport const unstable_batchedUpdates = ReactDOM.unstable_batchedUpdates\n", "import { notifyManager } from '@tanstack/query-core'\nimport { unstable_batchedUpdates } from './reactBatchedUpdates'\n\nnotifyManager.setBatchNotifyFunction(unstable_batchedUpdates)\n", "/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n// dispatch for CommonJS interop named imports.\n\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\nvar didWarnOld18Alpha = false;\nvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  {\n    if (!didWarnOld18Alpha) {\n      if (React.startTransition !== undefined) {\n        didWarnOld18Alpha = true;\n\n        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  var value = getSnapshot();\n\n  {\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n\n      if (!objectIs(value, cachedValue)) {\n        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  var _useState = useState({\n    inst: {\n      value: value,\n      getSnapshot: getSnapshot\n    }\n  }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n\n    var handleStoreChange = function () {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst: inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange);\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  var prevValue = inst.value;\n\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\nvar isServerEnvironment = !canUseDOM;\n\nvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\nvar useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n\nexports.useSyncExternalStore = useSyncExternalStore$2;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "'use client'\n// Temporary workaround due to an issue with react-native uSES - https://github.com/TanStack/query/pull/3601\nimport { useSyncExternalStore as uSES } from 'use-sync-external-store/shim/index.js'\n\nexport const useSyncExternalStore = uSES\n", "'use client'\nimport * as React from 'react'\n\nimport type { QueryClient } from '@tanstack/query-core'\nimport type { ContextOptions } from './types'\n\ndeclare global {\n  interface Window {\n    ReactQueryClientContext?: React.Context<QueryClient | undefined>\n  }\n}\n\nexport const defaultContext = React.createContext<QueryClient | undefined>(\n  undefined,\n)\nconst QueryClientSharingContext = React.createContext<boolean>(false)\n\n// If we are given a context, we will use it.\n// Otherwise, if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\nfunction getQueryClientContext(\n  context: React.Context<QueryClient | undefined> | undefined,\n  contextSharing: boolean,\n) {\n  if (context) {\n    return context\n  }\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext\n    }\n\n    return window.ReactQueryClientContext\n  }\n\n  return defaultContext\n}\n\nexport const useQueryClient = ({ context }: ContextOptions = {}) => {\n  const queryClient = React.useContext(\n    getQueryClientContext(context, React.useContext(QueryClientSharingContext)),\n  )\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return queryClient\n}\n\ntype QueryClientProviderPropsBase = {\n  client: QueryClient\n  children?: React.ReactNode\n}\ntype QueryClientProviderPropsWithContext = ContextOptions & {\n  contextSharing?: never\n} & QueryClientProviderPropsBase\ntype QueryClientProviderPropsWithContextSharing = {\n  context?: never\n  contextSharing?: boolean\n} & QueryClientProviderPropsBase\n\nexport type QueryClientProviderProps =\n  | QueryClientProviderPropsWithContext\n  | QueryClientProviderPropsWithContextSharing\n\nexport const QueryClientProvider = ({\n  client,\n  children,\n  context,\n  contextSharing = false,\n}: QueryClientProviderProps): JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  if (process.env.NODE_ENV !== 'production' && contextSharing) {\n    client\n      .getLogger()\n      .error(\n        `The contextSharing option has been deprecated and will be removed in the next major version`,\n      )\n  }\n\n  const Context = getQueryClientContext(context, contextSharing)\n\n  return (\n    <QueryClientSharingContext.Provider value={!context && contextSharing}>\n      <Context.Provider value={client}>{children}</Context.Provider>\n    </QueryClientSharingContext.Provider>\n  )\n}\n", "'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n", "'use client'\nimport * as React from 'react'\n\n// CONTEXT\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: () => void\n  isReset: () => boolean\n  reset: () => void\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport interface QueryErrorResetBoundaryProps {\n  children:\n    | ((value: QueryErrorResetBoundaryValue) => React.ReactNode)\n    | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function'\n        ? (children as Function)(value)\n        : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n", "export function shouldThrowError<T extends (...args: any[]) => boolean>(\n  _useErrorBoundary: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params)\n  }\n\n  return !!_useErrorBoundary\n}\n", "'use client'\nimport * as React from 'react'\nimport { shouldThrowError } from './utils'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  UseErrorBoundary,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (options.suspense || options.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  useErrorBoundary,\n  query,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  useErrorBoundary: UseErrorBoundary<\n    TQueryFnData,\n    TError,\n    TQueryData,\n    TQueryKey\n  >\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    shouldThrowError(useErrorBoundary, [result.error, query])\n  )\n}\n", "import type { DefaultedQueryObserverOptions } from '@tanstack/query-core'\nimport type { QueryObserver } from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\nimport type { QueryObserverResult } from '@tanstack/query-core'\nimport type { QueryKey } from '@tanstack/query-core'\n\nexport const ensureStaleTime = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => defaultedOptions?.suspense && willFetch(result, isRestoring)\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer\n    .fetchOptimistic(defaultedOptions)\n    .then(({ data }) => {\n      defaultedOptions.onSuccess?.(data as TData)\n      defaultedOptions.onSettled?.(data, null)\n    })\n    .catch((error) => {\n      errorResetBoundary.clearReset()\n      defaultedOptions.onError?.(error)\n      defaultedOptions.onSettled?.(undefined, error)\n    })\n", "'use client'\nimport * as React from 'react'\n\nimport { QueriesObserver, notifyManager } from '@tanstack/query-core'\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useIsRestoring } from './isRestoring'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport {\n  ensureStaleTime,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport type { OmitKeyof, QueryFunction, QueryKey } from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\n\n// This defines the `UseQueryOptions` that are accepted in `QueriesOptions` & `GetOptions`.\n// - `context` is omitted as it is passed as a root-level option to `useQueries` instead.\ntype UseQueryOptionsForUseQueries<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'context'\n>\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryOptionsForUseQueries<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryOptionsForUseQueries<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey> }\n    ? UseQueryOptionsForUseQueries<\n        TQueryFnData,\n        unknown,\n        TQueryFnData,\n        TQueryKey\n      >\n    : // Fallback\n      UseQueryOptionsForUseQueries\n\n// A defined initialData setting should return a DefinedUseQueryResult rather than UseQueryResult\ntype GetDefinedOrUndefinedQueryResult<T, TData, TError = unknown> = T extends {\n  initialData?: infer TInitialData\n}\n  ? unknown extends TInitialData\n    ? UseQueryResult<TData, TError>\n    : TInitialData extends TData\n    ? DefinedUseQueryResult<TData, TError>\n    : TInitialData extends () => infer TInitialDataResult\n    ? unknown extends TInitialDataResult\n      ? UseQueryResult<TData, TError>\n      : TInitialDataResult extends TData\n      ? DefinedUseQueryResult<TData, TError>\n      : UseQueryResult<TData, TError>\n    : UseQueryResult<TData, TError>\n  : UseQueryResult<TData, TError>\n\ntype GetUseQueryResult<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? GetDefinedOrUndefinedQueryResult<T, TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<unknown, any>\n        select: (data: any) => infer TData\n      }\n    ? GetDefinedOrUndefinedQueryResult<T, TData>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, any> }\n    ? GetDefinedOrUndefinedQueryResult<T, TQueryFnData>\n    : // Fallback\n      UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends any[],\n  TResult extends any[] = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryOptionsForUseQueries[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...TResult, GetOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesOptions<[...Tail], [...TResult, GetOptions<Head>], [...TDepth, 1]>\n  : unknown[] extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends UseQueryOptionsForUseQueries<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      infer TQueryKey\n    >[]\n  ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData, TQueryKey>[]\n  : // Fallback\n    UseQueryOptionsForUseQueries[]\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends any[],\n  TResults extends any[] = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryResult[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...TResults, GetUseQueryResult<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesResults<\n      [...Tail],\n      [...TResults, GetUseQueryResult<Head>],\n      [...TDepth, 1]\n    >\n  : T extends UseQueryOptionsForUseQueries<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      any\n    >[]\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    UseQueryResult<unknown extends TData ? TQueryFnData : TData, TError>[]\n  : // Fallback\n    UseQueryResult[]\n\nexport function useQueries<T extends any[]>({\n  queries,\n  context,\n}: {\n  queries: readonly [...QueriesOptions<T>]\n  context?: UseQueryOptions['context']\n}): QueriesResults<T> {\n  const queryClient = useQueryClient({ context })\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n\n  const defaultedQueries = React.useMemo(\n    () =>\n      queries.map((options) => {\n        const defaultedOptions = queryClient.defaultQueryOptions(options)\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions._optimisticResults = isRestoring\n          ? 'isRestoring'\n          : 'optimistic'\n\n        return defaultedOptions\n      }),\n    [queries, queryClient, isRestoring],\n  )\n\n  defaultedQueries.forEach((query) => {\n    ensureStaleTime(query)\n    ensurePreventErrorBoundaryRetry(query, errorResetBoundary)\n  })\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () => new QueriesObserver(queryClient, defaultedQueries),\n  )\n\n  const optimisticResult = observer.getOptimisticResult(defaultedQueries)\n\n  useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        isRestoring\n          ? () => undefined\n          : observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer, isRestoring],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, { listeners: false })\n  }, [defaultedQueries, observer])\n\n  const shouldAtLeastOneSuspend = optimisticResult.some((result, index) =>\n    shouldSuspend(defaultedQueries[index], result, isRestoring),\n  )\n\n  const suspensePromises = shouldAtLeastOneSuspend\n    ? optimisticResult.flatMap((result, index) => {\n        const options = defaultedQueries[index]\n        const queryObserver = observer.getObservers()[index]\n\n        if (options && queryObserver) {\n          if (shouldSuspend(options, result, isRestoring)) {\n            return fetchOptimistic(options, queryObserver, errorResetBoundary)\n          } else if (willFetch(result, isRestoring)) {\n            void fetchOptimistic(options, queryObserver, errorResetBoundary)\n          }\n        }\n        return []\n      })\n    : []\n\n  if (suspensePromises.length > 0) {\n    throw Promise.all(suspensePromises)\n  }\n  const observerQueries = observer.getQueries()\n  const firstSingleResultWhichShouldThrow = optimisticResult.find(\n    (result, index) =>\n      getHasError({\n        result,\n        errorResetBoundary,\n        useErrorBoundary: defaultedQueries[index]?.useErrorBoundary ?? false,\n        query: observerQueries[index]!,\n      }),\n  )\n\n  if (firstSingleResultWhichShouldThrow?.error) {\n    throw firstSingleResultWhichShouldThrow.error\n  }\n\n  return optimisticResult as QueriesResults<T>\n}\n", "'use client'\nimport * as React from 'react'\n\nimport { notifyManager } from '@tanstack/query-core'\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useIsRestoring } from './isRestoring'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { ensureStaleTime, fetchOptimistic, shouldSuspend } from './suspense'\nimport type { QueryKey, QueryObserver } from '@tanstack/query-core'\nimport type { UseBaseQueryOptions } from './types'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryK<PERSON>\n  >,\n  Observer: typeof QueryObserver,\n) {\n  const queryClient = useQueryClient({ context: options.context })\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const defaultedOptions = queryClient.defaultQueryOptions(options)\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  // Include callbacks in batch renders\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(\n      defaultedOptions.onError,\n    )\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(\n      defaultedOptions.onSuccess,\n    )\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(\n      defaultedOptions.onSettled,\n    )\n  }\n\n  ensureStaleTime(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        queryClient,\n        defaultedOptions,\n      ),\n  )\n\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = isRestoring\n          ? () => undefined\n          : observer.subscribe(notifyManager.batchCalls(onStoreChange))\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, isRestoring],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, { listeners: false })\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result, isRestoring)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      useErrorBoundary: defaultedOptions.useErrorBoundary,\n      query: observer.getCurrentQuery(),\n    })\n  ) {\n    throw result.error\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n", "'use client'\nimport { QueryObserver, parseQueryArgs } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  InitialDataFunction,\n  NonUndefinedGuard,\n  OmitKeyof,\n  QueryFunction,\n  QueryKey,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\n\n// HOOK\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & {\n    initialData:\n      | NonUndefinedGuard<TQueryFnData>\n      | (() => NonUndefinedGuard<TQueryFnData>)\n  },\n): DefinedUseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & {\n    initialData?:\n      | undefined\n      | InitialDataFunction<NonUndefinedGuard<TQueryFnData>>\n      | NonUndefinedGuard<TQueryFnData>\n  },\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError>\n\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  >,\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey'\n  >,\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData?: () => undefined },\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn'\n  >,\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg3?: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError> {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(parsedOptions, QueryObserver)\n}\n", "import { QueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type { QueryKey } from '@tanstack/query-core'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\n\nexport function useSuspenseQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(options: UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>) {\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      useErrorBoundary: true,\n      suspense: true,\n      placeholderData: undefined,\n      networkMode: 'always',\n      onSuccess: undefined,\n      onError: undefined,\n      onSettled: undefined,\n    },\n    QueryObserver,\n  ) as UseSuspenseQueryResult<TData, TError>\n}\n", "import { useQueries } from './useQueries'\nimport type {\n  UseQueryOptions,\n  UseSuspenseQueryOptions,\n  UseSuspenseQueryResult,\n} from './types'\nimport type { NetworkMode, QueryFunction } from '@tanstack/query-core'\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetSuspenseOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseSuspenseQueryOptions<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseSuspenseQueryOptions<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseSuspenseQueryOptions<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseSuspenseQueryOptions<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseSuspenseQueryOptions<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select?: (data: any) => infer TData\n      }\n    ? UseSuspenseQueryOptions<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n      }\n    ? UseSuspenseQueryOptions<TQueryFnData, unknown, TQueryFnData, TQueryKey>\n    : // Fallback\n      UseSuspenseQueryOptions\n\ntype GetSuspenseResults<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseSuspenseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseSuspenseQueryResult<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseSuspenseQueryResult<TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? UseSuspenseQueryResult<TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseSuspenseQueryResult<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseSuspenseQueryResult<TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, any>\n        select?: (data: any) => infer TData\n      }\n    ? UseSuspenseQueryResult<unknown extends TData ? TQueryFnData : TData>\n    : T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, any>\n      }\n    ? UseSuspenseQueryResult<TQueryFnData>\n    : // Fallback\n      UseSuspenseQueryResult\n\n/**\n * SuspenseQueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type SuspenseQueriesOptions<\n  T extends Array<any>,\n  TResult extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryOptions>\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...TResult, GetSuspenseOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? SuspenseQueriesOptions<\n      [...Tail],\n      [...TResult, GetSuspenseOptions<Head>],\n      [...TDepth, 1]\n    >\n  : Array<unknown> extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends Array<\n      UseSuspenseQueryOptions<\n        infer TQueryFnData,\n        infer TError,\n        infer TData,\n        infer TQueryKey\n      >\n    >\n  ? Array<UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>>\n  : // Fallback\n    Array<UseSuspenseQueryOptions>\n\n/**\n * SuspenseQueriesResults reducer recursively maps type param to results\n */\nexport type SuspenseQueriesResults<\n  T extends Array<any>,\n  TResult extends Array<any> = [],\n  TDepth extends ReadonlyArray<number> = [],\n> = TDepth['length'] extends MAXIMUM_DEPTH\n  ? Array<UseSuspenseQueryResult>\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...TResult, GetSuspenseResults<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? SuspenseQueriesResults<\n      [...Tail],\n      [...TResult, GetSuspenseResults<Head>],\n      [...TDepth, 1]\n    >\n  : T extends Array<\n      UseSuspenseQueryOptions<\n        infer TQueryFnData,\n        infer TError,\n        infer TData,\n        any\n      >\n    >\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    Array<\n      UseSuspenseQueryResult<\n        unknown extends TData ? TQueryFnData : TData,\n        TError\n      >\n    >\n  : // Fallback\n    Array<UseSuspenseQueryResult>\n\nexport function useSuspenseQueries<T extends any[]>({\n  queries,\n  context,\n}: {\n  queries: readonly [...SuspenseQueriesOptions<T>]\n  context?: UseQueryOptions['context']\n}): SuspenseQueriesResults<T> {\n  return useQueries({\n    queries: queries.map((query) => ({\n      ...query,\n      enabled: true,\n      useErrorBoundary: true,\n      suspense: true,\n      placeholderData: undefined,\n      networkMode: 'always' as NetworkMode,\n    })),\n    context,\n  }) as SuspenseQueriesResults<T>\n}\n", "import type {\n  InitialDataFunction,\n  NonUndefinedGuard,\n  OmitKeyof,\n  Query<PERSON><PERSON>,\n  WithRequired,\n} from '@tanstack/query-core'\nimport type { UseQueryOptions } from './types'\n\ntype UseQueryOptionsOmitted<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  T<PERSON>ueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'onSuccess' | 'onError' | 'onSettled' | 'refetchInterval'\n>\n\ntype ProhibitedQueryOptionsKeyInV5 = keyof Pick<\n  UseQueryOptionsOmitted,\n  'useErrorBoundary' | 'suspense' | 'getNextPageParam' | 'getPreviousPageParam'\n>\n\nexport type UndefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  T<PERSON><PERSON>y<PERSON>ey extends QueryKey = QueryKey,\n> = UseQueryOptionsOmitted<TQueryFnData, TError, TData, TQueryKey> & {\n  initialData?:\n    | undefined\n    | InitialDataFunction<NonUndefinedGuard<TQueryFnData>>\n    | NonUndefinedGuard<TQueryFnData>\n}\n\nexport type DefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = UseQueryOptionsOmitted<TQueryFnData, TError, TData, TQueryKey> & {\n  initialData:\n    | NonUndefinedGuard<TQueryFnData>\n    | (() => NonUndefinedGuard<TQueryFnData>)\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: WithRequired<\n    OmitKeyof<\n      DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n      ProhibitedQueryOptionsKeyInV5\n    >,\n    'queryKey'\n  >,\n): WithRequired<\n  DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'queryKey'\n>\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: WithRequired<\n    OmitKeyof<\n      UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n      ProhibitedQueryOptionsKeyInV5\n    >,\n    'queryKey'\n  >,\n): WithRequired<\n  UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'queryKey'\n>\n\nexport function queryOptions(options: unknown) {\n  return options\n}\n", "'use client'\nimport * as React from 'react'\n\nimport { hydrate } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { HydrateOptions } from '@tanstack/query-core'\nimport type { ContextOptions } from './types'\n\nexport function useHydrate(\n  state: unknown,\n  options: HydrateOptions & ContextOptions = {},\n) {\n  const queryClient = useQueryClient({ context: options.context })\n\n  const optionsRef = React.useRef(options)\n  optionsRef.current = options\n\n  // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n  React.useMemo(() => {\n    if (state) {\n      hydrate(queryClient, state, optionsRef.current)\n    }\n  }, [queryClient, state])\n}\n\nexport interface HydrateProps {\n  state?: unknown\n  options?: HydrateOptions\n  children?: React.ReactNode\n}\n\nexport const Hydrate = ({ children, options, state }: HydrateProps) => {\n  useHydrate(state, options)\n  return children as React.ReactElement\n}\n", "'use client'\nimport * as React from 'react'\nimport { notifyManager, parseFilterArgs } from '@tanstack/query-core'\n\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { ContextOptions } from './types'\nimport type { QueryFilters, QueryKey } from '@tanstack/query-core'\n\ninterface Options extends ContextOptions {}\n\nexport function useIsFetching(filters?: QueryFilters, options?: Options): number\nexport function useIsFetching(\n  queryKey?: QueryKey,\n  filters?: QueryFilters,\n  options?: Options,\n): number\nexport function useIsFetching(\n  arg1?: QueryKey | QueryFilters,\n  arg2?: QueryFilters | Options,\n  arg3?: Options,\n): number {\n  const [filters, options = {}] = parseFilterArgs(arg1, arg2, arg3)\n  const queryClient = useQueryClient({ context: options.context })\n  const queryCache = queryClient.getQueryCache()\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        queryCache.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [queryCache],\n    ),\n    () => queryClient.isFetching(filters),\n    () => queryClient.isFetching(filters),\n  )\n}\n", "'use client'\nimport * as React from 'react'\nimport { notify<PERSON>anager, parseMutationFilterArgs } from '@tanstack/query-core'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport { useQueryClient } from './QueryClientProvider'\nimport type { MutationFilters, MutationKey } from '@tanstack/query-core'\nimport type { ContextOptions } from './types'\n\ninterface Options extends ContextOptions {}\n\nexport function useIsMutating(\n  filters?: MutationFilters,\n  options?: Options,\n): number\nexport function useIsMutating(\n  mutationKey?: MutationKey,\n  filters?: Omit<MutationFilters, 'mutationKey'>,\n  options?: Options,\n): number\nexport function useIsMutating(\n  arg1?: MutationKey | MutationFilters,\n  arg2?: Omit<MutationFilters, 'mutationKey'> | Options,\n  arg3?: Options,\n): number {\n  const [filters, options = {}] = parseMutationFilterArgs(arg1, arg2, arg3)\n\n  const queryClient = useQueryClient({ context: options.context })\n  const mutationCache = queryClient.getMutationCache()\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        mutationCache.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [mutationCache],\n    ),\n    () => queryClient.isMutating(filters),\n    () => queryClient.isMutating(filters),\n  )\n}\n", "'use client'\nimport * as React from 'react'\nimport {\n  MutationObserver,\n  notifyManager,\n  parseMutationArgs,\n} from '@tanstack/query-core'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport { useQueryClient } from './QueryClientProvider'\nimport { shouldThrowError } from './utils'\nimport type { MutationFunction, MutationKey } from '@tanstack/query-core'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationFn: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationFn'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MutationKey,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MutationKey,\n  mutationFn?: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey' | 'mutationFn'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  arg1:\n    | MutationKey\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg2?:\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg3?: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const options = parseMutationArgs(arg1, arg2, arg3)\n  const queryClient = useQueryClient({ context: options.context })\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        queryClient,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.useErrorBoundary, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n", "'use client'\nimport { InfiniteQueryObserver, parseQueryArgs } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  QueryFunction,\n  QueryKey,\n  QueryObserver,\n} from '@tanstack/query-core'\nimport type { UseInfiniteQueryOptions, UseInfiniteQueryResult } from './types'\n\n// HOOK\n\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  T<PERSON>uer<PERSON><PERSON><PERSON> extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey'\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryFnData,\n      TQueryKey\n    >,\n    'queryKey' | 'queryFn'\n  >,\n): UseInfiniteQueryResult<TData, TError>\nexport function useInfiniteQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1:\n    | TQueryKey\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseInfiniteQueryOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryFnData,\n        TQueryKey\n      >,\n  arg3?: UseInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryFnData,\n    TQueryKey\n  >,\n): UseInfiniteQueryResult<TData, TError> {\n  const options = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(\n    options,\n    InfiniteQueryObserver as typeof QueryObserver,\n  ) as UseInfiniteQueryResult<TData, TError>\n}\n"], "names": ["noop", "getDefaultState", "unstable_batchedUpdates", "ReactDOM", "notify<PERSON><PERSON>ger", "setBatchNotifyFunction", "require$$0", "require$$1", "useSyncExternalStore", "uSES", "defaultContext", "React", "createContext", "undefined", "QueryClientSharingContext", "getQueryClientContext", "context", "contextSharing", "window", "ReactQueryClientContext", "useQueryClient", "queryClient", "useContext", "Error", "QueryClientProvider", "client", "children", "useEffect", "mount", "unmount", "<PERSON><PERSON><PERSON><PERSON>", "error", "Context", "IsRestoringContext", "useIsRestoring", "IsRestoringProvider", "Provider", "createValue", "isReset", "clear<PERSON><PERSON>t", "reset", "QueryErrorResetBoundaryContext", "useQueryErrorResetBoundary", "QueryErrorResetBoundary", "value", "useState", "shouldThrowError", "_useErrorBoundary", "params", "ensurePreventErrorBoundaryRetry", "options", "errorResetBoundary", "suspense", "useErrorBoundary", "retryOnMount", "useClearResetErrorBoundary", "getHasError", "result", "query", "isError", "isFetching", "ensureStaleTime", "defaultedOptions", "staleTime", "<PERSON><PERSON><PERSON><PERSON>", "isRestoring", "isLoading", "shouldSuspend", "fetchOptimistic", "observer", "then", "data", "onSuccess", "onSettled", "catch", "onError", "useQueries", "queries", "defaultedQueries", "useMemo", "map", "defaultQueryOptions", "_optimisticResults", "for<PERSON>ach", "QueriesObserver", "optimisticResult", "getOptimisticResult", "useCallback", "onStoreChange", "subscribe", "batchCalls", "getCurrentResult", "setQueries", "listeners", "shouldAtLeastOneSuspend", "some", "index", "suspensePromises", "flatMap", "queryObserver", "getObservers", "length", "Promise", "all", "observerQueries", "getQueries", "firstSingleResultWhichShouldThrow", "find", "useBaseQuery", "Observer", "unsubscribe", "updateResult", "setOptions", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notifyOnChangeProps", "trackResult", "useQuery", "arg1", "arg2", "arg3", "parsedOptions", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "QueryObserver", "useSuspenseQuery", "enabled", "placeholderData", "networkMode", "useSuspenseQueries", "queryOptions", "useHydrate", "state", "optionsRef", "useRef", "current", "hydrate", "Hydrate", "useIsFetching", "filters", "parseFilter<PERSON><PERSON>s", "queryCache", "get<PERSON><PERSON><PERSON><PERSON>ache", "useIsMutating", "parseMutationFilterArgs", "mutationCache", "getMutationCache", "isMutating", "useMutation", "parseMutationArgs", "MutationObserver", "mutate", "variables", "mutateOptions", "mutateAsync", "useInfiniteQuery", "InfiniteQueryObserver"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,MAAM,YAAY,CAAC;EACnB,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;EAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/C,GAAG;AACH;EACA,EAAE,SAAS,CAAC,QAAQ,EAAE;EACtB,IAAI,MAAM,QAAQ,GAAG;EACrB,MAAM,QAAQ;EACd,KAAK,CAAC;EACN,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;EACjC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;EACvB,IAAI,OAAO,MAAM;EACjB,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;EACtC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;EAC3B,KAAK,CAAC;EACN,GAAG;AACH;EACA,EAAE,YAAY,GAAG;EACjB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC;EACnC,GAAG;AACH;EACA,EAAE,WAAW,GAAG;EAChB,GAAG;AACH;EACA,EAAE,aAAa,GAAG;EAClB,GAAG;AACH;EACA;;EC5BA;EACA;AACK,QAAC,QAAQ,GAAG,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,IAAI,OAAO;EACnE,SAASA,MAAI,GAAG;EAChB,EAAE,OAAO,SAAS,CAAC;EACnB,CAAC;EACD,SAAS,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE;EAC1C,EAAE,OAAO,OAAO,OAAO,KAAK,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;EAClE,CAAC;EACD,SAAS,cAAc,CAAC,KAAK,EAAE;EAC/B,EAAE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,KAAK,QAAQ,CAAC;EACvE,CAAC;EACD,SAAS,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE;EACpC,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC;EACD,SAAS,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;EACxC,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9B,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EACtB,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;EACD,SAAS,cAAc,CAAC,SAAS,EAAE,SAAS,EAAE;EAC9C,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;EAChE,CAAC;EACD,SAAS,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EAC1C,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;EACzB,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;EAClC,IAAI,OAAO,EAAE,GAAG,IAAI;EACpB,MAAM,QAAQ,EAAE,IAAI;EACpB,MAAM,OAAO,EAAE,IAAI;EACnB,KAAK,CAAC;EACN,GAAG;AACH;EACA,EAAE,OAAO,EAAE,GAAG,IAAI;EAClB,IAAI,QAAQ,EAAE,IAAI;EAClB,GAAG,CAAC;EACJ,CAAC;EACD,SAAS,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EAC7C,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;EACxB,IAAI,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;EACpC,MAAM,OAAO,EAAE,GAAG,IAAI;EACtB,QAAQ,WAAW,EAAE,IAAI;EACzB,QAAQ,UAAU,EAAE,IAAI;EACxB,OAAO,CAAC;EACR,KAAK;AACL;EACA,IAAI,OAAO,EAAE,GAAG,IAAI;EACpB,MAAM,WAAW,EAAE,IAAI;EACvB,KAAK,CAAC;EACN,GAAG;AACH;EACA,EAAE,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;EAClC,IAAI,OAAO,EAAE,GAAG,IAAI;EACpB,MAAM,UAAU,EAAE,IAAI;EACtB,KAAK,CAAC;EACN,GAAG;AACH;EACA,EAAE,OAAO,EAAE,GAAG,IAAI;EAClB,GAAG,CAAC;EACJ,CAAC;EACD,SAAS,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EAC3C,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI;EACtC,IAAI,QAAQ,EAAE,IAAI;EAClB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;EAChC,CAAC;EACD,SAAS,uBAAuB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EACnD,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI;EACtC,IAAI,WAAW,EAAE,IAAI;EACrB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;EAChC,CAAC;EACD,SAAS,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE;EACpC,EAAE,MAAM;EACR,IAAI,IAAI,GAAG,KAAK;EAChB,IAAI,KAAK;EACT,IAAI,WAAW;EACf,IAAI,SAAS;EACb,IAAI,QAAQ;EACZ,IAAI,KAAK;EACT,GAAG,GAAG,OAAO,CAAC;AACd;EACA,EAAE,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;EAC5B,IAAI,IAAI,KAAK,EAAE;EACf,MAAM,IAAI,KAAK,CAAC,SAAS,KAAK,qBAAqB,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE;EAC9E,QAAQ,OAAO,KAAK,CAAC;EACrB,OAAO;EACP,KAAK,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;EAC3D,MAAM,OAAO,KAAK,CAAC;EACnB,KAAK;EACL,GAAG;AACH;EACA,EAAE,IAAI,IAAI,KAAK,KAAK,EAAE;EACtB,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;AACtC;EACA,IAAI,IAAI,IAAI,KAAK,QAAQ,IAAI,CAAC,QAAQ,EAAE;EACxC,MAAM,OAAO,KAAK,CAAC;EACnB,KAAK;AACL;EACA,IAAI,IAAI,IAAI,KAAK,UAAU,IAAI,QAAQ,EAAE;EACzC,MAAM,OAAO,KAAK,CAAC;EACnB,KAAK;EACL,GAAG;AACH;EACA,EAAE,IAAI,OAAO,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;EAC/D,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;EACA,EAAE,IAAI,OAAO,WAAW,KAAK,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE;EACrF,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;EACA,EAAE,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;EACtC,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;EACD,SAAS,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE;EAC1C,EAAE,MAAM;EACR,IAAI,KAAK;EACT,IAAI,QAAQ;EACZ,IAAI,SAAS;EACb,IAAI,WAAW;EACf,GAAG,GAAG,OAAO,CAAC;AACd;EACA,EAAE,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE;EAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE;EACvC,MAAM,OAAO,KAAK,CAAC;EACnB,KAAK;AACL;EACA,IAAI,IAAI,KAAK,EAAE;EACf,MAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,YAAY,CAAC,WAAW,CAAC,EAAE;EACpF,QAAQ,OAAO,KAAK,CAAC;EACrB,OAAO;EACP,KAAK,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;EAC5E,MAAM,OAAO,KAAK,CAAC;EACnB,KAAK;EACL,GAAG;AACH;EACA,EAAE,IAAI,OAAO,QAAQ,KAAK,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS,KAAK,QAAQ,EAAE;EACzF,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;EACA,EAAE,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;EACzC,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;EACD,SAAS,qBAAqB,CAAC,QAAQ,EAAE,OAAO,EAAE;EAClD,EAAE,MAAM,MAAM,GAAG,CAAC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,cAAc,KAAK,YAAY,CAAC;EACrF,EAAE,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;EAC1B,CAAC;EACD;EACA;EACA;EACA;AACA;EACA,SAAS,YAAY,CAAC,QAAQ,EAAE;EAChC,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK;EACnH,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;EAC3B,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;EAChB,CAAC;EACD;EACA;EACA;AACA;EACA,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE;EAC/B,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChC,CAAC;EACD;EACA;EACA;AACA;EACA,SAAS,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE;EAChC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;EACf,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,IAAI,OAAO,CAAC,KAAK,OAAO,CAAC,EAAE;EAC7B,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;EACA,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;EAChE,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1E,GAAG;AACH;EACA,EAAE,OAAO,KAAK,CAAC;EACf,CAAC;EACD;EACA;EACA;EACA;EACA;AACA;EACA,SAAS,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE;EAChC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;EACf,IAAI,OAAO,CAAC,CAAC;EACb,GAAG;AACH;EACA,EAAE,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AACnD;EACA,EAAE,IAAI,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;EACrD,IAAI,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;EAC3D,IAAI,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC9C,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;EAChC,IAAI,MAAM,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;EACjC,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;AACvB;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;EACpC,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnD;EACA,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;EAChC,QAAQ,UAAU,EAAE,CAAC;EACrB,OAAO;EACP,KAAK;AACL;EACA,IAAI,OAAO,KAAK,KAAK,KAAK,IAAI,UAAU,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;EAC9D,GAAG;AACH;EACA,EAAE,OAAO,CAAC,CAAC;EACX,CAAC;EACD;EACA;EACA;AACA;EACA,SAAS,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE;EACnC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;EAC1B,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;EACA,EAAE,KAAK,MAAM,GAAG,IAAI,CAAC,EAAE;EACvB,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;EAC3B,MAAM,OAAO,KAAK,CAAC;EACnB,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;EACD,SAAS,YAAY,CAAC,KAAK,EAAE;EAC7B,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;EAC5E,CAAC;AACD;EACA,SAAS,aAAa,CAAC,CAAC,EAAE;EAC1B,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE;EAC9B,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;AACA;EACA,EAAE,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC;AAC7B;EACA,EAAE,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;EACnC,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;AACA;EACA,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B;EACA,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;EACjC,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;AACA;EACA,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE;EAC7C,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;AACA;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA,SAAS,kBAAkB,CAAC,CAAC,EAAE;EAC/B,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC;EACjE,CAAC;AACD;EACA,SAAS,UAAU,CAAC,KAAK,EAAE;EAC3B,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EAC9B,CAAC;EACD,SAAS,OAAO,CAAC,KAAK,EAAE;EACxB,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC;EAChC,CAAC;EACD,SAAS,KAAK,CAAC,OAAO,EAAE;EACxB,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI;EAChC,IAAI,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;EACjC,GAAG,CAAC,CAAC;EACL,CAAC;EACD;EACA;EACA;EACA;AACA;EACA,SAAS,iBAAiB,CAAC,QAAQ,EAAE;EACrC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC1B,CAAC;EACD,SAAS,kBAAkB,GAAG;EAC9B,EAAE,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;EAC7C,IAAI,OAAO,IAAI,eAAe,EAAE,CAAC;EACjC,GAAG;AACH;EACA,EAAE,OAAO;EACT,CAAC;EACD,SAAS,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE;EAC9C;EACA,EAAE,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,IAAI,OAAO,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE;EAC1E,IAAI,OAAO,QAAQ,CAAC;EACpB,GAAG,MAAM,IAAI,OAAO,OAAO,CAAC,iBAAiB,KAAK,UAAU,EAAE;EAC9D,IAAI,OAAO,OAAO,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;EACrD,GAAG,MAAM,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,EAAE;EAClD;EACA,IAAI,OAAO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;EAC5C,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd;;ECzTA,MAAM,YAAY,SAAS,YAAY,CAAC;EACxC,EAAE,WAAW,GAAG;EAChB,IAAI,KAAK,EAAE,CAAC;AACZ;EACA,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI;EAC5B;EACA;EACA,MAAM,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,gBAAgB,EAAE;EAChD,QAAQ,MAAM,QAAQ,GAAG,MAAM,OAAO,EAAE,CAAC;AACzC;AACA;EACA,QAAQ,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EACrE,QAAQ,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EAC1D,QAAQ,OAAO,MAAM;EACrB;EACA,UAAU,MAAM,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;EACnE,UAAU,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;EACxD,SAAS,CAAC;EACV,OAAO;AACP;EACA,MAAM,OAAO;EACb,KAAK,CAAC;EACN,GAAG;AACH;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;EACvB,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACxC,KAAK;EACL,GAAG;AACH;EACA,EAAE,aAAa,GAAG;EAClB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;EAC9B,MAAM,IAAI,aAAa,CAAC;AACxB;EACA,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACjF,MAAM,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;EAC/B,KAAK;EACL,GAAG;AACH;EACA,EAAE,gBAAgB,CAAC,KAAK,EAAE;EAC1B,IAAI,IAAI,cAAc,CAAC;AACvB;EACA,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACjF,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI;EACpC,MAAM,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;EACxC,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;EACjC,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;EACvB,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,UAAU,CAAC,OAAO,EAAE;EACtB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,KAAK,OAAO,CAAC;AAC7C;EACA,IAAI,IAAI,OAAO,EAAE;EACjB,MAAM,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;EAC7B,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;EACrB,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;EAC5B,MAAM,QAAQ;EACd,KAAK,KAAK;EACV,MAAM,QAAQ,EAAE,CAAC;EACjB,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,SAAS,GAAG;EACd,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;EAC3C,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC;EAC1B,KAAK;AACL;AACA;EACA,IAAI,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;EACzC,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;AACL;EACA,IAAI,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;EAClF,GAAG;AACH;EACA,CAAC;AACI,QAAC,YAAY,GAAG,IAAI,YAAY;;ECpFrC,MAAM,YAAY,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EAC3C,MAAM,aAAa,SAAS,YAAY,CAAC;EACzC,EAAE,WAAW,GAAG;EAChB,IAAI,KAAK,EAAE,CAAC;AACZ;EACA,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI;EAC7B;EACA;EACA,MAAM,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,gBAAgB,EAAE;EAChD,QAAQ,MAAM,QAAQ,GAAG,MAAM,QAAQ,EAAE,CAAC;AAC1C;AACA;EACA,QAAQ,YAAY,CAAC,OAAO,CAAC,KAAK,IAAI;EACtC,UAAU,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EAC1D,SAAS,CAAC,CAAC;EACX,QAAQ,OAAO,MAAM;EACrB;EACA,UAAU,YAAY,CAAC,OAAO,CAAC,KAAK,IAAI;EACxC,YAAY,MAAM,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;EACxD,WAAW,CAAC,CAAC;EACb,SAAS,CAAC;EACV,OAAO;AACP;EACA,MAAM,OAAO;EACb,KAAK,CAAC;EACN,GAAG;AACH;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;EACvB,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACxC,KAAK;EACL,GAAG;AACH;EACA,EAAE,aAAa,GAAG;EAClB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;EAC9B,MAAM,IAAI,aAAa,CAAC;AACxB;EACA,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACjF,MAAM,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;EAC/B,KAAK;EACL,GAAG;AACH;EACA,EAAE,gBAAgB,CAAC,KAAK,EAAE;EAC1B,IAAI,IAAI,cAAc,CAAC;AACvB;EACA,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACjF,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,IAAI;EACnC,MAAM,IAAI,OAAO,MAAM,KAAK,SAAS,EAAE;EACvC,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;EAC/B,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;EACxB,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,SAAS,CAAC,MAAM,EAAE;EACpB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC;AAC3C;EACA,IAAI,IAAI,OAAO,EAAE;EACjB,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EAC3B,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;EACtB,KAAK;EACL,GAAG;AACH;EACA,EAAE,QAAQ,GAAG;EACb,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;EAC5B,MAAM,QAAQ;EACd,KAAK,KAAK;EACV,MAAM,QAAQ,EAAE,CAAC;EACjB,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,QAAQ,GAAG;EACb,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;EAC1C,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC;EACzB,KAAK;AACL;EACA,IAAI,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,OAAO,SAAS,CAAC,MAAM,KAAK,WAAW,EAAE;EACrF,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;AACL;EACA,IAAI,OAAO,SAAS,CAAC,MAAM,CAAC;EAC5B,GAAG;AACH;EACA,CAAC;AACI,QAAC,aAAa,GAAG,IAAI,aAAa;;ECrFvC,SAAS,iBAAiB,CAAC,YAAY,EAAE;EACzC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,YAAY,EAAE,KAAK,CAAC,CAAC;EACnD,CAAC;AACD;EACA,SAAS,QAAQ,CAAC,WAAW,EAAE;EAC/B,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,GAAG,WAAW,GAAG,QAAQ,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC;EACvG,CAAC;EACD,MAAM,cAAc,CAAC;EACrB,EAAE,WAAW,CAAC,OAAO,EAAE;EACvB,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;EAC5D,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;EAC5D,GAAG;AACH;EACA,CAAC;EACD,SAAS,gBAAgB,CAAC,KAAK,EAAE;EACjC,EAAE,OAAO,KAAK,YAAY,cAAc,CAAC;EACzC,CAAC;EACD,SAAS,aAAa,CAAC,MAAM,EAAE;EAC/B,EAAE,IAAI,gBAAgB,GAAG,KAAK,CAAC;EAC/B,EAAE,IAAI,YAAY,GAAG,CAAC,CAAC;EACvB,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC;EACzB,EAAE,IAAI,UAAU,CAAC;EACjB,EAAE,IAAI,cAAc,CAAC;EACrB,EAAE,IAAI,aAAa,CAAC;EACpB,EAAE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,YAAY,EAAE,WAAW,KAAK;EAC7D,IAAI,cAAc,GAAG,YAAY,CAAC;EAClC,IAAI,aAAa,GAAG,WAAW,CAAC;EAChC,GAAG,CAAC,CAAC;AACL;EACA,EAAE,MAAM,MAAM,GAAG,aAAa,IAAI;EAClC,IAAI,IAAI,CAAC,UAAU,EAAE;EACrB,MAAM,MAAM,CAAC,IAAI,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC;EAChD,MAAM,MAAM,CAAC,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;EACrD,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,WAAW,GAAG,MAAM;EAC5B,IAAI,gBAAgB,GAAG,IAAI,CAAC;EAC5B,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,aAAa,GAAG,MAAM;EAC9B,IAAI,gBAAgB,GAAG,KAAK,CAAC;EAC7B,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,MAAM,CAAC,WAAW,KAAK,QAAQ,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;AACtH;EACA,EAAE,MAAM,OAAO,GAAG,KAAK,IAAI;EAC3B,IAAI,IAAI,CAAC,UAAU,EAAE;EACrB,MAAM,UAAU,GAAG,IAAI,CAAC;EACxB,MAAM,MAAM,CAAC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;EAClE,MAAM,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC;EACjD,MAAM,cAAc,CAAC,KAAK,CAAC,CAAC;EAC5B,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,MAAM,GAAG,KAAK,IAAI;EAC1B,IAAI,IAAI,CAAC,UAAU,EAAE;EACrB,MAAM,UAAU,GAAG,IAAI,CAAC;EACxB,MAAM,MAAM,CAAC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EAC9D,MAAM,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC;EACjD,MAAM,aAAa,CAAC,KAAK,CAAC,CAAC;EAC3B,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG,MAAM;EACtB,IAAI,OAAO,IAAI,OAAO,CAAC,eAAe,IAAI;EAC1C,MAAM,UAAU,GAAG,KAAK,IAAI;EAC5B,QAAQ,MAAM,WAAW,GAAG,UAAU,IAAI,CAAC,WAAW,EAAE,CAAC;AACzD;EACA,QAAQ,IAAI,WAAW,EAAE;EACzB,UAAU,eAAe,CAAC,KAAK,CAAC,CAAC;EACjC,SAAS;AACT;EACA,QAAQ,OAAO,WAAW,CAAC;EAC3B,OAAO,CAAC;AACR;EACA,MAAM,MAAM,CAAC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;EACzD,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM;EAClB,MAAM,UAAU,GAAG,SAAS,CAAC;AAC7B;EACA,MAAM,IAAI,CAAC,UAAU,EAAE;EACvB,QAAQ,MAAM,CAAC,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;EACjE,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;AACJ;AACA;EACA,EAAE,MAAM,GAAG,GAAG,MAAM;EACpB;EACA,IAAI,IAAI,UAAU,EAAE;EACpB,MAAM,OAAO;EACb,KAAK;AACL;EACA,IAAI,IAAI,cAAc,CAAC;AACvB;EACA,IAAI,IAAI;EACR,MAAM,cAAc,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;EACnC,KAAK,CAAC,OAAO,KAAK,EAAE;EACpB,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EAC7C,KAAK;AACL;EACA,IAAI,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI;EACjE,MAAM,IAAI,aAAa,EAAE,kBAAkB,CAAC;AAC5C;EACA;EACA,MAAM,IAAI,UAAU,EAAE;EACtB,QAAQ,OAAO;EACf,OAAO;AACP;AACA;EACA,MAAM,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,aAAa,GAAG,CAAC,CAAC;EAC/E,MAAM,MAAM,UAAU,GAAG,CAAC,kBAAkB,GAAG,MAAM,CAAC,UAAU,KAAK,IAAI,GAAG,kBAAkB,GAAG,iBAAiB,CAAC;EACnH,MAAM,MAAM,KAAK,GAAG,OAAO,UAAU,KAAK,UAAU,GAAG,UAAU,CAAC,YAAY,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC;EACpG,MAAM,MAAM,WAAW,GAAG,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,YAAY,GAAG,KAAK,IAAI,OAAO,KAAK,KAAK,UAAU,IAAI,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAC3J;EACA,MAAM,IAAI,gBAAgB,IAAI,CAAC,WAAW,EAAE;EAC5C;EACA,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC;EACtB,QAAQ,OAAO;EACf,OAAO;AACP;EACA,MAAM,YAAY,EAAE,CAAC;AACrB;EACA,MAAM,MAAM,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAC1E;EACA,MAAM,KAAK,CAAC,KAAK,CAAC;EAClB,OAAO,IAAI,CAAC,MAAM;EAClB,QAAQ,IAAI,WAAW,EAAE,EAAE;EAC3B,UAAU,OAAO,KAAK,EAAE,CAAC;EACzB,SAAS;AACT;EACA,QAAQ,OAAO;EACf,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM;EACpB,QAAQ,IAAI,gBAAgB,EAAE;EAC9B,UAAU,MAAM,CAAC,KAAK,CAAC,CAAC;EACxB,SAAS,MAAM;EACf,UAAU,GAAG,EAAE,CAAC;EAChB,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;AACJ;AACA;EACA,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;EACpC,IAAI,GAAG,EAAE,CAAC;EACV,GAAG,MAAM;EACT,IAAI,KAAK,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACtB,GAAG;AACH;EACA,EAAE,OAAO;EACT,IAAI,OAAO;EACX,IAAI,MAAM;EACV,IAAI,QAAQ,EAAE,MAAM;EACpB,MAAM,MAAM,WAAW,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC;EACrE,MAAM,OAAO,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;EACvD,KAAK;EACL,IAAI,WAAW;EACf,IAAI,aAAa;EACjB,GAAG,CAAC;EACJ;;ECnKA,MAAM,aAAa,GAAG,OAAO;;ECE7B,SAAS,mBAAmB,GAAG;EAC/B,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;EACjB,EAAE,IAAI,YAAY,GAAG,CAAC,CAAC;AACvB;EACA,EAAE,IAAI,QAAQ,GAAG,QAAQ,IAAI;EAC7B,IAAI,QAAQ,EAAE,CAAC;EACf,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,aAAa,GAAG,QAAQ,IAAI;EAClC,IAAI,QAAQ,EAAE,CAAC;EACf,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG,QAAQ,IAAI;EAC5B,IAAI,IAAI,MAAM,CAAC;EACf,IAAI,YAAY,EAAE,CAAC;AACnB;EACA,IAAI,IAAI;EACR,MAAM,MAAM,GAAG,QAAQ,EAAE,CAAC;EAC1B,KAAK,SAAS;EACd,MAAM,YAAY,EAAE,CAAC;AACrB;EACA,MAAM,IAAI,CAAC,YAAY,EAAE;EACzB,QAAQ,KAAK,EAAE,CAAC;EAChB,OAAO;EACP,KAAK;AACL;EACA,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,QAAQ,GAAG,QAAQ,IAAI;EAC/B,IAAI,IAAI,YAAY,EAAE;EACtB,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC3B,KAAK,MAAM;EACX,MAAM,iBAAiB,CAAC,MAAM;EAC9B,QAAQ,QAAQ,CAAC,QAAQ,CAAC,CAAC;EAC3B,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG,CAAC;EACJ;EACA;EACA;AACA;AACA;EACA,EAAE,MAAM,UAAU,GAAG,QAAQ,IAAI;EACjC,IAAI,OAAO,CAAC,GAAG,IAAI,KAAK;EACxB,MAAM,QAAQ,CAAC,MAAM;EACrB,QAAQ,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;EAC1B,OAAO,CAAC,CAAC;EACT,KAAK,CAAC;EACN,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG,MAAM;EACtB,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC;EAChC,IAAI,KAAK,GAAG,EAAE,CAAC;AACf;EACA,IAAI,IAAI,aAAa,CAAC,MAAM,EAAE;EAC9B,MAAM,iBAAiB,CAAC,MAAM;EAC9B,QAAQ,aAAa,CAAC,MAAM;EAC5B,UAAU,aAAa,CAAC,OAAO,CAAC,QAAQ,IAAI;EAC5C,YAAY,QAAQ,CAAC,QAAQ,CAAC,CAAC;EAC/B,WAAW,CAAC,CAAC;EACb,SAAS,CAAC,CAAC;EACX,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG,CAAC;EACJ;EACA;EACA;EACA;AACA;AACA;EACA,EAAE,MAAM,iBAAiB,GAAG,EAAE,IAAI;EAClC,IAAI,QAAQ,GAAG,EAAE,CAAC;EAClB,GAAG,CAAC;EACJ;EACA;EACA;EACA;AACA;AACA;EACA,EAAE,MAAM,sBAAsB,GAAG,EAAE,IAAI;EACvC,IAAI,aAAa,GAAG,EAAE,CAAC;EACvB,GAAG,CAAC;AACJ;EACA,EAAE,OAAO;EACT,IAAI,KAAK;EACT,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,IAAI,iBAAiB;EACrB,IAAI,sBAAsB;EAC1B,GAAG,CAAC;EACJ,CAAC;AACD;AACK,QAAC,aAAa,GAAG,mBAAmB;;EC7FzC,MAAM,SAAS,CAAC;EAChB,EAAE,OAAO,GAAG;EACZ,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;EAC1B,GAAG;AACH;EACA,EAAE,UAAU,GAAG;EACf,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;AAC1B;EACA,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;EACxC,MAAM,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,MAAM;EACxC,QAAQ,IAAI,CAAC,cAAc,EAAE,CAAC;EAC9B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;EACzB,KAAK;EACL,GAAG;AACH;EACA,EAAE,eAAe,CAAC,YAAY,EAAE;EAChC;EACA,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,YAAY,IAAI,IAAI,GAAG,YAAY,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EAC9H,GAAG;AACH;EACA,EAAE,cAAc,GAAG;EACnB,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;EACxB,MAAM,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EACnC,MAAM,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;EACjC,KAAK;EACL,GAAG;AACH;EACA;;ECvBA;EACA,MAAM,KAAK,SAAS,SAAS,CAAC;EAC9B,EAAE,WAAW,CAAC,MAAM,EAAE;EACtB,IAAI,KAAK,EAAE,CAAC;EACZ,IAAI,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;EACrC,IAAI,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;EAChD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;EACpC,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;EACxB,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;EAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,aAAa,CAAC;EACjD,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;EACpC,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;EACtC,IAAI,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,IAAIC,iBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACtE,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;EACnC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;EACtB,GAAG;AACH;EACA,EAAE,IAAI,IAAI,GAAG;EACb,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;EAC7B,GAAG;AACH;EACA,EAAE,UAAU,CAAC,OAAO,EAAE;EACtB,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc;EAC3C,MAAM,GAAG,OAAO;EAChB,KAAK,CAAC;EACN,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;EACjD,GAAG;AACH;EACA,EAAE,cAAc,GAAG;EACnB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,MAAM,EAAE;EACrE,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EAC9B,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE;EAC5B,IAAI,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AACrE;EACA,IAAI,IAAI,CAAC,QAAQ,CAAC;EAClB,MAAM,IAAI;EACV,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,aAAa,EAAE,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,SAAS;EACjE,MAAM,MAAM,EAAE,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;EACvD,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,QAAQ,CAAC,KAAK,EAAE,eAAe,EAAE;EACnC,IAAI,IAAI,CAAC,QAAQ,CAAC;EAClB,MAAM,IAAI,EAAE,UAAU;EACtB,MAAM,KAAK;EACX,MAAM,eAAe;EACrB,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,MAAM,CAAC,OAAO,EAAE;EAClB,IAAI,IAAI,aAAa,CAAC;AACtB;EACA,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;EACjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;EACpF,IAAI,OAAO,OAAO,GAAG,OAAO,CAAC,IAAI,CAACD,MAAI,CAAC,CAAC,KAAK,CAACA,MAAI,CAAC,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;EACxE,GAAG;AACH;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;EACpB,IAAI,IAAI,CAAC,MAAM,CAAC;EAChB,MAAM,MAAM,EAAE,IAAI;EAClB,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,KAAK,GAAG;EACV,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;EACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EACrC,GAAG;AACH;EACA,EAAE,QAAQ,GAAG;EACb,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC;EAC/E,GAAG;AACH;EACA,EAAE,UAAU,GAAG;EACf,IAAI,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;EAC5D,GAAG;AACH;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC,OAAO,CAAC,CAAC;EACzI,GAAG;AACH;EACA,EAAE,aAAa,CAAC,SAAS,GAAG,CAAC,EAAE;EAC/B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;EACzH,GAAG;AACH;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,IAAI,cAAc,CAAC;AACvB;EACA,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,wBAAwB,EAAE,CAAC,CAAC;AAC5E;EACA,IAAI,IAAI,QAAQ,EAAE;EAClB,MAAM,QAAQ,CAAC,OAAO,CAAC;EACvB,QAAQ,aAAa,EAAE,KAAK;EAC5B,OAAO,CAAC,CAAC;EACT,KAAK;AACL;AACA;EACA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC;EACjF,GAAG;AACH;EACA,EAAE,QAAQ,GAAG;EACb,IAAI,IAAI,cAAc,CAAC;AACvB;EACA,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,sBAAsB,EAAE,CAAC,CAAC;AAC1E;EACA,IAAI,IAAI,QAAQ,EAAE;EAClB,MAAM,QAAQ,CAAC,OAAO,CAAC;EACvB,QAAQ,aAAa,EAAE,KAAK;EAC5B,OAAO,CAAC,CAAC;EACT,KAAK;AACL;AACA;EACA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC;EACjF,GAAG;AACH;EACA,EAAE,WAAW,CAAC,QAAQ,EAAE;EACxB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;EAC5C,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACpC;EACA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;EAC5B,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;EACxB,QAAQ,IAAI,EAAE,eAAe;EAC7B,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,QAAQ;EAChB,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;AACH;EACA,EAAE,cAAc,CAAC,QAAQ,EAAE;EAC3B,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;EAC3C,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC;AAClE;EACA,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;EAClC;EACA;EACA,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;EAC1B,UAAU,IAAI,IAAI,CAAC,mBAAmB,EAAE;EACxC,YAAY,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;EAChC,cAAc,MAAM,EAAE,IAAI;EAC1B,aAAa,CAAC,CAAC;EACf,WAAW,MAAM;EACjB,YAAY,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;EACvC,WAAW;EACX,SAAS;AACT;EACA,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;EAC1B,OAAO;AACP;EACA,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;EACxB,QAAQ,IAAI,EAAE,iBAAiB;EAC/B,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,QAAQ;EAChB,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;AACH;EACA,EAAE,iBAAiB,GAAG;EACtB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;EACjC,GAAG;AACH;EACA,EAAE,UAAU,GAAG;EACf,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;EACnC,MAAM,IAAI,CAAC,QAAQ,CAAC;EACpB,QAAQ,IAAI,EAAE,YAAY;EAC1B,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;AACH;EACA,EAAE,KAAK,CAAC,OAAO,EAAE,YAAY,EAAE;EAC/B,IAAI,IAAI,qBAAqB,EAAE,qBAAqB,CAAC;AACrD;EACA,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,MAAM,EAAE;EAC3C,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,aAAa,EAAE;EAC1F;EACA,QAAQ,IAAI,CAAC,MAAM,CAAC;EACpB,UAAU,MAAM,EAAE,IAAI;EACtB,SAAS,CAAC,CAAC;EACX,OAAO,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;EAC/B,QAAQ,IAAI,cAAc,CAAC;AAC3B;EACA;EACA,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,aAAa,EAAE,CAAC;AAC1F;EACA,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC;EAC5B,OAAO;EACP,KAAK;AACL;AACA;EACA,IAAI,IAAI,OAAO,EAAE;EACjB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;EAC/B,KAAK;EACL;AACA;AACA;EACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;EAC/B,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACnE;EACA,MAAM,IAAI,QAAQ,EAAE;EACpB,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;EAC1C,OAAO;EACP,KAAK;AACL;EACA,IAA+C;EAC/C,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;EACjD,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qIAAqI,CAAC,CAAC;EACjK,OAAO;EACP,KAAK;AACL;EACA,IAAI,MAAM,eAAe,GAAG,kBAAkB,EAAE,CAAC;AACjD;EACA,IAAI,MAAM,cAAc,GAAG;EAC3B,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;EAC7B,MAAM,SAAS,EAAE,SAAS;EAC1B,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;EACrB,KAAK,CAAC;EACN;EACA;AACA;EACA,IAAI,MAAM,iBAAiB,GAAG,MAAM,IAAI;EACxC,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE;EAC9C,QAAQ,UAAU,EAAE,IAAI;EACxB,QAAQ,GAAG,EAAE,MAAM;EACnB,UAAU,IAAI,eAAe,EAAE;EAC/B,YAAY,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;EAC5C,YAAY,OAAO,eAAe,CAAC,MAAM,CAAC;EAC1C,WAAW;AACX;EACA,UAAU,OAAO,SAAS,CAAC;EAC3B,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK,CAAC;AACN;EACA,IAAI,iBAAiB,CAAC,cAAc,CAAC,CAAC;AACtC;EACA,IAAI,MAAM,OAAO,GAAG,MAAM;EAC1B,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;EACjC,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,gCAAgC,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC;EAC/F,OAAO;AACP;EACA,MAAM,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;EACvC,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;EAClD,KAAK,CAAC;AACN;AACA;EACA,IAAI,MAAM,OAAO,GAAG;EACpB,MAAM,YAAY;EAClB,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;EAC3B,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;EAC7B,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;EACvB,MAAM,OAAO;EACb,KAAK,CAAC;EACN,IAAI,iBAAiB,CAAC,OAAO,CAAC,CAAC;EAC/B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC9G;EACA,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;AAClC;EACA,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,MAAM,CAAC,qBAAqB,GAAG,OAAO,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,EAAE;EACtK,MAAM,IAAI,sBAAsB,CAAC;AACjC;EACA,MAAM,IAAI,CAAC,QAAQ,CAAC;EACpB,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,IAAI,EAAE,CAAC,sBAAsB,GAAG,OAAO,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI;EAC5G,OAAO,CAAC,CAAC;EACT,KAAK;AACL;EACA,IAAI,MAAM,OAAO,GAAG,KAAK,IAAI;EAC7B;EACA,MAAM,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;EACtD,QAAQ,IAAI,CAAC,QAAQ,CAAC;EACtB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,KAAK,EAAE,KAAK;EACtB,SAAS,CAAC,CAAC;EACX,OAAO;AACP;EACA,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;EACpC,QAAQ,IAAI,qBAAqB,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,mBAAmB,CAAC;AACnG;EACA;EACA,QAAQ,CAAC,qBAAqB,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EAClK,QAAQ,CAAC,sBAAsB,GAAG,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACzL;EACA,QAAmD;EACnD,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;EACnC,SAAS;EACT,OAAO;AACP;EACA,MAAM,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;EACtC;EACA,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;EAC1B,OAAO;AACP;EACA,MAAM,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;EACxC,KAAK,CAAC;AACN;AACA;EACA,IAAI,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC;EACjC,MAAM,EAAE,EAAE,OAAO,CAAC,OAAO;EACzB,MAAM,KAAK,EAAE,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC;EAC3F,MAAM,SAAS,EAAE,IAAI,IAAI;EACzB,QAAQ,IAAI,sBAAsB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,mBAAmB,CAAC;AACrG;EACA,QAAQ,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;EACzC,UAAqD;EACrD,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wIAAwI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;EACzL,WAAW;AACX;EACA,UAAU,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,CAAC,CAAC;EACpE,UAAU,OAAO;EACjB,SAAS;AACT;EACA,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC3B;EACA,QAAQ,CAAC,sBAAsB,GAAG,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACvK,QAAQ,CAAC,sBAAsB,GAAG,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACzL;EACA,QAAQ,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;EACxC;EACA,UAAU,IAAI,CAAC,UAAU,EAAE,CAAC;EAC5B,SAAS;AACT;EACA,QAAQ,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;EAC1C,OAAO;EACP,MAAM,OAAO;EACb,MAAM,MAAM,EAAE,CAAC,YAAY,EAAE,KAAK,KAAK;EACvC,QAAQ,IAAI,CAAC,QAAQ,CAAC;EACtB,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,YAAY;EACtB,UAAU,KAAK;EACf,SAAS,CAAC,CAAC;EACX,OAAO;EACP,MAAM,OAAO,EAAE,MAAM;EACrB,QAAQ,IAAI,CAAC,QAAQ,CAAC;EACtB,UAAU,IAAI,EAAE,OAAO;EACvB,SAAS,CAAC,CAAC;EACX,OAAO;EACP,MAAM,UAAU,EAAE,MAAM;EACxB,QAAQ,IAAI,CAAC,QAAQ,CAAC;EACtB,UAAU,IAAI,EAAE,UAAU;EAC1B,SAAS,CAAC,CAAC;EACX,OAAO;EACP,MAAM,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;EAClC,MAAM,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU;EAC5C,MAAM,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,WAAW;EAC9C,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;EACxC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;EACxB,GAAG;AACH;EACA,EAAE,QAAQ,CAAC,MAAM,EAAE;EACnB,IAAI,MAAM,OAAO,GAAG,KAAK,IAAI;EAC7B,MAAM,IAAI,YAAY,EAAE,qBAAqB,CAAC;AAC9C;EACA,MAAM,QAAQ,MAAM,CAAC,IAAI;EACzB,QAAQ,KAAK,QAAQ;EACrB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,iBAAiB,EAAE,MAAM,CAAC,YAAY;EAClD,YAAY,kBAAkB,EAAE,MAAM,CAAC,KAAK;EAC5C,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,OAAO;EACpB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,WAAW,EAAE,QAAQ;EACjC,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,UAAU;EACvB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,WAAW,EAAE,UAAU;EACnC,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,OAAO;EACpB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,iBAAiB,EAAE,CAAC;EAChC,YAAY,kBAAkB,EAAE,IAAI;EACpC,YAAY,SAAS,EAAE,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,KAAK,IAAI,GAAG,YAAY,GAAG,IAAI;EACjF,YAAY,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,UAAU,GAAG,QAAQ;EACnF,YAAY,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI;EACxC,cAAc,KAAK,EAAE,IAAI;EACzB,cAAc,MAAM,EAAE,SAAS;EAC/B,aAAa,CAAC;EACd,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,SAAS;EACtB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;EAC7B,YAAY,eAAe,EAAE,KAAK,CAAC,eAAe,GAAG,CAAC;EACtD,YAAY,aAAa,EAAE,CAAC,qBAAqB,GAAG,MAAM,CAAC,aAAa,KAAK,IAAI,GAAG,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE;EACtH,YAAY,KAAK,EAAE,IAAI;EACvB,YAAY,aAAa,EAAE,KAAK;EAChC,YAAY,MAAM,EAAE,SAAS;EAC7B,YAAY,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI;EAClC,cAAc,WAAW,EAAE,MAAM;EACjC,cAAc,iBAAiB,EAAE,CAAC;EAClC,cAAc,kBAAkB,EAAE,IAAI;EACtC,aAAa,CAAC;EACd,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,OAAO;EACpB,UAAU,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACrC;EACA,UAAU,IAAI,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;EAC3E,YAAY,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW;EACxC,cAAc,WAAW,EAAE,MAAM;EACjC,aAAa,CAAC;EACd,WAAW;AACX;EACA,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,KAAK,EAAE,KAAK;EACxB,YAAY,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,GAAG,CAAC;EACxD,YAAY,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE;EACtC,YAAY,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,GAAG,CAAC;EAC1D,YAAY,kBAAkB,EAAE,KAAK;EACrC,YAAY,WAAW,EAAE,MAAM;EAC/B,YAAY,MAAM,EAAE,OAAO;EAC3B,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,YAAY;EACzB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,aAAa,EAAE,IAAI;EAC/B,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,UAAU;EACvB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,GAAG,MAAM,CAAC,KAAK;EAC3B,WAAW,CAAC;EACZ,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACrC,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI;EACzC,QAAQ,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;EACvC,OAAO,CAAC,CAAC;EACT,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;EACxB,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,MAAM;EACd,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,CAAC;AACD;EACA,SAASC,iBAAe,CAAC,OAAO,EAAE;EAClC,EAAE,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU,GAAG,OAAO,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC;EACvG,EAAE,MAAM,OAAO,GAAG,OAAO,IAAI,KAAK,WAAW,CAAC;EAC9C,EAAE,MAAM,oBAAoB,GAAG,OAAO,GAAG,OAAO,OAAO,CAAC,oBAAoB,KAAK,UAAU,GAAG,OAAO,CAAC,oBAAoB,EAAE,GAAG,OAAO,CAAC,oBAAoB,GAAG,CAAC,CAAC;EAChK,EAAE,OAAO;EACT,IAAI,IAAI;EACR,IAAI,eAAe,EAAE,CAAC;EACtB,IAAI,aAAa,EAAE,OAAO,GAAG,oBAAoB,IAAI,IAAI,GAAG,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;EACjG,IAAI,KAAK,EAAE,IAAI;EACf,IAAI,gBAAgB,EAAE,CAAC;EACvB,IAAI,cAAc,EAAE,CAAC;EACrB,IAAI,iBAAiB,EAAE,CAAC;EACxB,IAAI,kBAAkB,EAAE,IAAI;EAC5B,IAAI,SAAS,EAAE,IAAI;EACnB,IAAI,aAAa,EAAE,KAAK;EACxB,IAAI,MAAM,EAAE,OAAO,GAAG,SAAS,GAAG,SAAS;EAC3C,IAAI,WAAW,EAAE,MAAM;EACvB,GAAG,CAAC;EACJ;;ECldA;EACA,MAAM,UAAU,SAAS,YAAY,CAAC;EACtC,EAAE,WAAW,CAAC,MAAM,EAAE;EACtB,IAAI,KAAK,EAAE,CAAC;EACZ,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;EAC/B,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;EACtB,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;EACzB,GAAG;AACH;EACA,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE;EAChC,IAAI,IAAI,kBAAkB,CAAC;AAC3B;EACA,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;EACtC,IAAI,MAAM,SAAS,GAAG,CAAC,kBAAkB,GAAG,OAAO,CAAC,SAAS,KAAK,IAAI,GAAG,kBAAkB,GAAG,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;EACvI,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACpC;EACA,IAAI,IAAI,CAAC,KAAK,EAAE;EAChB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;EACxB,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE;EAClC,QAAQ,QAAQ;EAChB,QAAQ,SAAS;EACjB,QAAQ,OAAO,EAAE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC;EACpD,QAAQ,KAAK;EACb,QAAQ,cAAc,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;EACzD,OAAO,CAAC,CAAC;EACT,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EACtB,KAAK;AACL;EACA,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;EACA,EAAE,GAAG,CAAC,KAAK,EAAE;EACb,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;EAC3C,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;EAC/C,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC/B,MAAM,IAAI,CAAC,MAAM,CAAC;EAClB,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,KAAK;EACb,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;AACH;EACA,EAAE,MAAM,CAAC,KAAK,EAAE;EAChB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACxD;EACA,IAAI,IAAI,UAAU,EAAE;EACpB,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;EACtB,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC;AAC3D;EACA,MAAM,IAAI,UAAU,KAAK,KAAK,EAAE;EAChC,QAAQ,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;EAChD,OAAO;AACP;EACA,MAAM,IAAI,CAAC,MAAM,CAAC;EAClB,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,KAAK;EACb,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;AACH;EACA,EAAE,KAAK,GAAG;EACV,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI;EACpC,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EAC3B,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,GAAG,CAAC,SAAS,EAAE;EACjB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;EACtC,GAAG;AACH;EACA,EAAE,MAAM,GAAG;EACX,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;EACxB,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;EACnB,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAClD;EACA,IAAI,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,WAAW,EAAE;EAC9C,MAAM,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;EAC3B,KAAK;AACL;EACA,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;EAClE,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE;EACtB,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EAClD,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;EACrH,GAAG;AACH;EACA,EAAE,MAAM,CAAC,KAAK,EAAE;EAChB,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;EAC9B,QAAQ,QAAQ;EAChB,OAAO,KAAK;EACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC;EACxB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI;EACpC,QAAQ,KAAK,CAAC,OAAO,EAAE,CAAC;EACxB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,QAAQ,GAAG;EACb,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI;EACpC,QAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;EACzB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;;EC5HA;EACA,MAAM,QAAQ,SAAS,SAAS,CAAC;EACjC,EAAE,WAAW,CAAC,MAAM,EAAE;EACtB,IAAI,KAAK,EAAE,CAAC;EACZ,IAAI,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;EAChD,IAAI,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;EACxC,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;EAC9C,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,aAAa,CAAC;EACjD,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;EACxB,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,eAAe,EAAE,CAAC;EACnD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;EACpC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;EACtB,GAAG;AACH;EACA,EAAE,UAAU,CAAC,OAAO,EAAE;EACtB,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc;EAC3C,MAAM,GAAG,OAAO;EAChB,KAAK,CAAC;EACN,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;EACjD,GAAG;AACH;EACA,EAAE,IAAI,IAAI,GAAG;EACb,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;EAC7B,GAAG;AACH;EACA,EAAE,QAAQ,CAAC,KAAK,EAAE;EAClB,IAAI,IAAI,CAAC,QAAQ,CAAC;EAClB,MAAM,IAAI,EAAE,UAAU;EACtB,MAAM,KAAK;EACX,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,WAAW,CAAC,QAAQ,EAAE;EACxB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;EAC5C,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACpC;EACA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;EAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;EAChC,QAAQ,IAAI,EAAE,eAAe;EAC7B,QAAQ,QAAQ,EAAE,IAAI;EACtB,QAAQ,QAAQ;EAChB,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;AACH;EACA,EAAE,cAAc,CAAC,QAAQ,EAAE;EAC3B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC;EAChE,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;EACtB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;EAC9B,MAAM,IAAI,EAAE,iBAAiB;EAC7B,MAAM,QAAQ,EAAE,IAAI;EACpB,MAAM,QAAQ;EACd,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,cAAc,GAAG;EACnB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;EAChC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS,EAAE;EAC3C,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;EAC1B,OAAO,MAAM;EACb,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EACxC,OAAO;EACP,KAAK;EACL,GAAG;AACH;EACA,EAAE,QAAQ,GAAG;EACb,IAAI,IAAI,qBAAqB,EAAE,aAAa,CAAC;AAC7C;EACA,IAAI,OAAO,CAAC,qBAAqB,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAI,GAAG,qBAAqB,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;EACjK,GAAG;AACH;EACA,EAAE,MAAM,OAAO,GAAG;EAClB,IAAI,MAAM,eAAe,GAAG,MAAM;EAClC,MAAM,IAAI,mBAAmB,CAAC;AAC9B;EACA,MAAM,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC;EACnC,QAAQ,EAAE,EAAE,MAAM;EAClB,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;EACxC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;EACzD,WAAW;AACX;EACA,UAAU,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;EAC/D,SAAS;EACT,QAAQ,MAAM,EAAE,CAAC,YAAY,EAAE,KAAK,KAAK;EACzC,UAAU,IAAI,CAAC,QAAQ,CAAC;EACxB,YAAY,IAAI,EAAE,QAAQ;EAC1B,YAAY,YAAY;EACxB,YAAY,KAAK;EACjB,WAAW,CAAC,CAAC;EACb,SAAS;EACT,QAAQ,OAAO,EAAE,MAAM;EACvB,UAAU,IAAI,CAAC,QAAQ,CAAC;EACxB,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW,CAAC,CAAC;EACb,SAAS;EACT,QAAQ,UAAU,EAAE,MAAM;EAC1B,UAAU,IAAI,CAAC,QAAQ,CAAC;EACxB,YAAY,IAAI,EAAE,UAAU;EAC5B,WAAW,CAAC,CAAC;EACb,SAAS;EACT,QAAQ,KAAK,EAAE,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,mBAAmB,GAAG,CAAC;EAC3F,QAAQ,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;EAC3C,QAAQ,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;EAC7C,OAAO,CAAC,CAAC;EACT,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;EAClC,KAAK,CAAC;AACN;EACA,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC;AACrD;EACA,IAAI,IAAI;EACR,MAAM,IAAI,sBAAsB,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,cAAc,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,cAAc,CAAC;AACvL;EACA,MAAM,IAAI,CAAC,QAAQ,EAAE;EACrB,QAAQ,IAAI,qBAAqB,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,aAAa,CAAC;AAChG;EACA,QAAQ,IAAI,CAAC,QAAQ,CAAC;EACtB,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;EAC3C,SAAS,CAAC,CAAC;AACX;EACA,QAAQ,OAAO,CAAC,qBAAqB,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;EAC1M,QAAQ,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACrL;EACA,QAAQ,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;EAC5C,UAAU,IAAI,CAAC,QAAQ,CAAC;EACxB,YAAY,IAAI,EAAE,SAAS;EAC3B,YAAY,OAAO;EACnB,YAAY,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;EAC3C,WAAW,CAAC,CAAC;EACb,SAAS;EACT,OAAO;AACP;EACA,MAAM,MAAM,IAAI,GAAG,MAAM,eAAe,EAAE,CAAC;AAC3C;EACA,MAAM,OAAO,CAAC,sBAAsB,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;EACrO,MAAM,OAAO,CAAC,qBAAqB,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAChM;EACA,MAAM,OAAO,CAAC,sBAAsB,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;EAC3O,MAAM,OAAO,CAAC,qBAAqB,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;EACtM,MAAM,IAAI,CAAC,QAAQ,CAAC;EACpB,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,IAAI;EACZ,OAAO,CAAC,CAAC;EACT,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK,CAAC,OAAO,KAAK,EAAE;EACpB,MAAM,IAAI;EACV,QAAQ,IAAI,sBAAsB,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,cAAc,EAAE,sBAAsB,EAAE,uBAAuB,EAAE,sBAAsB,EAAE,cAAc,CAAC;AAC3L;EACA;EACA,QAAQ,OAAO,CAAC,sBAAsB,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AACtO;EACA,QAAQ,IAAI,aAAoB,KAAK,YAAY,EAAE;EACnD,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;EACnC,SAAS;AACT;EACA,QAAQ,OAAO,CAAC,qBAAqB,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACjM;EACA,QAAQ,OAAO,CAAC,sBAAsB,GAAG,CAAC,uBAAuB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,uBAAuB,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;EACrP,QAAQ,OAAO,CAAC,sBAAsB,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;EAChN,QAAQ,MAAM,KAAK,CAAC;EACpB,OAAO,SAAS;EAChB,QAAQ,IAAI,CAAC,QAAQ,CAAC;EACtB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,KAAK,EAAE,KAAK;EACtB,SAAS,CAAC,CAAC;EACX,OAAO;EACP,KAAK;EACL,GAAG;AACH;EACA,EAAE,QAAQ,CAAC,MAAM,EAAE;EACnB,IAAI,MAAM,OAAO,GAAG,KAAK,IAAI;EAC7B,MAAM,QAAQ,MAAM,CAAC,IAAI;EACzB,QAAQ,KAAK,QAAQ;EACrB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,YAAY,EAAE,MAAM,CAAC,YAAY;EAC7C,YAAY,aAAa,EAAE,MAAM,CAAC,KAAK;EACvC,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,OAAO;EACpB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,QAAQ,EAAE,IAAI;EAC1B,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,UAAU;EACvB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,QAAQ,EAAE,KAAK;EAC3B,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,SAAS;EACtB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,OAAO,EAAE,MAAM,CAAC,OAAO;EACnC,YAAY,IAAI,EAAE,SAAS;EAC3B,YAAY,YAAY,EAAE,CAAC;EAC3B,YAAY,aAAa,EAAE,IAAI;EAC/B,YAAY,KAAK,EAAE,IAAI;EACvB,YAAY,QAAQ,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;EACzD,YAAY,MAAM,EAAE,SAAS;EAC7B,YAAY,SAAS,EAAE,MAAM,CAAC,SAAS;EACvC,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,SAAS;EACtB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;EAC7B,YAAY,YAAY,EAAE,CAAC;EAC3B,YAAY,aAAa,EAAE,IAAI;EAC/B,YAAY,KAAK,EAAE,IAAI;EACvB,YAAY,MAAM,EAAE,SAAS;EAC7B,YAAY,QAAQ,EAAE,KAAK;EAC3B,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,OAAO;EACpB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,IAAI,EAAE,SAAS;EAC3B,YAAY,KAAK,EAAE,MAAM,CAAC,KAAK;EAC/B,YAAY,YAAY,EAAE,KAAK,CAAC,YAAY,GAAG,CAAC;EAChD,YAAY,aAAa,EAAE,MAAM,CAAC,KAAK;EACvC,YAAY,QAAQ,EAAE,KAAK;EAC3B,YAAY,MAAM,EAAE,OAAO;EAC3B,WAAW,CAAC;AACZ;EACA,QAAQ,KAAK,UAAU;EACvB,UAAU,OAAO,EAAE,GAAG,KAAK;EAC3B,YAAY,GAAG,MAAM,CAAC,KAAK;EAC3B,WAAW,CAAC;EACZ,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACrC,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI;EACzC,QAAQ,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;EAC1C,OAAO,CAAC,CAAC;EACT,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;EAChC,QAAQ,QAAQ,EAAE,IAAI;EACtB,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,MAAM;EACd,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,CAAC;EACD,SAAS,eAAe,GAAG;EAC3B,EAAE,OAAO;EACT,IAAI,OAAO,EAAE,SAAS;EACtB,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,KAAK,EAAE,IAAI;EACf,IAAI,YAAY,EAAE,CAAC;EACnB,IAAI,aAAa,EAAE,IAAI;EACvB,IAAI,QAAQ,EAAE,KAAK;EACnB,IAAI,MAAM,EAAE,MAAM;EAClB,IAAI,SAAS,EAAE,SAAS;EACxB,GAAG,CAAC;EACJ;;EC5PA;EACA,MAAM,aAAa,SAAS,YAAY,CAAC;EACzC,EAAE,WAAW,CAAC,MAAM,EAAE;EACtB,IAAI,KAAK,EAAE,CAAC;EACZ,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;EAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;EACxB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;EACxB,GAAG;AACH;EACA,EAAE,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE;EAChC,IAAI,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC;EAClC,MAAM,aAAa,EAAE,IAAI;EACzB,MAAM,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE;EAChC,MAAM,UAAU,EAAE,EAAE,IAAI,CAAC,UAAU;EACnC,MAAM,OAAO,EAAE,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC;EACrD,MAAM,KAAK;EACX,MAAM,cAAc,EAAE,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,SAAS;EACvG,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;EACvB,IAAI,OAAO,QAAQ,CAAC;EACpB,GAAG;AACH;EACA,EAAE,GAAG,CAAC,QAAQ,EAAE;EAChB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAClC,IAAI,IAAI,CAAC,MAAM,CAAC;EAChB,MAAM,IAAI,EAAE,OAAO;EACnB,MAAM,QAAQ;EACd,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,MAAM,CAAC,QAAQ,EAAE;EACnB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC;EAChE,IAAI,IAAI,CAAC,MAAM,CAAC;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,QAAQ;EACd,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,KAAK,GAAG;EACV,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI;EACzC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;EAC9B,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,MAAM,GAAG;EACX,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC;EAC1B,GAAG;AACH;EACA,EAAE,IAAI,CAAC,OAAO,EAAE;EAChB,IAAI,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,WAAW,EAAE;EAC9C,MAAM,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;EAC3B,KAAK;AACL;EACA,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;EAC7E,GAAG;AACH;EACA,EAAE,OAAO,CAAC,OAAO,EAAE;EACnB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,IAAI,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;EAC/E,GAAG;AACH;EACA,EAAE,MAAM,CAAC,KAAK,EAAE;EAChB,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;EAC9B,QAAQ,QAAQ;EAChB,OAAO,KAAK;EACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC;EACxB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,qBAAqB,GAAG;EAC1B,IAAI,IAAI,cAAc,CAAC;AACvB;EACA,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,cAAc,GAAG,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,MAAM;EAC/G,MAAM,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;EAC3E,MAAM,OAAO,aAAa,CAAC,KAAK,CAAC,MAAM,eAAe,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAC,KAAK,CAACD,MAAI,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;EAC9J,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM;EAClB,MAAM,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;EAChC,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC;EACzB,GAAG;AACH;EACA;;ECzFA,SAAS,qBAAqB,GAAG;EACjC,EAAE,OAAO;EACT,IAAI,OAAO,EAAE,OAAO,IAAI;EACxB,MAAM,OAAO,CAAC,OAAO,GAAG,MAAM;EAC9B,QAAQ,IAAI,qBAAqB,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,oBAAoB,CAAC;AACrJ;EACA,QAAQ,MAAM,WAAW,GAAG,CAAC,qBAAqB,GAAG,OAAO,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,sBAAsB,GAAG,qBAAqB,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,WAAW,CAAC;EAC1M,QAAQ,MAAM,SAAS,GAAG,CAAC,sBAAsB,GAAG,OAAO,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,sBAAsB,GAAG,sBAAsB,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,SAAS,CAAC;EACxM,QAAQ,MAAM,SAAS,GAAG,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC;EAC3E,QAAQ,MAAM,kBAAkB,GAAG,CAAC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,SAAS,MAAM,SAAS,CAAC;EACpG,QAAQ,MAAM,sBAAsB,GAAG,CAAC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,SAAS,MAAM,UAAU,CAAC;EACzG,QAAQ,MAAM,QAAQ,GAAG,CAAC,CAAC,mBAAmB,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,mBAAmB,CAAC,KAAK,KAAK,EAAE,CAAC;EACzH,QAAQ,MAAM,aAAa,GAAG,CAAC,CAAC,oBAAoB,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,oBAAoB,CAAC,UAAU,KAAK,EAAE,CAAC;EACrI,QAAQ,IAAI,aAAa,GAAG,aAAa,CAAC;EAC1C,QAAQ,IAAI,SAAS,GAAG,KAAK,CAAC;AAC9B;EACA,QAAQ,MAAM,iBAAiB,GAAG,MAAM,IAAI;EAC5C,UAAU,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE;EAClD,YAAY,UAAU,EAAE,IAAI;EAC5B,YAAY,GAAG,EAAE,MAAM;EACvB,cAAc,IAAI,eAAe,CAAC;AAClC;EACA,cAAc,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,MAAM,KAAK,IAAI,IAAI,eAAe,CAAC,OAAO,EAAE;EACzF,gBAAgB,SAAS,GAAG,IAAI,CAAC;EACjC,eAAe,MAAM;EACrB,gBAAgB,IAAI,gBAAgB,CAAC;AACrC;EACA,gBAAgB,CAAC,gBAAgB,GAAG,OAAO,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;EACxH,kBAAkB,SAAS,GAAG,IAAI,CAAC;EACnC,iBAAiB,CAAC,CAAC;EACnB,eAAe;AACf;EACA,cAAc,OAAO,OAAO,CAAC,MAAM,CAAC;EACpC,aAAa;EACb,WAAW,CAAC,CAAC;EACb,SAAS,CAAC;AACV;AACA;EACA,QAAQ,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,KAAK,MAAM,OAAO,CAAC,MAAM,CAAC,gCAAgC,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;AAC9I;EACA,QAAQ,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,KAAK;EAChE,UAAU,aAAa,GAAG,QAAQ,GAAG,CAAC,KAAK,EAAE,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,aAAa,EAAE,KAAK,CAAC,CAAC;EAC3F,UAAU,OAAO,QAAQ,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;EAChE,SAAS,CAAC;AACV;AACA;EACA,QAAQ,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,KAAK;EAC9D,UAAU,IAAI,SAAS,EAAE;EACzB,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;EAC/C,WAAW;AACX;EACA,UAAU,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE;EACvE,YAAY,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EAC1C,WAAW;AACX;EACA,UAAU,MAAM,cAAc,GAAG;EACjC,YAAY,QAAQ,EAAE,OAAO,CAAC,QAAQ;EACtC,YAAY,SAAS,EAAE,KAAK;EAC5B,YAAY,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI;EACtC,WAAW,CAAC;EACZ,UAAU,iBAAiB,CAAC,cAAc,CAAC,CAAC;EAC5C,UAAU,MAAM,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;EACxD,UAAU,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;EACnH,UAAU,OAAO,OAAO,CAAC;EACzB,SAAS,CAAC;AACV;EACA,QAAQ,IAAI,OAAO,CAAC;AACpB;EACA,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;EAC9B,UAAU,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;EAClC,SAAS;EACT,aAAa,IAAI,kBAAkB,EAAE;EACrC,UAAU,MAAM,MAAM,GAAG,OAAO,SAAS,KAAK,WAAW,CAAC;EAC1D,UAAU,MAAM,KAAK,GAAG,MAAM,GAAG,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;EACzF,UAAU,OAAO,GAAG,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;EACvD,SAAS;EACT,aAAa,IAAI,sBAAsB,EAAE;EACzC,UAAU,MAAM,MAAM,GAAG,OAAO,SAAS,KAAK,WAAW,CAAC;EAC1D,UAAU,MAAM,KAAK,GAAG,MAAM,GAAG,SAAS,GAAG,oBAAoB,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;EAC7F,UAAU,OAAO,GAAG,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EAC7D,SAAS;EACT,aAAa;EACb,UAAU,aAAa,GAAG,EAAE,CAAC;EAC7B,UAAU,MAAM,MAAM,GAAG,OAAO,OAAO,CAAC,OAAO,CAAC,gBAAgB,KAAK,WAAW,CAAC;EACjF,UAAU,MAAM,oBAAoB,GAAG,WAAW,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC;AACjH;EACA,UAAU,OAAO,GAAG,oBAAoB,GAAG,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvJ;EACA,UAAU,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACpD,YAAY,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI;EAC5C,cAAc,MAAM,mBAAmB,GAAG,WAAW,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC;AACpH;EACA,cAAc,IAAI,mBAAmB,EAAE;EACvC,gBAAgB,MAAM,KAAK,GAAG,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EACnG,gBAAgB,OAAO,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;EACvD,eAAe;AACf;EACA,cAAc,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1F,aAAa,CAAC,CAAC;EACf,WAAW;EACX,SAAS;AACT;EACA,QAAQ,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,KAAK;EACpD,UAAU,KAAK;EACf,UAAU,UAAU,EAAE,aAAa;EACnC,SAAS,CAAC,CAAC,CAAC;EACZ,QAAQ,OAAO,YAAY,CAAC;EAC5B,OAAO,CAAC;EACR,KAAK;EACL,GAAG,CAAC;EACJ,CAAC;EACD,SAAS,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE;EAC1C,EAAE,OAAO,OAAO,CAAC,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;EAC9G,CAAC;EACD,SAAS,oBAAoB,CAAC,OAAO,EAAE,KAAK,EAAE;EAC9C,EAAE,OAAO,OAAO,CAAC,oBAAoB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;EACvG,CAAC;EACD;EACA;EACA;EACA;AACA;EACA,SAAS,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE;EACrC,EAAE,IAAI,OAAO,CAAC,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;EACxD,IAAI,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EAC3D,IAAI,OAAO,OAAO,aAAa,KAAK,WAAW,IAAI,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,KAAK,CAAC;EACrG,GAAG;AACH;EACA,EAAE,OAAO;EACT,CAAC;EACD;EACA;EACA;EACA;AACA;EACA,SAAS,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE;EACzC,EAAE,IAAI,OAAO,CAAC,oBAAoB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;EAC5D,IAAI,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EACnE,IAAI,OAAO,OAAO,iBAAiB,KAAK,WAAW,IAAI,iBAAiB,KAAK,IAAI,IAAI,iBAAiB,KAAK,KAAK,CAAC;EACjH,GAAG;AACH;EACA,EAAE,OAAO;EACT;;ECrIA;EACA,MAAM,WAAW,CAAC;EAClB,EAAE,WAAW,CAAC,MAAM,GAAG,EAAE,EAAE;EAC3B,IAAI,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,IAAI,UAAU,EAAE,CAAC;EAC5D,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,IAAI,IAAI,aAAa,EAAE,CAAC;EACrE,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,aAAa,CAAC;EACjD,IAAI,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC;EACtD,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;EAC5B,IAAI,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;EAC/B,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB;EACA,IAAI,IAA6C,MAAM,CAAC,MAAM,EAAE;EAChE,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4FAA4F,CAAC,CAAC;EACtH,KAAK;EACL,GAAG;AACH;EACA,EAAE,KAAK,GAAG;EACV,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;EACtB,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,OAAO;EACtC,IAAI,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC,SAAS,CAAC,MAAM;EACzD,MAAM,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE;EACpC,QAAQ,IAAI,CAAC,qBAAqB,EAAE,CAAC;EACrC,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;EAClC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,SAAS,CAAC,MAAM;EAC3D,MAAM,IAAI,aAAa,CAAC,QAAQ,EAAE,EAAE;EACpC,QAAQ,IAAI,CAAC,qBAAqB,EAAE,CAAC;EACrC,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;EACnC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,IAAI,qBAAqB,EAAE,qBAAqB,CAAC;AACrD;EACA,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;EACtB,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,OAAO;EACtC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACxG,IAAI,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;EACtC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACzG,IAAI,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;EACvC,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE;EACzB,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EAClD,IAAI,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC;EACrC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;EACnD,GAAG;AACH;EACA,EAAE,UAAU,CAAC,OAAO,EAAE;EACtB,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,GAAG,OAAO;EAClD,MAAM,QAAQ,EAAE,IAAI;EACpB,KAAK,CAAC,CAAC,MAAM,CAAC;EACd,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE;EAClC,IAAI,IAAI,qBAAqB,CAAC;AAC9B;EACA,IAAI,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC;EACjI,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EACpC,IAAI,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAC3D,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;EACjE,IAAI,OAAO,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;EACrF,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,cAAc,CAAC,iBAAiB,EAAE;EACpC,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC;EAChE,MAAM,QAAQ;EACd,MAAM,KAAK;EACX,KAAK,KAAK;EACV,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;EAC9B,MAAM,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;EAC9B,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE;EAC3C,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EACjD,IAAI,MAAM,QAAQ,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;EAC/D,IAAI,MAAM,IAAI,GAAG,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACrD;EACA,IAAI,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;EACrC,MAAM,OAAO,SAAS,CAAC;EACvB,KAAK;AACL;EACA,IAAI,MAAM,aAAa,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;EACnD,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;EACrE,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,GAAG,OAAO;EACnF,MAAM,MAAM,EAAE,IAAI;EAClB,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,cAAc,CAAC,iBAAiB,EAAE,OAAO,EAAE,OAAO,EAAE;EACtD,IAAI,OAAO,aAAa,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1F,MAAM,QAAQ;EACd,KAAK,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACtE,GAAG;AACH;EACA,EAAE,aAAa,CAAC,QAAQ;EACxB;EACA;EACA;EACA,EAAE,OAAO,EAAE;EACX,IAAI,IAAI,sBAAsB,CAAC;AAC/B;EACA,IAAI,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,KAAK,CAAC;EAC9H,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE;EAC5B,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EAClD,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;EACvC,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI;EACnD,QAAQ,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EACjC,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EACjC,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACjE,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;EACvC,IAAI,MAAM,cAAc,GAAG;EAC3B,MAAM,IAAI,EAAE,QAAQ;EACpB,MAAM,GAAG,OAAO;EAChB,KAAK,CAAC;EACN,IAAI,OAAO,aAAa,CAAC,KAAK,CAAC,MAAM;EACrC,MAAM,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI;EACnD,QAAQ,KAAK,CAAC,KAAK,EAAE,CAAC;EACtB,OAAO,CAAC,CAAC;EACT,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;EAC1D,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EAClC,IAAI,MAAM,CAAC,OAAO,EAAE,aAAa,GAAG,EAAE,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5E;EACA,IAAI,IAAI,OAAO,aAAa,CAAC,MAAM,KAAK,WAAW,EAAE;EACrD,MAAM,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;EAClC,KAAK;AACL;EACA,IAAI,MAAM,QAAQ,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;EAC3H,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAACA,MAAI,CAAC,CAAC,KAAK,CAACA,MAAI,CAAC,CAAC;EACxD,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EACtC,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACjE,IAAI,OAAO,aAAa,CAAC,KAAK,CAAC,MAAM;EACrC,MAAM,IAAI,IAAI,EAAE,oBAAoB,CAAC;AACrC;EACA,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI;EACxD,QAAQ,KAAK,CAAC,UAAU,EAAE,CAAC;EAC3B,OAAO,CAAC,CAAC;AACT;EACA,MAAM,IAAI,OAAO,CAAC,WAAW,KAAK,MAAM,EAAE;EAC1C,QAAQ,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;EACjC,OAAO;AACP;EACA,MAAM,MAAM,cAAc,GAAG,EAAE,GAAG,OAAO;EACzC,QAAQ,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,oBAAoB,GAAG,OAAO,CAAC,WAAW,KAAK,IAAI,GAAG,oBAAoB,GAAG,OAAO,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,QAAQ;EAC3I,OAAO,CAAC;EACR,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;EAC1D,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EACnC,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACjE,IAAI,MAAM,QAAQ,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI;EAClI,MAAM,IAAI,qBAAqB,CAAC;AAChC;EACA,MAAM,OAAO,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,GAAG,OAAO;EAChD,QAAQ,aAAa,EAAE,CAAC,qBAAqB,GAAG,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,aAAa,KAAK,IAAI,GAAG,qBAAqB,GAAG,IAAI;EACxI,QAAQ,IAAI,EAAE;EACd,UAAU,WAAW,EAAE,OAAO,CAAC,WAAW;EAC1C,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC,CAAC;EACR,IAAI,IAAI,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAACA,MAAI,CAAC,CAAC;AACnD;EACA,IAAI,IAAI,EAAE,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,YAAY,CAAC,EAAE;EACpD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAACA,MAAI,CAAC,CAAC;EACpC,KAAK;AACL;EACA,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EAC/B,IAAI,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAC3D,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;AACrE;EACA,IAAI,IAAI,OAAO,gBAAgB,CAAC,KAAK,KAAK,WAAW,EAAE;EACvD,MAAM,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;EACrC,KAAK;AACL;EACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;EAChE,IAAI,OAAO,KAAK,CAAC,aAAa,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;EAC/H,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EAClC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAACA,MAAI,CAAC,CAAC,KAAK,CAACA,MAAI,CAAC,CAAC;EACpE,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EACvC,IAAI,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAC3D,IAAI,aAAa,CAAC,QAAQ,GAAG,qBAAqB,EAAE,CAAC;EACrD,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;EAC1C,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,qBAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;EAC1C,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAACA,MAAI,CAAC,CAAC,KAAK,CAACA,MAAI,CAAC,CAAC;EAC5E,GAAG;AACH;EACA,EAAE,qBAAqB,GAAG;EAC1B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;EACtD,GAAG;AACH;EACA,EAAE,aAAa,GAAG;EAClB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC;EAC3B,GAAG;AACH;EACA,EAAE,gBAAgB,GAAG;EACrB,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC;EAC9B,GAAG;AACH;EACA,EAAE,SAAS,GAAG;EACd,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;EACvB,GAAG;AACH;EACA,EAAE,iBAAiB,GAAG;EACtB,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC;EAC/B,GAAG;AACH;EACA,EAAE,iBAAiB,CAAC,OAAO,EAAE;EAC7B,IAAI,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;EAClC,GAAG;AACH;EACA,EAAE,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE;EACtC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AACrG;EACA,IAAI,IAAI,MAAM,EAAE;EAChB,MAAM,MAAM,CAAC,cAAc,GAAG,OAAO,CAAC;EACtC,KAAK,MAAM;EACX,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;EAC9B,QAAQ,QAAQ;EAChB,QAAQ,cAAc,EAAE,OAAO;EAC/B,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;AACH;EACA,EAAE,gBAAgB,CAAC,QAAQ,EAAE;EAC7B,IAAI,IAAI,CAAC,QAAQ,EAAE;EACnB,MAAM,OAAO,SAAS,CAAC;EACvB,KAAK;AACL;AACA;EACA,IAAI,MAAM,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtG;EACA,IAA+C;EAC/C;EACA,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AACrG;EACA,MAAM,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;EACvC,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uDAAuD,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,gNAAgN,CAAC,CAAC;EACjU,OAAO;EACP,KAAK;AACL;EACA,IAAI,OAAO,qBAAqB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,cAAc,CAAC;EACzF,GAAG;AACH;EACA,EAAE,mBAAmB,CAAC,WAAW,EAAE,OAAO,EAAE;EAC5C,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,YAAY,CAAC,WAAW,CAAC,KAAK,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AAC9G;EACA,IAAI,IAAI,MAAM,EAAE;EAChB,MAAM,MAAM,CAAC,cAAc,GAAG,OAAO,CAAC;EACtC,KAAK,MAAM;EACX,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;EACjC,QAAQ,WAAW;EACnB,QAAQ,cAAc,EAAE,OAAO;EAC/B,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;AACH;EACA,EAAE,mBAAmB,CAAC,WAAW,EAAE;EACnC,IAAI,IAAI,CAAC,WAAW,EAAE;EACtB,MAAM,OAAO,SAAS,CAAC;EACvB,KAAK;AACL;AACA;EACA,IAAI,MAAM,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,eAAe,CAAC,WAAW,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AAC/G;EACA,IAA+C;EAC/C;EACA,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,eAAe,CAAC,WAAW,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AAC9G;EACA,MAAM,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;EACvC,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0DAA0D,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,yNAAyN,CAAC,CAAC;EAChV,OAAO;EACP,KAAK;AACL;EACA,IAAI,OAAO,qBAAqB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,cAAc,CAAC;EACzF,GAAG;AACH;EACA,EAAE,mBAAmB,CAAC,OAAO,EAAE;EAC/B,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,UAAU,EAAE;EAC/C,MAAM,OAAO,OAAO,CAAC;EACrB,KAAK;AACL;EACA,IAAI,MAAM,gBAAgB,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO;EAC7D,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC;EAC3E,MAAM,GAAG,OAAO;EAChB,MAAM,UAAU,EAAE,IAAI;EACtB,KAAK,CAAC;AACN;EACA,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,IAAI,gBAAgB,CAAC,QAAQ,EAAE;EAClE,MAAM,gBAAgB,CAAC,SAAS,GAAG,qBAAqB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;EACtG,KAAK;AACL;AACA;EACA,IAAI,IAAI,OAAO,gBAAgB,CAAC,kBAAkB,KAAK,WAAW,EAAE;EACpE,MAAM,gBAAgB,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,WAAW,KAAK,QAAQ,CAAC;EACtF,KAAK;AACL;EACA,IAAI,IAAI,OAAO,gBAAgB,CAAC,gBAAgB,KAAK,WAAW,EAAE;EAClE,MAAM,gBAAgB,CAAC,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC;EACtE,KAAK;AACL;EACA,IAAI,OAAO,gBAAgB,CAAC;EAC5B,GAAG;AACH;EACA,EAAE,sBAAsB,CAAC,OAAO,EAAE;EAClC,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,UAAU,EAAE;EAC/C,MAAM,OAAO,OAAO,CAAC;EACrB,KAAK;AACL;EACA,IAAI,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS;EAC7C,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC;EACjF,MAAM,GAAG,OAAO;EAChB,MAAM,UAAU,EAAE,IAAI;EACtB,KAAK,CAAC;EACN,GAAG;AACH;EACA,EAAE,KAAK,GAAG;EACV,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;EAC5B,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;EAC/B,GAAG;AACH;EACA;;ECxYA,MAAM,aAAa,SAAS,YAAY,CAAC;EACzC,EAAE,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE;EAC/B,IAAI,KAAK,EAAE,CAAC;EACZ,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACzB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;EAC3B,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;EAClC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;EAC5B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;EACvB,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;EAC7B,GAAG;AACH;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACzC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3C,GAAG;AACH;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;EACnC,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1C;EACA,MAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;EAC/D,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;EAC5B,OAAO;AACP;EACA,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;EAC1B,KAAK;EACL,GAAG;AACH;EACA,EAAE,aAAa,GAAG;EAClB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;EAC9B,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;EACrB,KAAK;EACL,GAAG;AACH;EACA,EAAE,sBAAsB,GAAG;EAC3B,IAAI,OAAO,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;EAC3F,GAAG;AACH;EACA,EAAE,wBAAwB,GAAG;EAC7B,IAAI,OAAO,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;EAC7F,GAAG;AACH;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;EAC/B,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;EAC7B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;EAChC,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;EAC3C,GAAG;AACH;EACA,EAAE,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE;EACrC,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;EACrC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;EACxC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;AAC5D;EACA,IAAI,IAA6C,QAAQ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,WAAW,EAAE;EAC1H,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,wLAAwL,CAAC,CAAC;EAC9N,KAAK;AACL;EACA,IAAI,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;EACzD,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC;EACzC,QAAQ,IAAI,EAAE,wBAAwB;EACtC,QAAQ,KAAK,EAAE,IAAI,CAAC,YAAY;EAChC,QAAQ,QAAQ,EAAE,IAAI;EACtB,OAAO,CAAC,CAAC;EACT,KAAK;AACL;EACA,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,WAAW,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;EAClG,MAAM,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;EAC1D,KAAK;AACL;AACA;EACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;EAChC,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;EACnD,KAAK;AACL;EACA,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;EACvB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AACxC;EACA,IAAI,IAAI,OAAO,IAAI,qBAAqB,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE;EACnG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;EAC1B,KAAK;AACL;AACA;EACA,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AACrC;EACA,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,WAAW,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,WAAW,CAAC,SAAS,CAAC,EAAE;EAC1J,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;EAChC,KAAK;AACL;EACA,IAAI,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9D;EACA,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,WAAW,CAAC,OAAO,IAAI,mBAAmB,KAAK,IAAI,CAAC,sBAAsB,CAAC,EAAE;EAC7J,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;EACtD,KAAK;EACL,GAAG;AACH;EACA,EAAE,mBAAmB,CAAC,OAAO,EAAE;EAC/B,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;EAC1E,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACrD;EACA,IAAI,IAAI,qCAAqC,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE;EACtE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;EAClC,MAAM,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC;EAC/C,MAAM,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;EACxD,KAAK;AACL;EACA,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG;AACH;EACA,EAAE,gBAAgB,GAAG;EACrB,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC;EAC9B,GAAG;AACH;EACA,EAAE,WAAW,CAAC,MAAM,EAAE;EACtB,IAAI,MAAM,aAAa,GAAG,EAAE,CAAC;EAC7B,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI;EACvC,MAAM,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,GAAG,EAAE;EAChD,QAAQ,YAAY,EAAE,KAAK;EAC3B,QAAQ,UAAU,EAAE,IAAI;EACxB,QAAQ,GAAG,EAAE,MAAM;EACnB,UAAU,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACrC,UAAU,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;EAC7B,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,aAAa,CAAC;EACzB,GAAG;AACH;EACA,EAAE,eAAe,GAAG;EACpB,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC;EAC7B,GAAG;AACH;EACA,EAAE,MAAM,GAAG;EACX,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EAC1D,GAAG;AACH;EACA,EAAE,OAAO,CAAC;EACV,IAAI,WAAW;EACf,IAAI,GAAG,OAAO;EACd,GAAG,GAAG,EAAE,EAAE;EACV,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,OAAO;EAClC,MAAM,IAAI,EAAE;EACZ,QAAQ,WAAW;EACnB,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,eAAe,CAAC,OAAO,EAAE;EAC3B,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;EACtE,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;EACnF,IAAI,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;EACtC,IAAI,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC,CAAC;EAChF,GAAG;AACH;EACA,EAAE,KAAK,CAAC,YAAY,EAAE;EACtB,IAAI,IAAI,qBAAqB,CAAC;AAC9B;EACA,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,YAAY;EAC9C,MAAM,aAAa,EAAE,CAAC,qBAAqB,GAAG,YAAY,CAAC,aAAa,KAAK,IAAI,GAAG,qBAAqB,GAAG,IAAI;EAChH,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM;EAClB,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;EAC1B,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC;EAChC,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,YAAY,CAAC,YAAY,EAAE;EAC7B;EACA,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB;EACA,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AACtE;EACA,IAAI,IAAI,EAAE,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,YAAY,CAAC,EAAE;EAC9D,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAACA,MAAI,CAAC,CAAC;EACpC,KAAK;AACL;EACA,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG;AACH;EACA,EAAE,kBAAkB,GAAG;EACvB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B;EACA,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;EAC3F,MAAM,OAAO;EACb,KAAK;AACL;EACA,IAAI,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;EAC1F;AACA;EACA,IAAI,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC;EAC7B,IAAI,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,MAAM;EAC3C,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;EACvC,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;EAC5B,OAAO;EACP,KAAK,EAAE,OAAO,CAAC,CAAC;EAChB,GAAG;AACH;EACA,EAAE,sBAAsB,GAAG;EAC3B,IAAI,IAAI,qBAAqB,CAAC;AAC9B;EACA,IAAI,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,IAAI,GAAG,qBAAqB,GAAG,KAAK,CAAC;EAC1O,GAAG;AACH;EACA,EAAE,qBAAqB,CAAC,YAAY,EAAE;EACtC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;EAChC,IAAI,IAAI,CAAC,sBAAsB,GAAG,YAAY,CAAC;AAC/C;EACA,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,EAAE;EACzI,MAAM,OAAO;EACb,KAAK;AACL;EACA,IAAI,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,MAAM;EAC/C,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,2BAA2B,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE;EAChF,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;EAC5B,OAAO;EACP,KAAK,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;EACpC,GAAG;AACH;EACA,EAAE,YAAY,GAAG;EACjB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;EAC9B,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;EAC9D,GAAG;AACH;EACA,EAAE,iBAAiB,GAAG;EACtB,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;EAC7B,MAAM,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EACxC,MAAM,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;EACtC,KAAK;EACL,GAAG;AACH;EACA,EAAE,oBAAoB,GAAG;EACzB,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;EAChC,MAAM,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;EAC5C,MAAM,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;EACzC,KAAK;EACL,GAAG;AACH;EACA,EAAE,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE;EAC/B,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;EACxC,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;EACrC,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC;EAC1C,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC;EACpD,IAAI,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC;EACxD,IAAI,MAAM,WAAW,GAAG,KAAK,KAAK,SAAS,CAAC;EAC5C,IAAI,MAAM,iBAAiB,GAAG,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC;EACxF,IAAI,MAAM,eAAe,GAAG,WAAW,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC;EACxF,IAAI,MAAM;EACV,MAAM,KAAK;EACX,KAAK,GAAG,KAAK,CAAC;EACd,IAAI,IAAI;EACR,MAAM,aAAa;EACnB,MAAM,KAAK;EACX,MAAM,cAAc;EACpB,MAAM,WAAW;EACjB,MAAM,MAAM;EACZ,KAAK,GAAG,KAAK,CAAC;EACd,IAAI,IAAI,cAAc,GAAG,KAAK,CAAC;EAC/B,IAAI,IAAI,iBAAiB,GAAG,KAAK,CAAC;EAClC,IAAI,IAAI,IAAI,CAAC;AACb;EACA,IAAI,IAAI,OAAO,CAAC,kBAAkB,EAAE;EACpC,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;EAC1C,MAAM,MAAM,YAAY,GAAG,CAAC,OAAO,IAAI,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;EAC1E,MAAM,MAAM,eAAe,GAAG,OAAO,IAAI,qBAAqB,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;AACvG;EACA,MAAM,IAAI,YAAY,IAAI,eAAe,EAAE;EAC3C,QAAQ,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,UAAU,GAAG,QAAQ,CAAC;AAClF;EACA,QAAQ,IAAI,CAAC,aAAa,EAAE;EAC5B,UAAU,MAAM,GAAG,SAAS,CAAC;EAC7B,SAAS;EACT,OAAO;AACP;EACA,MAAM,IAAI,OAAO,CAAC,kBAAkB,KAAK,aAAa,EAAE;EACxD,QAAQ,WAAW,GAAG,MAAM,CAAC;EAC7B,OAAO;EACP,KAAK;AACL;AACA;EACA,IAAI,IAAI,OAAO,CAAC,gBAAgB,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,eAAe,IAAI,IAAI,IAAI,eAAe,CAAC,SAAS,IAAI,MAAM,KAAK,OAAO,EAAE;EACxI,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC;EAClC,MAAM,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC;EACpD,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;EACtC,MAAM,cAAc,GAAG,IAAI,CAAC;EAC5B,KAAK;EACL,SAAS,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE;EAClE;EACA,MAAM,IAAI,UAAU,IAAI,KAAK,CAAC,IAAI,MAAM,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE;EACtI,QAAQ,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC;EACjC,OAAO,MAAM;EACb,QAAQ,IAAI;EACZ,UAAU,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;EACzC,UAAU,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;EAC5C,UAAU,IAAI,GAAG,WAAW,CAAC,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;EAC3F,UAAU,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;EACnC,UAAU,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;EAClC,SAAS,CAAC,OAAO,WAAW,EAAE;EAC9B,UAAqD;EACrD,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;EACvD,WAAW;AACX;EACA,UAAU,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;EACzC,SAAS;EACT,OAAO;EACP,KAAK;EACL,SAAS;EACT,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;EACxB,KAAK;AACL;AACA;EACA,IAAI,IAAI,OAAO,OAAO,CAAC,eAAe,KAAK,WAAW,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,MAAM,KAAK,SAAS,EAAE;EAC/G,MAAM,IAAI,eAAe,CAAC;AAC1B;EACA,MAAM,IAAI,UAAU,IAAI,IAAI,IAAI,UAAU,CAAC,iBAAiB,IAAI,OAAO,CAAC,eAAe,MAAM,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,iBAAiB,CAAC,eAAe,CAAC,EAAE;EACtK,QAAQ,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC;EAC1C,OAAO,MAAM;EACb,QAAQ,eAAe,GAAG,OAAO,OAAO,CAAC,eAAe,KAAK,UAAU,GAAG,OAAO,CAAC,eAAe,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC;AAC9H;EACA,QAAQ,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;EACtE,UAAU,IAAI;EACd,YAAY,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;EAC9D,YAAY,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;EACpC,WAAW,CAAC,OAAO,WAAW,EAAE;EAChC,YAAuD;EACvD,cAAc,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;EACzD,aAAa;AACb;EACA,YAAY,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,OAAO;AACP;EACA,MAAM,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;EAClD,QAAQ,MAAM,GAAG,SAAS,CAAC;EAC3B,QAAQ,IAAI,GAAG,WAAW,CAAC,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;EACpG,QAAQ,iBAAiB,GAAG,IAAI,CAAC;EACjC,OAAO;EACP,KAAK;AACL;EACA,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;EAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;EAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC;EAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;EAClC,MAAM,MAAM,GAAG,OAAO,CAAC;EACvB,KAAK;AACL;EACA,IAAI,MAAM,UAAU,GAAG,WAAW,KAAK,UAAU,CAAC;EAClD,IAAI,MAAM,SAAS,GAAG,MAAM,KAAK,SAAS,CAAC;EAC3C,IAAI,MAAM,OAAO,GAAG,MAAM,KAAK,OAAO,CAAC;EACvC,IAAI,MAAM,MAAM,GAAG;EACnB,MAAM,MAAM;EACZ,MAAM,WAAW;EACjB,MAAM,SAAS;EACf,MAAM,SAAS,EAAE,MAAM,KAAK,SAAS;EACrC,MAAM,OAAO;EACb,MAAM,gBAAgB,EAAE,SAAS,IAAI,UAAU;EAC/C,MAAM,IAAI;EACV,MAAM,aAAa;EACnB,MAAM,KAAK;EACX,MAAM,cAAc;EACpB,MAAM,YAAY,EAAE,KAAK,CAAC,iBAAiB;EAC3C,MAAM,aAAa,EAAE,KAAK,CAAC,kBAAkB;EAC7C,MAAM,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;EAC9C,MAAM,SAAS,EAAE,KAAK,CAAC,eAAe,GAAG,CAAC,IAAI,KAAK,CAAC,gBAAgB,GAAG,CAAC;EACxE,MAAM,mBAAmB,EAAE,KAAK,CAAC,eAAe,GAAG,iBAAiB,CAAC,eAAe,IAAI,KAAK,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,gBAAgB;EACnJ,MAAM,UAAU;EAChB,MAAM,YAAY,EAAE,UAAU,IAAI,CAAC,SAAS;EAC5C,MAAM,cAAc,EAAE,OAAO,IAAI,KAAK,CAAC,aAAa,KAAK,CAAC;EAC1D,MAAM,QAAQ,EAAE,WAAW,KAAK,QAAQ;EACxC,MAAM,iBAAiB;EACvB,MAAM,cAAc;EACpB,MAAM,cAAc,EAAE,OAAO,IAAI,KAAK,CAAC,aAAa,KAAK,CAAC;EAC1D,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC;EACtC,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;EAC3B,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM;EACzB,KAAK,CAAC;EACN,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG;AACH;EACA,EAAE,YAAY,CAAC,aAAa,EAAE;EAC9B,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC;EAC1C,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;EAC1E,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;EACtD,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC;AAC7C;EACA,IAAI,IAAI,mBAAmB,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE;EACrD,MAAM,OAAO;EACb,KAAK;AACL;EACA,IAAI,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC;AACpC;EACA,IAAI,MAAM,oBAAoB,GAAG;EACjC,MAAM,KAAK,EAAE,IAAI;EACjB,KAAK,CAAC;AACN;EACA,IAAI,MAAM,qBAAqB,GAAG,MAAM;EACxC,MAAM,IAAI,CAAC,UAAU,EAAE;EACvB,QAAQ,OAAO,IAAI,CAAC;EACpB,OAAO;AACP;EACA,MAAM,MAAM;EACZ,QAAQ,mBAAmB;EAC3B,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;EACvB,MAAM,MAAM,wBAAwB,GAAG,OAAO,mBAAmB,KAAK,UAAU,GAAG,mBAAmB,EAAE,GAAG,mBAAmB,CAAC;AAC/H;EACA,MAAM,IAAI,wBAAwB,KAAK,KAAK,IAAI,CAAC,wBAAwB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;EACtG,QAAQ,OAAO,IAAI,CAAC;EACpB,OAAO;AACP;EACA,MAAM,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,wBAAwB,IAAI,IAAI,GAAG,wBAAwB,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;AACrH;EACA,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;EACzC,QAAQ,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;EACnC,OAAO;AACP;EACA,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI;EACzD,QAAQ,MAAM,QAAQ,GAAG,GAAG,CAAC;EAC7B,QAAQ,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,UAAU,CAAC,QAAQ,CAAC,CAAC;EAC9E,QAAQ,OAAO,OAAO,IAAI,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;EACtD,OAAO,CAAC,CAAC;EACT,KAAK,CAAC;AACN;EACA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,SAAS,MAAM,KAAK,IAAI,qBAAqB,EAAE,EAAE;EACzG,MAAM,oBAAoB,CAAC,SAAS,GAAG,IAAI,CAAC;EAC5C,KAAK;AACL;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,oBAAoB;EACzC,MAAM,GAAG,aAAa;EACtB,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/E;EACA,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,EAAE;EACrC,MAAM,OAAO;EACb,KAAK;AACL;EACA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;EACxC,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;EAC9B,IAAI,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC,KAAK,CAAC;EAChD,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC;AAClD;EACA,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;EAC7B,MAAM,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;EAClE,MAAM,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;EAC9B,KAAK;EACL,GAAG;AACH;EACA,EAAE,aAAa,CAAC,MAAM,EAAE;EACxB,IAAI,MAAM,aAAa,GAAG,EAAE,CAAC;AAC7B;EACA,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE;EACnC,MAAM,aAAa,CAAC,SAAS,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;EAC/C,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;EAC3E,MAAM,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC;EACnC,KAAK;AACL;EACA,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AACrC;EACA,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;EAC7B,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;EAC1B,KAAK;EACL,GAAG;AACH;EACA,EAAE,MAAM,CAAC,aAAa,EAAE;EACxB,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B;EACA,MAAM,IAAI,aAAa,CAAC,SAAS,EAAE;EACnC,QAAQ,IAAI,qBAAqB,EAAE,aAAa,EAAE,qBAAqB,EAAE,cAAc,CAAC;AACxF;EACA,QAAQ,CAAC,qBAAqB,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;EACjK,QAAQ,CAAC,qBAAqB,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EACzK,OAAO,MAAM,IAAI,aAAa,CAAC,OAAO,EAAE;EACxC,QAAQ,IAAI,qBAAqB,EAAE,cAAc,EAAE,sBAAsB,EAAE,cAAc,CAAC;AAC1F;EACA,QAAQ,CAAC,qBAAqB,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAClK,QAAQ,CAAC,sBAAsB,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACjL,OAAO;AACP;AACA;EACA,MAAM,IAAI,aAAa,CAAC,SAAS,EAAE;EACnC,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;EAChC,UAAU,QAAQ;EAClB,SAAS,KAAK;EACd,UAAU,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EACvC,SAAS,CAAC,CAAC;EACX,OAAO;AACP;AACA;EACA,MAAM,IAAI,aAAa,CAAC,KAAK,EAAE;EAC/B,QAAQ,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC;EAC3C,UAAU,KAAK,EAAE,IAAI,CAAC,YAAY;EAClC,UAAU,IAAI,EAAE,wBAAwB;EACxC,SAAS,CAAC,CAAC;EACX,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,CAAC;AACD;EACA,SAAS,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE;EAC3C,EAAE,OAAO,OAAO,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,OAAO,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,CAAC;EACxI,CAAC;AACD;EACA,SAAS,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE;EAC5C,EAAE,OAAO,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,IAAI,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;EACrI,CAAC;AACD;EACA,SAAS,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE;EAC9C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;EACjC,IAAI,MAAM,KAAK,GAAG,OAAO,KAAK,KAAK,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EACrE,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;EAC5E,GAAG;AACH;EACA,EAAE,OAAO,KAAK,CAAC;EACf,CAAC;AACD;EACA,SAAS,qBAAqB,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE;EACvE,EAAE,OAAO,OAAO,CAAC,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,SAAS,IAAI,WAAW,CAAC,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;EACjL,CAAC;AACD;EACA,SAAS,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE;EACjC,EAAE,OAAO,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;EAChD,CAAC;EACD;AACA;AACA;EACA,SAAS,qCAAqC,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE;EACpF;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,OAAO,CAAC,gBAAgB,EAAE;EAChC,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;EACH;AACA;AACA;EACA,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;EAC7C;EACA;EACA;EACA,IAAI,OAAO,gBAAgB,CAAC,iBAAiB,CAAC;EAC9C,GAAG;EACH;AACA;AACA;EACA,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,EAAE,gBAAgB,CAAC,EAAE;EAC3E,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;AACA;EACA,EAAE,OAAO,KAAK,CAAC;EACf;;EC3jBA,MAAM,eAAe,SAAS,YAAY,CAAC;EAC3C,EAAE,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE;EAC/B,IAAI,KAAK,EAAE,CAAC;EACZ,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACzB,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;EACtB,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;EACrB,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;EACxB,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC3B;EACA,IAAI,IAAI,OAAO,EAAE;EACjB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;EAC/B,KAAK;EACL,GAAG;AACH;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;EACnC,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI;EACzC,QAAQ,QAAQ,CAAC,SAAS,CAAC,MAAM,IAAI;EACrC,UAAU,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;EAC1C,SAAS,CAAC,CAAC;EACX,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;AACH;EACA,EAAE,aAAa,GAAG;EAClB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;EAC9B,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;EACrB,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;EAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI;EACvC,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;EACzB,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE;EACrC,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;EAC3B,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;EAC3C,MAAM,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC1E;EACA,MAAM,kBAAkB,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAAC,CAAC;EACjH,MAAM,MAAM,YAAY,GAAG,kBAAkB,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;EAC3E,MAAM,MAAM,eAAe,GAAG,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACvH,MAAM,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC,CAAC;EAClF,MAAM,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,KAAK,QAAQ,KAAK,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AACvG;EACA,MAAM,IAAI,aAAa,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE;EAC3E,QAAQ,OAAO;EACf,OAAO;AACP;EACA,MAAM,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC;EACpC,MAAM,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC;EAC1C,MAAM,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;AAC9B;EACA,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;EAChC,QAAQ,OAAO;EACf,OAAO;AACP;EACA,MAAM,UAAU,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAI;EAClE,QAAQ,QAAQ,CAAC,OAAO,EAAE,CAAC;EAC3B,OAAO,CAAC,CAAC;EACT,MAAM,UAAU,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC,OAAO,CAAC,QAAQ,IAAI;EAClE,QAAQ,QAAQ,CAAC,SAAS,CAAC,MAAM,IAAI;EACrC,UAAU,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;EAC1C,SAAS,CAAC,CAAC;EACX,OAAO,CAAC,CAAC;EACT,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;EACpB,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,gBAAgB,GAAG;EACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;EACvB,GAAG;AACH;EACA,EAAE,UAAU,GAAG;EACf,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC;EACtE,GAAG;AACH;EACA,EAAE,YAAY,GAAG;EACjB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC;EAC1B,GAAG;AACH;EACA,EAAE,mBAAmB,CAAC,OAAO,EAAE;EAC/B,IAAI,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;EAC7H,GAAG;AACH;EACA,EAAE,qBAAqB,CAAC,OAAO,EAAE;EACjC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;EACzC,IAAI,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5G,IAAI,MAAM,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;EACnG,IAAI,MAAM,iBAAiB,GAAG,qBAAqB,CAAC,OAAO,CAAC,gBAAgB,IAAI;EAChF,MAAM,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;AACrE;EACA,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE;EACzB,QAAQ,OAAO,CAAC;EAChB,UAAU,qBAAqB,EAAE,gBAAgB;EACjD,UAAU,QAAQ,EAAE,KAAK;EACzB,SAAS,CAAC,CAAC;EACX,OAAO;AACP;EACA,MAAM,OAAO,EAAE,CAAC;EAChB,KAAK,CAAC,CAAC;EACP,IAAI,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC;EAC9G,IAAI,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;EACnI,IAAI,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;EACzF,IAAI,MAAM,kBAAkB,GAAG,aAAa,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;AAC7G;EACA,IAAI,MAAM,WAAW,GAAG,OAAO,IAAI;EACnC,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;EACxE,MAAM,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;EAC5E,MAAM,OAAO,eAAe,IAAI,IAAI,GAAG,eAAe,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;EAC1G,KAAK,CAAC;AACN;EACA,IAAI,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK;EAC1E,MAAM,IAAI,OAAO,CAAC,gBAAgB,EAAE;EACpC;EACA,QAAQ,MAAM,sBAAsB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACjE;EACA,QAAQ,IAAI,sBAAsB,KAAK,SAAS,EAAE;EAClD,UAAU,OAAO;EACjB,YAAY,qBAAqB,EAAE,OAAO;EAC1C,YAAY,QAAQ,EAAE,sBAAsB;EAC5C,WAAW,CAAC;EACZ,SAAS;EACT,OAAO;AACP;EACA,MAAM,OAAO;EACb,QAAQ,qBAAqB,EAAE,OAAO;EACtC,QAAQ,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC;EACtC,OAAO,CAAC;EACR,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,2BAA2B,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB,CAAC,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC;AAClK;EACA,IAAI,OAAO,iBAAiB,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;EAC5F,GAAG;AACH;EACA,EAAE,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE;EAC7B,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnD;EACA,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;EACtB,MAAM,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;EAC1D,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;EACpB,KAAK;EACL,GAAG;AACH;EACA,EAAE,MAAM,GAAG;EACX,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;EAC9B,QAAQ,QAAQ;EAChB,OAAO,KAAK;EACZ,QAAQ,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EAC9B,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;;ECjKA,MAAM,qBAAqB,SAAS,aAAa,CAAC;EAClD;EACA;EACA;EACA;EACA,EAAE,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE;EAC/B,IAAI,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;EAC3B,GAAG;AACH;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;EACxB,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACvD,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/D,GAAG;AACH;EACA,EAAE,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE;EACrC,IAAI,KAAK,CAAC,UAAU,CAAC,EAAE,GAAG,OAAO;EACjC,MAAM,QAAQ,EAAE,qBAAqB,EAAE;EACvC,KAAK,EAAE,aAAa,CAAC,CAAC;EACtB,GAAG;AACH;EACA,EAAE,mBAAmB,CAAC,OAAO,EAAE;EAC/B,IAAI,OAAO,CAAC,QAAQ,GAAG,qBAAqB,EAAE,CAAC;EAC/C,IAAI,OAAO,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;EAC9C,GAAG;AACH;EACA,EAAE,aAAa,CAAC;EAChB,IAAI,SAAS;EACb,IAAI,GAAG,OAAO;EACd,GAAG,GAAG,EAAE,EAAE;EACV,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,OAAO;EAClC,MAAM,IAAI,EAAE;EACZ,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,SAAS;EAC9B,UAAU,SAAS;EACnB,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,iBAAiB,CAAC;EACpB,IAAI,SAAS;EACb,IAAI,GAAG,OAAO;EACd,GAAG,GAAG,EAAE,EAAE;EACV,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,OAAO;EAClC,MAAM,IAAI,EAAE;EACZ,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,UAAU;EAC/B,UAAU,SAAS;EACnB,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE;EAC/B,IAAI,IAAI,gBAAgB,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,WAAW,EAAE,YAAY,CAAC;AACrH;EACA,IAAI,MAAM;EACV,MAAM,KAAK;EACX,KAAK,GAAG,KAAK,CAAC;EACd,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;EACtD,IAAI,MAAM;EACV,MAAM,UAAU;EAChB,MAAM,YAAY;EAClB,KAAK,GAAG,MAAM,CAAC;EACf,IAAI,MAAM,kBAAkB,GAAG,UAAU,IAAI,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,qBAAqB,GAAG,gBAAgB,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,SAAS,MAAM,SAAS,CAAC;EAC7N,IAAI,MAAM,sBAAsB,GAAG,UAAU,IAAI,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,qBAAqB,GAAG,iBAAiB,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,SAAS,MAAM,UAAU,CAAC;EACpO,IAAI,OAAO,EAAE,GAAG,MAAM;EACtB,MAAM,aAAa,EAAE,IAAI,CAAC,aAAa;EACvC,MAAM,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;EAC/C,MAAM,WAAW,EAAE,WAAW,CAAC,OAAO,EAAE,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;EACxG,MAAM,eAAe,EAAE,eAAe,CAAC,OAAO,EAAE,CAAC,YAAY,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC;EAClH,MAAM,kBAAkB;EACxB,MAAM,sBAAsB;EAC5B,MAAM,YAAY,EAAE,YAAY,IAAI,CAAC,kBAAkB,IAAI,CAAC,sBAAsB;EAClF,KAAK,CAAC;EACN,GAAG;AACH;EACA;;EC5EA;EACA,MAAM,gBAAgB,SAAS,YAAY,CAAC;EAC5C,EAAE,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE;EAC/B,IAAI,KAAK,EAAE,CAAC;EACZ,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACzB,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;EAC7B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;EACvB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;EACxB,GAAG;AACH;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACzC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACvC,GAAG;AACH;EACA,EAAE,UAAU,CAAC,OAAO,EAAE;EACtB,IAAI,IAAI,qBAAqB,CAAC;AAC9B;EACA,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;EACrC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;AAC/D;EACA,IAAI,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;EACzD,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC;EAC5C,QAAQ,IAAI,EAAE,wBAAwB;EACtC,QAAQ,QAAQ,EAAE,IAAI,CAAC,eAAe;EACtC,QAAQ,QAAQ,EAAE,IAAI;EACtB,OAAO,CAAC,CAAC;EACT,KAAK;AACL;EACA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACrH,GAAG;AACH;EACA,EAAE,aAAa,GAAG;EAClB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;EAC9B,MAAM,IAAI,sBAAsB,CAAC;AACjC;EACA,MAAM,CAAC,sBAAsB,GAAG,IAAI,CAAC,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;EACrH,KAAK;EACL,GAAG;AACH;EACA,EAAE,gBAAgB,CAAC,MAAM,EAAE;EAC3B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;AACxB;EACA,IAAI,MAAM,aAAa,GAAG;EAC1B,MAAM,SAAS,EAAE,IAAI;EACrB,KAAK,CAAC;AACN;EACA,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE;EACnC,MAAM,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC;EACrC,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;EACxC,MAAM,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC;EACnC,KAAK;AACL;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;EAC/B,GAAG;AACH;EACA,EAAE,gBAAgB,GAAG;EACrB,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC;EAC9B,GAAG;AACH;EACA,EAAE,KAAK,GAAG;EACV,IAAI,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;EACrC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;EACxB,IAAI,IAAI,CAAC,MAAM,CAAC;EAChB,MAAM,SAAS,EAAE,IAAI;EACrB,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;EAC7B,IAAI,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;AACjC;EACA,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;EAC9B,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;EAChD,KAAK;AACL;EACA,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO;EAC9F,MAAM,SAAS,EAAE,OAAO,SAAS,KAAK,WAAW,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;EACtF,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;EAC3C,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;EAC1C,GAAG;AACH;EACA,EAAE,YAAY,GAAG;EACjB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,eAAe,EAAE,CAAC;EACxF,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC;EACjD,IAAI,MAAM,MAAM,GAAG,EAAE,GAAG,KAAK;EAC7B,MAAM,SAAS;EACf,MAAM,SAAS,EAAE,SAAS;EAC1B,MAAM,SAAS,EAAE,KAAK,CAAC,MAAM,KAAK,SAAS;EAC3C,MAAM,OAAO,EAAE,KAAK,CAAC,MAAM,KAAK,OAAO;EACvC,MAAM,MAAM,EAAE,KAAK,CAAC,MAAM,KAAK,MAAM;EACrC,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM;EACzB,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;EACvB,KAAK,CAAC;EACN,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;EAChC,GAAG;AACH;EACA,EAAE,MAAM,CAAC,OAAO,EAAE;EAClB,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM;EAC9B;EACA,MAAM,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;EACrD,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE;EAC/B,UAAU,IAAI,qBAAqB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,oBAAoB,CAAC;AACvG;EACA,UAAU,CAAC,qBAAqB,GAAG,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;EAC/O,UAAU,CAAC,sBAAsB,GAAG,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;EACzP,SAAS,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE;EACpC,UAAU,IAAI,sBAAsB,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,oBAAoB,CAAC;AACzG;EACA,UAAU,CAAC,sBAAsB,GAAG,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa,EAAE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;EAClP,UAAU,CAAC,sBAAsB,GAAG,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa,EAAE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,IAAI,CAAC,oBAAoB,EAAE,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;EAC/P,SAAS;EACT,OAAO;AACP;AACA;EACA,MAAM,IAAI,OAAO,CAAC,SAAS,EAAE;EAC7B,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;EAChC,UAAU,QAAQ;EAClB,SAAS,KAAK;EACd,UAAU,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EACvC,SAAS,CAAC,CAAC;EACX,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;;EClIA;EACA;EACA,SAAS,iBAAiB,CAAC,QAAQ,EAAE;EACrC,EAAE,OAAO;EACT,IAAI,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,WAAW;EAC7C,IAAI,KAAK,EAAE,QAAQ,CAAC,KAAK;EACzB,GAAG,CAAC;EACJ,CAAC;EACD;EACA;EACA;AACA;AACA;EACA,SAAS,cAAc,CAAC,KAAK,EAAE;EAC/B,EAAE,OAAO;EACT,IAAI,KAAK,EAAE,KAAK,CAAC,KAAK;EACtB,IAAI,QAAQ,EAAE,KAAK,CAAC,QAAQ;EAC5B,IAAI,SAAS,EAAE,KAAK,CAAC,SAAS;EAC9B,GAAG,CAAC;EACJ,CAAC;AACD;EACA,SAAS,8BAA8B,CAAC,QAAQ,EAAE;EAClD,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;EACjC,CAAC;EACD,SAAS,2BAA2B,CAAC,KAAK,EAAE;EAC5C,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC;EAC1C,CAAC;EACD,SAAS,SAAS,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE;EACzC,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC;EACvB,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC;AACrB;EACA,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,EAAE;EAC5C,IAAI,MAAM,uBAAuB,GAAG,OAAO,CAAC,uBAAuB,IAAI,8BAA8B,CAAC;EACtG,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,QAAQ,IAAI;EAC3D,MAAM,IAAI,uBAAuB,CAAC,QAAQ,CAAC,EAAE;EAC7C,QAAQ,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,EAAE;EAC1C,IAAI,MAAM,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,2BAA2B,CAAC;EAC7F,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI;EACrD,MAAM,IAAI,oBAAoB,CAAC,KAAK,CAAC,EAAE;EACvC,QAAQ,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EAC5C,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,OAAO;EACT,IAAI,SAAS;EACb,IAAI,OAAO;EACX,GAAG,CAAC;EACJ,CAAC;EACD,SAAS,OAAO,CAAC,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE;EACnD,EAAE,IAAI,OAAO,eAAe,KAAK,QAAQ,IAAI,eAAe,KAAK,IAAI,EAAE;EACvE,IAAI,OAAO;EACX,GAAG;AACH;EACA,EAAE,MAAM,aAAa,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;EAClD,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;AAC5C;EACA,EAAE,MAAM,SAAS,GAAG,eAAe,CAAC,SAAS,IAAI,EAAE,CAAC;AACpD;EACA,EAAE,MAAM,OAAO,GAAG,eAAe,CAAC,OAAO,IAAI,EAAE,CAAC;EAChD,EAAE,SAAS,CAAC,OAAO,CAAC,kBAAkB,IAAI;EAC1C,IAAI,IAAI,qBAAqB,CAAC;AAC9B;EACA,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,IAAI,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,qBAAqB,GAAG,OAAO,CAAC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,qBAAqB,CAAC,SAAS,CAAC;EACrK,MAAM,WAAW,EAAE,kBAAkB,CAAC,WAAW;EACjD,KAAK,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC;EACjC,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;EACnB,IAAI,QAAQ;EACZ,IAAI,KAAK;EACT,IAAI,SAAS;EACb,GAAG,KAAK;EACR,IAAI,IAAI,sBAAsB,CAAC;AAC/B;EACA,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC5C;EACA,IAAI,IAAI,KAAK,EAAE;EACf,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,EAAE;EAC3D;EACA;EACA,QAAQ,MAAM;EACd,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,GAAG,oBAAoB;EACjC,SAAS,GAAG,KAAK,CAAC;EAClB,QAAQ,KAAK,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;EAC7C,OAAO;AACP;EACA,MAAM,OAAO;EACb,KAAK;AACL;AACA;EACA,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,IAAI,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,sBAAsB,GAAG,OAAO,CAAC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC,OAAO,CAAC;EAClK,MAAM,QAAQ;EACd,MAAM,SAAS;EACf,KAAK;EACL;EACA,IAAI,EAAE,GAAG,KAAK;EACd,MAAM,WAAW,EAAE,MAAM;EACzB,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC;EACL;;ECtGO,MAAME,uBAAuB,GAAGC,mBAAQ,CAACD,uBAAzC;;ECAPE,aAAa,CAACC,sBAAd,CAAqCH,uBAArC,CAAA;;;;;;;;;;;;;;;;;;;;;ACQA;EACA,CAA2C;EAC3C,GAAE,CAAC,WAAW;AAGd;EACA;EACA,CAAA;KACE,OAAO,8BAA8B,KAAK,WAAW;KACrD,OAAO,8BAA8B,CAAC,2BAA2B;EACnE,KAAI,UAAU;KACZ;KACA,8BAA8B,CAAC,2BAA2B,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;IACzE;EACD,WAAU,IAAI,KAAK,GAAGI,yBAAgB,CAAC;AACvC;EACA,CAAA,IAAI,oBAAoB,GAAG,KAAK,CAAC,kDAAkD,CAAC;AACpF;GACA,SAAS,KAAK,CAAC,MAAM,EAAE;KACrB;OACE;EACJ,OAAM,KAAK,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;WACjH,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;UACpC;AACP;SACM,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QACrC;MACF;IACF;AACD;EACA,CAAA,SAAS,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE;EAC3C;EACA;KACE;EACF,KAAI,IAAI,sBAAsB,GAAG,oBAAoB,CAAC,sBAAsB,CAAC;EAC7E,KAAI,IAAI,KAAK,GAAG,sBAAsB,CAAC,gBAAgB,EAAE,CAAC;AAC1D;EACA,KAAI,IAAI,KAAK,KAAK,EAAE,EAAE;SAChB,MAAM,IAAI,IAAI,CAAC;SACf,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7B;AACL;AACA;OACI,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;EAClD,OAAM,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;EAC1B,MAAK,CAAC,CAAC;AACP;OACI,cAAc,CAAC,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC;EACjD;EACA;AACA;EACA,KAAI,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;MACxE;IACF;AACD;EACA;EACA;EACA;EACA;EACA,CAAA,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;KAChB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;MACnE;IACF;AACD;EACA,CAAA,IAAI,QAAQ,GAAG,OAAO,MAAM,CAAC,EAAE,KAAK,UAAU,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;AAChE;EACA;AACA;EACA,CAAA,IAAI,QAAQ,GAAG,KAAK,CAAC,QAAQ;EAC7B,KAAI,SAAS,GAAG,KAAK,CAAC,SAAS;EAC/B,KAAI,eAAe,GAAG,KAAK,CAAC,eAAe;EAC3C,KAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;GACxC,IAAI,iBAAiB,GAAG,KAAK,CAAC;GAC9B,IAAI,0BAA0B,GAAG,KAAK,CAAC;EACvC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA,CAAA,SAAS,oBAAoB,CAAC,SAAS,EAAE,WAAW;EACpD;EACA;EACA;EACA,CAAA,iBAAiB,EAAE;KACjB;OACE,IAAI,CAAC,iBAAiB,EAAE;EAC5B,OAAM,IAAI,KAAK,CAAC,eAAe,KAAK,SAAS,EAAE;WACvC,iBAAiB,GAAG,IAAI,CAAC;AACjC;WACQ,KAAK,CAAC,gEAAgE,GAAG,6CAA6C,GAAG,gEAAgE,GAAG,yBAAyB,CAAC,CAAC;UACxN;QACF;MACF;EACH;EACA;EACA;AACA;AACA;EACA,GAAE,IAAI,KAAK,GAAG,WAAW,EAAE,CAAC;AAC5B;KACE;OACE,IAAI,CAAC,0BAA0B,EAAE;EACrC,OAAM,IAAI,WAAW,GAAG,WAAW,EAAE,CAAC;AACtC;SACM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE;EACzC,SAAQ,KAAK,CAAC,sEAAsE,CAAC,CAAC;AACtF;WACQ,0BAA0B,GAAG,IAAI,CAAC;UACnC;QACF;MACF;EACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;AACA;EACA,GAAE,IAAI,SAAS,GAAG,QAAQ,CAAC;EAC3B,KAAI,IAAI,EAAE;SACJ,KAAK,EAAE,KAAK;SACZ,WAAW,EAAE,WAAW;QACzB;EACL,IAAG,CAAC;EACJ,OAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI;EAC9B,OAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;EACjC;EACA;AACA;AACA;KACE,eAAe,CAAC,YAAY;EAC9B,KAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACvB,KAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;EACnC;EACA;EACA;AACA;EACA,KAAI,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;EACtC;EACA,OAAM,WAAW,CAAC;WACV,IAAI,EAAE,IAAI;EAClB,QAAO,CAAC,CAAC;QACJ;MACF,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;KACpC,SAAS,CAAC,YAAY;EACxB;EACA;EACA,KAAI,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;EACtC;EACA,OAAM,WAAW,CAAC;WACV,IAAI,EAAE,IAAI;EAClB,QAAO,CAAC,CAAC;QACJ;AACL;OACI,IAAI,iBAAiB,GAAG,YAAY;EACxC;EACA;EACA;EACA;EACA;EACA;EACA,OAAM,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;EACxC;EACA,SAAQ,WAAW,CAAC;aACV,IAAI,EAAE,IAAI;EACpB,UAAS,CAAC,CAAC;UACJ;EACP,MAAK,CAAC;AACN;AACA;EACA,KAAI,OAAO,SAAS,CAAC,iBAAiB,CAAC,CAAC;EACxC,IAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;EAClB,GAAE,aAAa,CAAC,KAAK,CAAC,CAAC;KACrB,OAAO,KAAK,CAAC;IACd;AACD;GACA,SAAS,sBAAsB,CAAC,IAAI,EAAE;EACtC,GAAE,IAAI,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC;EAC3C,GAAE,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7B;EACA,GAAE,IAAI;EACN,KAAI,IAAI,SAAS,GAAG,iBAAiB,EAAE,CAAC;OACpC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;MACxC,CAAC,OAAO,KAAK,EAAE;OACd,OAAO,IAAI,CAAC;MACb;IACF;AACD;EACA,CAAA,SAAS,sBAAsB,CAAC,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE;EAC3E;EACA;EACA;EACA;KACE,OAAO,WAAW,EAAE,CAAC;IACtB;AACD;GACA,IAAI,SAAS,GAAG,CAAC,EAAE,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,KAAK,WAAW,CAAC,CAAC;AACpJ;EACA,CAAA,IAAI,mBAAmB,GAAG,CAAC,SAAS,CAAC;AACrC;EACA,CAAA,IAAI,IAAI,GAAG,mBAAmB,GAAG,sBAAsB,GAAG,oBAAoB,CAAC;EAC/E,CAAA,IAAI,sBAAsB,GAAG,KAAK,CAAC,oBAAoB,KAAK,SAAS,GAAG,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;AAC1G;EACA,CAA4B,oCAAA,CAAA,oBAAA,GAAG,sBAAsB,CAAC;EACtD;EACA,CAAA;KACE,OAAO,8BAA8B,KAAK,WAAW;KACrD,OAAO,8BAA8B,CAAC,0BAA0B;EAClE,KAAI,UAAU;KACZ;KACA,8BAA8B,CAAC,0BAA0B,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;IACxE;EACD;EACA,IAAG,GAAG,CAAC;EACP,EAAA;;;;;AC7OA;EACA,CAEO;KACL,MAAA,CAAA,OAAA,GAAiBC,6CAA6D,CAAC;EACjF,EAAA;;;ECFO,MAAMC,oBAAoB,GAAGC,iCAA7B;;ACQA,QAAMC,cAAc,gBAAGC,gBAAK,CAACC,aAAN,CAC5BC,SAD4B,EAAvB;EAGP,MAAMC,yBAAyB,gBAAGH,gBAAK,CAACC,aAAN,CAA6B,KAA7B,CAAlC;EAGA;EACA;EACA;EACA;EACA;EACA;;EACA,SAASG,qBAAT,CACEC,OADF,EAEEC,cAFF,EAGE;EACA,EAAA,IAAID,OAAJ,EAAa;EACX,IAAA,OAAOA,OAAP,CAAA;EACD,GAAA;;EACD,EAAA,IAAIC,cAAc,IAAI,OAAOC,MAAP,KAAkB,WAAxC,EAAqD;EACnD,IAAA,IAAI,CAACA,MAAM,CAACC,uBAAZ,EAAqC;QACnCD,MAAM,CAACC,uBAAP,GAAiCT,cAAjC,CAAA;EACD,KAAA;;MAED,OAAOQ,MAAM,CAACC,uBAAd,CAAA;EACD,GAAA;;EAED,EAAA,OAAOT,cAAP,CAAA;EACD,CAAA;;AAEM,QAAMU,cAAc,GAAG,CAAC;EAAEJ,EAAAA,OAAAA;EAAF,CAAA,GAA8B,EAA/B,KAAsC;EAClE,EAAA,MAAMK,WAAW,GAAGV,gBAAK,CAACW,UAAN,CAClBP,qBAAqB,CAACC,OAAD,EAAUL,gBAAK,CAACW,UAAN,CAAiBR,yBAAjB,CAAV,CADH,CAApB,CAAA;;IAIA,IAAI,CAACO,WAAL,EAAkB;EAChB,IAAA,MAAM,IAAIE,KAAJ,CAAU,wDAAV,CAAN,CAAA;EACD,GAAA;;EAED,EAAA,OAAOF,WAAP,CAAA;EACD,EAVM;AA4BA,QAAMG,mBAAmB,GAAG,CAAC;IAClCC,MADkC;IAElCC,QAFkC;IAGlCV,OAHkC;EAIlCC,EAAAA,cAAc,GAAG,KAAA;EAJiB,CAAD,KAKU;IAC3CN,gBAAK,CAACgB,SAAN,CAAgB,MAAM;EACpBF,IAAAA,MAAM,CAACG,KAAP,EAAA,CAAA;EACA,IAAA,OAAO,MAAM;EACXH,MAAAA,MAAM,CAACI,OAAP,EAAA,CAAA;OADF,CAAA;KAFF,EAKG,CAACJ,MAAD,CALH,CAAA,CAAA;;IAOA,IAA6CR,cAA7C,EAA6D;MAC3DQ,MAAM,CACHK,SADH,EAAA,CAEGC,KAFH,CAAA,6FAAA,CAAA,CAAA;EAKD,GAAA;;EAED,EAAA,MAAMC,OAAO,GAAGjB,qBAAqB,CAACC,OAAD,EAAUC,cAAV,CAArC,CAAA;IAEA,oBACEN,gBAAA,CAAA,aAAA,CAAC,yBAAD,CAA2B,QAA3B,EAAA;MAAoC,KAAK,EAAE,CAACK,OAAD,IAAYC,cAAAA;KACrD,eAAAN,gBAAA,CAAA,aAAA,CAAC,OAAD,CAAS,QAAT,EAAA;EAAkB,IAAA,KAAK,EAAEc,MAAAA;KAASC,EAAAA,QAAlC,CADF,CADF,CAAA;EAKD;;EC/FD,MAAMO,kBAAkB,gBAAGtB,gBAAK,CAACC,aAAN,CAAoB,KAApB,CAA3B,CAAA;AAEO,QAAMsB,cAAc,GAAG,MAAMvB,gBAAK,CAACW,UAAN,CAAiBW,kBAAjB,EAA7B;AACME,QAAAA,mBAAmB,GAAGF,kBAAkB,CAACG;;ECKtD,SAASC,WAAT,GAAqD;IACnD,IAAIC,OAAO,GAAG,KAAd,CAAA;IACA,OAAO;EACLC,IAAAA,UAAU,EAAE,MAAM;EAChBD,MAAAA,OAAO,GAAG,KAAV,CAAA;OAFG;EAILE,IAAAA,KAAK,EAAE,MAAM;EACXF,MAAAA,OAAO,GAAG,IAAV,CAAA;OALG;EAOLA,IAAAA,OAAO,EAAE,MAAM;EACb,MAAA,OAAOA,OAAP,CAAA;EACD,KAAA;KATH,CAAA;EAWD,CAAA;;EAED,MAAMG,8BAA8B,gBAAG9B,gBAAK,CAACC,aAAN,CAAoByB,WAAW,EAA/B,CAAvC;;AAIO,QAAMK,0BAA0B,GAAG,MACxC/B,gBAAK,CAACW,UAAN,CAAiBmB,8BAAjB;;AAUK,QAAME,uBAAuB,GAAG,CAAC;EACtCjB,EAAAA,QAAAA;EADsC,CAAD,KAEH;IAClC,MAAM,CAACkB,KAAD,CAAA,GAAUjC,gBAAK,CAACkC,QAAN,CAAe,MAAMR,WAAW,EAAhC,CAAhB,CAAA;IACA,oBACE1B,gBAAA,CAAA,aAAA,CAAC,8BAAD,CAAgC,QAAhC,EAAA;EAAyC,IAAA,KAAK,EAAEiC,KAAAA;KAC7C,EAAA,OAAOlB,QAAP,KAAoB,UAApB,GACIA,QAAD,CAAuBkB,KAAvB,CADH,GAEGlB,QAHN,CADF,CAAA;EAOD;;ECpDM,SAASoB,gBAAT,CACLC,iBADK,EAELC,MAFK,EAGI;EACT;EACA,EAAA,IAAI,OAAOD,iBAAP,KAA6B,UAAjC,EAA6C;EAC3C,IAAA,OAAOA,iBAAiB,CAAC,GAAGC,MAAJ,CAAxB,CAAA;EACD,GAAA;;IAED,OAAO,CAAC,CAACD,iBAAT,CAAA;EACD;;ECEM,MAAME,+BAA+B,GAAG,CAO7CC,OAP6C,EAc7CC,kBAd6C,KAe1C;EACH,EAAA,IAAID,OAAO,CAACE,QAAR,IAAoBF,OAAO,CAACG,gBAAhC,EAAkD;EAChD;EACA,IAAA,IAAI,CAACF,kBAAkB,CAACb,OAAnB,EAAL,EAAmC;QACjCY,OAAO,CAACI,YAAR,GAAuB,KAAvB,CAAA;EACD,KAAA;EACF,GAAA;EACF,CAtBM,CAAA;EAwBA,MAAMC,0BAA0B,GACrCJ,kBADwC,IAErC;IACHxC,gBAAK,CAACgB,SAAN,CAAgB,MAAM;EACpBwB,IAAAA,kBAAkB,CAACZ,UAAnB,EAAA,CAAA;KADF,EAEG,CAACY,kBAAD,CAFH,CAAA,CAAA;EAGD,CANM,CAAA;EAQA,MAAMK,WAAW,GAAG,CAMzB;IACAC,MADA;IAEAN,kBAFA;IAGAE,gBAHA;EAIAK,EAAAA,KAAAA;EAJA,CANyB,KAqBrB;IACJ,OACED,MAAM,CAACE,OAAP,IACA,CAACR,kBAAkB,CAACb,OAAnB,EADD,IAEA,CAACmB,MAAM,CAACG,UAFR,IAGAd,gBAAgB,CAACO,gBAAD,EAAmB,CAACI,MAAM,CAAC1B,KAAR,EAAe2B,KAAf,CAAnB,CAJlB,CAAA;EAMD,CA5BM;;ECtCA,MAAMG,eAAe,GAC1BC,gBAD6B,IAE1B;IACH,IAAIA,gBAAgB,CAACV,QAArB,EAA+B;EAC7B;EACA;EACA,IAAA,IAAI,OAAOU,gBAAgB,CAACC,SAAxB,KAAsC,QAA1C,EAAoD;QAClDD,gBAAgB,CAACC,SAAjB,GAA6B,IAA7B,CAAA;EACD,KAAA;EACF,GAAA;EACF,CAVM,CAAA;EAYA,MAAMC,SAAS,GAAG,CACvBP,MADuB,EAEvBQ,WAFuB,KAGpBR,MAAM,CAACS,SAAP,IAAoBT,MAAM,CAACG,UAA3B,IAAyC,CAACK,WAHxC,CAAA;EAKA,MAAME,aAAa,GAAG,CAC3BL,gBAD2B,EAI3BL,MAJ2B,EAK3BQ,WAL2B,KAMxB,CAAAH,gBAAgB,IAAhB,IAAA,GAAA,KAAA,CAAA,GAAAA,gBAAgB,CAAEV,QAAlB,KAA8BY,SAAS,CAACP,MAAD,EAASQ,WAAT,CANrC,CAAA;EAQA,MAAMG,eAAe,GAAG,CAO7BN,gBAP6B,EAc7BO,QAd6B,EAe7BlB,kBAf6B,KAiB7BkB,QAAQ,CACLD,eADH,CACmBN,gBADnB,CAEGQ,CAAAA,IAFH,CAEQ,CAAC;EAAEC,EAAAA,IAAAA;EAAF,CAAD,KAAc;EAClBT,EAAAA,gBAAgB,CAACU,SAAjB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAAV,gBAAgB,CAACU,SAAjB,CAA6BD,IAA7B,CAAA,CAAA;IACAT,gBAAgB,CAACW,SAAjB,IAAAX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAgB,CAACW,SAAjB,CAA6BF,IAA7B,EAAmC,IAAnC,CAAA,CAAA;EACD,CALH,CAMGG,CAAAA,KANH,CAMU3C,KAAD,IAAW;EAChBoB,EAAAA,kBAAkB,CAACZ,UAAnB,EAAA,CAAA;EACAuB,EAAAA,gBAAgB,CAACa,OAAjB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAAb,gBAAgB,CAACa,OAAjB,CAA2B5C,KAA3B,CAAA,CAAA;IACA+B,gBAAgB,CAACW,SAAjB,IAAAX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAgB,CAACW,SAAjB,CAA6B5D,SAA7B,EAAwCkB,KAAxC,CAAA,CAAA;EACD,CAVH,CAjBK;;ECmJA,SAAS6C,UAAT,CAAqC;IAC1CC,OAD0C;EAE1C7D,EAAAA,OAAAA;EAF0C,CAArC,EAMe;IACpB,MAAMK,WAAW,GAAGD,cAAc,CAAC;EAAEJ,IAAAA,OAAAA;EAAF,GAAD,CAAlC,CAAA;IACA,MAAMiD,WAAW,GAAG/B,cAAc,EAAlC,CAAA;IACA,MAAMiB,kBAAkB,GAAGT,0BAA0B,EAArD,CAAA;EAEA,EAAA,MAAMoC,gBAAgB,GAAGnE,gBAAK,CAACoE,OAAN,CACvB,MACEF,OAAO,CAACG,GAAR,CAAa9B,OAAD,IAAa;MACvB,MAAMY,gBAAgB,GAAGzC,WAAW,CAAC4D,mBAAZ,CAAgC/B,OAAhC,CAAzB,CADuB;;EAIvBY,IAAAA,gBAAgB,CAACoB,kBAAjB,GAAsCjB,WAAW,GAC7C,aAD6C,GAE7C,YAFJ,CAAA;EAIA,IAAA,OAAOH,gBAAP,CAAA;KARF,CAFqB,EAYvB,CAACe,OAAD,EAAUxD,WAAV,EAAuB4C,WAAvB,CAZuB,CAAzB,CAAA;EAeAa,EAAAA,gBAAgB,CAACK,OAAjB,CAA0BzB,KAAD,IAAW;MAClCG,eAAe,CAACH,KAAD,CAAf,CAAA;EACAT,IAAAA,+BAA+B,CAACS,KAAD,EAAQP,kBAAR,CAA/B,CAAA;KAFF,CAAA,CAAA;IAKAI,0BAA0B,CAACJ,kBAAD,CAA1B,CAAA;EAEA,EAAA,MAAM,CAACkB,QAAD,CAAa1D,GAAAA,gBAAK,CAACkC,QAAN,CACjB,MAAM,IAAIuC,eAAJ,CAAoB/D,WAApB,EAAiCyD,gBAAjC,CADW,CAAnB,CAAA;EAIA,EAAA,MAAMO,gBAAgB,GAAGhB,QAAQ,CAACiB,mBAAT,CAA6BR,gBAA7B,CAAzB,CAAA;EAEAtE,EAAAA,oBAAoB,CAClBG,gBAAK,CAAC4E,WAAN,CACGC,aAAD,IACEvB,WAAW,GACP,MAAMpD,SADC,GAEPwD,QAAQ,CAACoB,SAAT,CAAmBrF,aAAa,CAACsF,UAAd,CAAyBF,aAAzB,CAAnB,CAJR,EAKE,CAACnB,QAAD,EAAWJ,WAAX,CALF,CADkB,EAQlB,MAAMI,QAAQ,CAACsB,gBAAT,EARY,EASlB,MAAMtB,QAAQ,CAACsB,gBAAT,EATY,CAApB,CAAA;IAYAhF,gBAAK,CAACgB,SAAN,CAAgB,MAAM;EACpB;EACA;EACA0C,IAAAA,QAAQ,CAACuB,UAAT,CAAoBd,gBAApB,EAAsC;EAAEe,MAAAA,SAAS,EAAE,KAAA;OAAnD,CAAA,CAAA;EACD,GAJD,EAIG,CAACf,gBAAD,EAAmBT,QAAnB,CAJH,CAAA,CAAA;IAMA,MAAMyB,uBAAuB,GAAGT,gBAAgB,CAACU,IAAjB,CAAsB,CAACtC,MAAD,EAASuC,KAAT,KACpD7B,aAAa,CAACW,gBAAgB,CAACkB,KAAD,CAAjB,EAA0BvC,MAA1B,EAAkCQ,WAAlC,CADiB,CAAhC,CAAA;EAIA,EAAA,MAAMgC,gBAAgB,GAAGH,uBAAuB,GAC5CT,gBAAgB,CAACa,OAAjB,CAAyB,CAACzC,MAAD,EAASuC,KAAT,KAAmB;EAC1C,IAAA,MAAM9C,OAAO,GAAG4B,gBAAgB,CAACkB,KAAD,CAAhC,CAAA;EACA,IAAA,MAAMG,aAAa,GAAG9B,QAAQ,CAAC+B,YAAT,EAAA,CAAwBJ,KAAxB,CAAtB,CAAA;;MAEA,IAAI9C,OAAO,IAAIiD,aAAf,EAA8B;QAC5B,IAAIhC,aAAa,CAACjB,OAAD,EAAUO,MAAV,EAAkBQ,WAAlB,CAAjB,EAAiD;EAC/C,QAAA,OAAOG,eAAe,CAAClB,OAAD,EAAUiD,aAAV,EAAyBhD,kBAAzB,CAAtB,CAAA;SADF,MAEO,IAAIa,SAAS,CAACP,MAAD,EAASQ,WAAT,CAAb,EAAoC;EACzC,QAAA,KAAKG,eAAe,CAAClB,OAAD,EAAUiD,aAAV,EAAyBhD,kBAAzB,CAApB,CAAA;EACD,OAAA;EACF,KAAA;;EACD,IAAA,OAAO,EAAP,CAAA;KAXF,CAD4C,GAc5C,EAdJ,CAAA;;EAgBA,EAAA,IAAI8C,gBAAgB,CAACI,MAAjB,GAA0B,CAA9B,EAAiC;EAC/B,IAAA,MAAMC,OAAO,CAACC,GAAR,CAAYN,gBAAZ,CAAN,CAAA;EACD,GAAA;;EACD,EAAA,MAAMO,eAAe,GAAGnC,QAAQ,CAACoC,UAAT,EAAxB,CAAA;IACA,MAAMC,iCAAiC,GAAGrB,gBAAgB,CAACsB,IAAjB,CACxC,CAAClD,MAAD,EAASuC,KAAT,KAAA;EAAA,IAAA,IAAA,qBAAA,EAAA,sBAAA,CAAA;;EAAA,IAAA,OACExC,WAAW,CAAC;QACVC,MADU;QAEVN,kBAFU;QAGVE,gBAAgB,EAAA,CAAA,qBAAA,GAAA,CAAA,sBAAA,GAAEyB,gBAAgB,CAACkB,KAAD,CAAlB,KAAE,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAyB3C,gBAA3B,KAAA,IAAA,GAAA,qBAAA,GAA+C,KAHrD;QAIVK,KAAK,EAAE8C,eAAe,CAACR,KAAD,CAAA;EAJZ,KAAD,CADb,CAAA;EAAA,GADwC,CAA1C,CAAA;;EAUA,EAAA,IAAIU,iCAAJ,IAAA,IAAA,IAAIA,iCAAiC,CAAE3E,KAAvC,EAA8C;MAC5C,MAAM2E,iCAAiC,CAAC3E,KAAxC,CAAA;EACD,GAAA;;EAED,EAAA,OAAOsD,gBAAP,CAAA;EACD;;ECjQM,SAASuB,YAAT,CAOL1D,OAPK,EAcL2D,QAdK,EAeL;IACA,MAAMxF,WAAW,GAAGD,cAAc,CAAC;MAAEJ,OAAO,EAAEkC,OAAO,CAAClC,OAAAA;EAAnB,GAAD,CAAlC,CAAA;IACA,MAAMiD,WAAW,GAAG/B,cAAc,EAAlC,CAAA;IACA,MAAMiB,kBAAkB,GAAGT,0BAA0B,EAArD,CAAA;IACA,MAAMoB,gBAAgB,GAAGzC,WAAW,CAAC4D,mBAAZ,CAAgC/B,OAAhC,CAAzB,CAJA;;IAOAY,gBAAgB,CAACoB,kBAAjB,GAAsCjB,WAAW,GAC7C,aAD6C,GAE7C,YAFJ,CAPA;;IAYA,IAAIH,gBAAgB,CAACa,OAArB,EAA8B;MAC5Bb,gBAAgB,CAACa,OAAjB,GAA2BvE,aAAa,CAACsF,UAAd,CACzB5B,gBAAgB,CAACa,OADQ,CAA3B,CAAA;EAGD,GAAA;;IAED,IAAIb,gBAAgB,CAACU,SAArB,EAAgC;MAC9BV,gBAAgB,CAACU,SAAjB,GAA6BpE,aAAa,CAACsF,UAAd,CAC3B5B,gBAAgB,CAACU,SADU,CAA7B,CAAA;EAGD,GAAA;;IAED,IAAIV,gBAAgB,CAACW,SAArB,EAAgC;MAC9BX,gBAAgB,CAACW,SAAjB,GAA6BrE,aAAa,CAACsF,UAAd,CAC3B5B,gBAAgB,CAACW,SADU,CAA7B,CAAA;EAGD,GAAA;;IAEDZ,eAAe,CAACC,gBAAD,CAAf,CAAA;EACAb,EAAAA,+BAA+B,CAACa,gBAAD,EAAmBX,kBAAnB,CAA/B,CAAA;IAEAI,0BAA0B,CAACJ,kBAAD,CAA1B,CAAA;EAEA,EAAA,MAAM,CAACkB,QAAD,CAAa1D,GAAAA,gBAAK,CAACkC,QAAN,CACjB,MACE,IAAIgE,QAAJ,CACExF,WADF,EAEEyC,gBAFF,CAFe,CAAnB,CAAA;EAQA,EAAA,MAAML,MAAM,GAAGY,QAAQ,CAACiB,mBAAT,CAA6BxB,gBAA7B,CAAf,CAAA;EAEAtD,EAAAA,oBAAoB,CAClBG,gBAAK,CAAC4E,WAAN,CACGC,aAAD,IAAmB;EACjB,IAAA,MAAMsB,WAAW,GAAG7C,WAAW,GAC3B,MAAMpD,SADqB,GAE3BwD,QAAQ,CAACoB,SAAT,CAAmBrF,aAAa,CAACsF,UAAd,CAAyBF,aAAzB,CAAnB,CAFJ,CADiB;EAMjB;;EACAnB,IAAAA,QAAQ,CAAC0C,YAAT,EAAA,CAAA;EAEA,IAAA,OAAOD,WAAP,CAAA;EACD,GAXH,EAYE,CAACzC,QAAD,EAAWJ,WAAX,CAZF,CADkB,EAelB,MAAMI,QAAQ,CAACsB,gBAAT,EAfY,EAgBlB,MAAMtB,QAAQ,CAACsB,gBAAT,EAhBY,CAApB,CAAA;IAmBAhF,gBAAK,CAACgB,SAAN,CAAgB,MAAM;EACpB;EACA;EACA0C,IAAAA,QAAQ,CAAC2C,UAAT,CAAoBlD,gBAApB,EAAsC;EAAE+B,MAAAA,SAAS,EAAE,KAAA;OAAnD,CAAA,CAAA;EACD,GAJD,EAIG,CAAC/B,gBAAD,EAAmBO,QAAnB,CAJH,EAhEA;;IAuEA,IAAIF,aAAa,CAACL,gBAAD,EAAmBL,MAAnB,EAA2BQ,WAA3B,CAAjB,EAA0D;EACxD,IAAA,MAAMG,eAAe,CAACN,gBAAD,EAAmBO,QAAnB,EAA6BlB,kBAA7B,CAArB,CAAA;EACD,GAzED;;;EA4EA,EAAA,IACEK,WAAW,CAAC;MACVC,MADU;MAEVN,kBAFU;MAGVE,gBAAgB,EAAES,gBAAgB,CAACT,gBAHzB;MAIVK,KAAK,EAAEW,QAAQ,CAAC4C,eAAT,EAAA;EAJG,GAAD,CADb,EAOE;MACA,MAAMxD,MAAM,CAAC1B,KAAb,CAAA;EACD,GArFD;;;EAwFA,EAAA,OAAO,CAAC+B,gBAAgB,CAACoD,mBAAlB,GACH7C,QAAQ,CAAC8C,WAAT,CAAqB1D,MAArB,CADG,GAEHA,MAFJ,CAAA;EAGD;;ECeM,SAAS2D,QAAT,CAMLC,IANK,EAOLC,IAPK,EAULC,IAVK,EAW0B;IAC/B,MAAMC,aAAa,GAAGC,cAAc,CAACJ,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAApC,CAAA;EACA,EAAA,OAAOX,YAAY,CAACY,aAAD,EAAgBE,aAAhB,CAAnB,CAAA;EACD;;ECnJM,SAASC,gBAAT,CAKLzE,OALK,EAKqE;EAC1E,EAAA,OAAO0D,YAAY,CACjB,EACE,GAAG1D,OADL;EAEE0E,IAAAA,OAAO,EAAE,IAFX;EAGEvE,IAAAA,gBAAgB,EAAE,IAHpB;EAIED,IAAAA,QAAQ,EAAE,IAJZ;EAKEyE,IAAAA,eAAe,EAAEhH,SALnB;EAMEiH,IAAAA,WAAW,EAAE,QANf;EAOEtD,IAAAA,SAAS,EAAE3D,SAPb;EAQE8D,IAAAA,OAAO,EAAE9D,SARX;EASE4D,IAAAA,SAAS,EAAE5D,SAAAA;KAVI,EAYjB6G,aAZiB,CAAnB,CAAA;EAcD;;ECsHM,SAASK,kBAAT,CAA6C;IAClDlD,OADkD;EAElD7D,EAAAA,OAAAA;EAFkD,CAA7C,EAMuB;EAC5B,EAAA,OAAO4D,UAAU,CAAC;MAChBC,OAAO,EAAEA,OAAO,CAACG,GAAR,CAAatB,KAAD,KAAY,EAC/B,GAAGA,KAD4B;EAE/BkE,MAAAA,OAAO,EAAE,IAFsB;EAG/BvE,MAAAA,gBAAgB,EAAE,IAHa;EAI/BD,MAAAA,QAAQ,EAAE,IAJqB;EAK/ByE,MAAAA,eAAe,EAAEhH,SALc;EAM/BiH,MAAAA,WAAW,EAAE,QAAA;EANkB,KAAZ,CAAZ,CADO;EAShB9G,IAAAA,OAAAA;EATgB,GAAD,CAAjB,CAAA;EAWD;;EC9EM,SAASgH,YAAT,CAAsB9E,OAAtB,EAAwC;EAC7C,EAAA,OAAOA,OAAP,CAAA;EACD;;EC7EM,SAAS+E,UAAT,CACLC,KADK,EAELhF,OAAwC,GAAG,EAFtC,EAGL;IACA,MAAM7B,WAAW,GAAGD,cAAc,CAAC;MAAEJ,OAAO,EAAEkC,OAAO,CAAClC,OAAAA;EAAnB,GAAD,CAAlC,CAAA;EAEA,EAAA,MAAMmH,UAAU,GAAGxH,gBAAK,CAACyH,MAAN,CAAalF,OAAb,CAAnB,CAAA;EACAiF,EAAAA,UAAU,CAACE,OAAX,GAAqBnF,OAArB,CAJA;EAOA;EACA;EACA;;IACAvC,gBAAK,CAACoE,OAAN,CAAc,MAAM;EAClB,IAAA,IAAImD,KAAJ,EAAW;QACTI,OAAO,CAACjH,WAAD,EAAc6G,KAAd,EAAqBC,UAAU,CAACE,OAAhC,CAAP,CAAA;EACD,KAAA;EACF,GAJD,EAIG,CAAChH,WAAD,EAAc6G,KAAd,CAJH,CAAA,CAAA;EAKD,CAAA;AAQM,QAAMK,OAAO,GAAG,CAAC;IAAE7G,QAAF;IAAYwB,OAAZ;EAAqBgF,EAAAA,KAAAA;EAArB,CAAD,KAAgD;EACrED,EAAAA,UAAU,CAACC,KAAD,EAAQhF,OAAR,CAAV,CAAA;EACA,EAAA,OAAOxB,QAAP,CAAA;EACD;;ECpBM,SAAS8G,aAAT,CACLnB,IADK,EAELC,IAFK,EAGLC,IAHK,EAIG;EACR,EAAA,MAAM,CAACkB,OAAD,EAAUvF,OAAO,GAAG,EAApB,CAAA,GAA0BwF,eAAe,CAACrB,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAA/C,CAAA;IACA,MAAMlG,WAAW,GAAGD,cAAc,CAAC;MAAEJ,OAAO,EAAEkC,OAAO,CAAClC,OAAAA;EAAnB,GAAD,CAAlC,CAAA;EACA,EAAA,MAAM2H,UAAU,GAAGtH,WAAW,CAACuH,aAAZ,EAAnB,CAAA;EAEA,EAAA,OAAOpI,oBAAoB,CACzBG,gBAAK,CAAC4E,WAAN,CACGC,aAAD,IACEmD,UAAU,CAAClD,SAAX,CAAqBrF,aAAa,CAACsF,UAAd,CAAyBF,aAAzB,CAArB,CAFJ,EAGE,CAACmD,UAAD,CAHF,CADyB,EAMzB,MAAMtH,WAAW,CAACuC,UAAZ,CAAuB6E,OAAvB,CANmB,EAOzB,MAAMpH,WAAW,CAACuC,UAAZ,CAAuB6E,OAAvB,CAPmB,CAA3B,CAAA;EASD;;ECfM,SAASI,aAAT,CACLxB,IADK,EAELC,IAFK,EAGLC,IAHK,EAIG;EACR,EAAA,MAAM,CAACkB,OAAD,EAAUvF,OAAO,GAAG,EAApB,CAAA,GAA0B4F,uBAAuB,CAACzB,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAAvD,CAAA;IAEA,MAAMlG,WAAW,GAAGD,cAAc,CAAC;MAAEJ,OAAO,EAAEkC,OAAO,CAAClC,OAAAA;EAAnB,GAAD,CAAlC,CAAA;EACA,EAAA,MAAM+H,aAAa,GAAG1H,WAAW,CAAC2H,gBAAZ,EAAtB,CAAA;EAEA,EAAA,OAAOxI,oBAAoB,CACzBG,gBAAK,CAAC4E,WAAN,CACGC,aAAD,IACEuD,aAAa,CAACtD,SAAd,CAAwBrF,aAAa,CAACsF,UAAd,CAAyBF,aAAzB,CAAxB,CAFJ,EAGE,CAACuD,aAAD,CAHF,CADyB,EAMzB,MAAM1H,WAAW,CAAC4H,UAAZ,CAAuBR,OAAvB,CANmB,EAOzB,MAAMpH,WAAW,CAAC4H,UAAZ,CAAuBR,OAAvB,CAPmB,CAA3B,CAAA;EASD;;EC0BM,SAASS,WAAT,CAML7B,IANK,EAULC,IAVK,EAaLC,IAbK,EAcmD;IACxD,MAAMrE,OAAO,GAAGiG,iBAAiB,CAAC9B,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAAjC,CAAA;IACA,MAAMlG,WAAW,GAAGD,cAAc,CAAC;MAAEJ,OAAO,EAAEkC,OAAO,CAAClC,OAAAA;EAAnB,GAAD,CAAlC,CAAA;EAEA,EAAA,MAAM,CAACqD,QAAD,CAAa1D,GAAAA,gBAAK,CAACkC,QAAN,CACjB,MACE,IAAIuG,gBAAJ,CACE/H,WADF,EAEE6B,OAFF,CAFe,CAAnB,CAAA;IAQAvC,gBAAK,CAACgB,SAAN,CAAgB,MAAM;MACpB0C,QAAQ,CAAC2C,UAAT,CAAoB9D,OAApB,CAAA,CAAA;EACD,GAFD,EAEG,CAACmB,QAAD,EAAWnB,OAAX,CAFH,CAAA,CAAA;EAIA,EAAA,MAAMO,MAAM,GAAGjD,oBAAoB,CACjCG,gBAAK,CAAC4E,WAAN,CACGC,aAAD,IACEnB,QAAQ,CAACoB,SAAT,CAAmBrF,aAAa,CAACsF,UAAd,CAAyBF,aAAzB,CAAnB,CAFJ,EAGE,CAACnB,QAAD,CAHF,CADiC,EAMjC,MAAMA,QAAQ,CAACsB,gBAAT,EAN2B,EAOjC,MAAMtB,QAAQ,CAACsB,gBAAT,EAP2B,CAAnC,CAAA;IAUA,MAAM0D,MAAM,GAAG1I,gBAAK,CAAC4E,WAAN,CAGb,CAAC+D,SAAD,EAAYC,aAAZ,KAA8B;MAC5BlF,QAAQ,CAACgF,MAAT,CAAgBC,SAAhB,EAA2BC,aAA3B,CAAA,CAA0C7E,KAA1C,CAAgD1E,IAAhD,CAAA,CAAA;EACD,GALY,EAMb,CAACqE,QAAD,CANa,CAAf,CAAA;;EASA,EAAA,IACEZ,MAAM,CAAC1B,KAAP,IACAe,gBAAgB,CAACuB,QAAQ,CAACnB,OAAT,CAAiBG,gBAAlB,EAAoC,CAACI,MAAM,CAAC1B,KAAR,CAApC,CAFlB,EAGE;MACA,MAAM0B,MAAM,CAAC1B,KAAb,CAAA;EACD,GAAA;;IAED,OAAO,EAAE,GAAG0B,MAAL;MAAa4F,MAAb;MAAqBG,WAAW,EAAE/F,MAAM,CAAC4F,MAAAA;KAAhD,CAAA;EACD;;EAGD,SAASrJ,IAAT,GAAgB;;EC9DT,SAASyJ,gBAAT,CAMLpC,IANK,EAeLC,IAfK,EAwBLC,IAxBK,EA+BkC;IACvC,MAAMrE,OAAO,GAAGuE,cAAc,CAACJ,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAA9B,CAAA;EACA,EAAA,OAAOX,YAAY,CACjB1D,OADiB,EAEjBwG,qBAFiB,CAAnB,CAAA;EAID;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}