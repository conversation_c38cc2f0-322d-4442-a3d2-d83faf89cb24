{"version": 3, "file": "queryOptions.d.ts", "sourceRoot": "", "sources": ["../../src/queryOptions.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,mBAAmB,EACnB,iBAAiB,EACjB,SAAS,EACT,QAAQ,EACR,YAAY,EACb,MAAM,sBAAsB,CAAA;AAC7B,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,SAAS,CAAA;AAE9C,aAAK,sBAAsB,CACzB,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,IACnC,SAAS,CACX,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACvD,WAAW,GAAG,SAAS,GAAG,WAAW,GAAG,iBAAiB,CAC1D,CAAA;AAED,aAAK,6BAA6B,GAAG,MAAM,IAAI,CAC7C,sBAAsB,EACtB,kBAAkB,GAAG,UAAU,GAAG,kBAAkB,GAAG,sBAAsB,CAC9E,CAAA;AAED,oBAAY,2BAA2B,CACrC,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,IACnC,sBAAsB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,GAAG;IACnE,WAAW,CAAC,EACR,SAAS,GACT,mBAAmB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,GACpD,iBAAiB,CAAC,YAAY,CAAC,CAAA;CACpC,CAAA;AAED,oBAAY,yBAAyB,CACnC,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,IACnC,sBAAsB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,GAAG;IACnE,WAAW,EACP,iBAAiB,CAAC,YAAY,CAAC,GAC/B,CAAC,MAAM,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAA;CAC5C,CAAA;AAED,wBAAgB,YAAY,CAC1B,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,OAAO,EAAE,YAAY,CACnB,SAAS,CACP,yBAAyB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACjE,6BAA6B,CAC9B,EACD,UAAU,CACX,GACA,YAAY,CACb,yBAAyB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACjE,UAAU,CACX,CAAA;AAED,wBAAgB,YAAY,CAC1B,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,OAAO,EAAE,YAAY,CACnB,SAAS,CACP,2BAA2B,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACnE,6BAA6B,CAC9B,EACD,UAAU,CACX,GACA,YAAY,CACb,2BAA2B,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACnE,UAAU,CACX,CAAA"}