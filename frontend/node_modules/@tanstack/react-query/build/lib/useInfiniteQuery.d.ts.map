{"version": 3, "file": "useInfiniteQuery.d.ts", "sourceRoot": "", "sources": ["../../src/useInfiniteQuery.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EACV,aAAa,EACb,QAAQ,EAET,MAAM,sBAAsB,CAAA;AAC7B,OAAO,KAAK,EAAE,uBAAuB,EAAE,sBAAsB,EAAE,MAAM,SAAS,CAAA;AAI9E,wBAAgB,gBAAgB,CAC9B,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,OAAO,EAAE,uBAAuB,CAC9B,YAAY,EACZ,MAAM,EACN,KAAK,EACL,YAAY,EACZ,SAAS,CACV,GACA,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AACxC,wBAAgB,gBAAgB,CAC9B,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,QAAQ,EAAE,SAAS,EACnB,OAAO,CAAC,EAAE,IAAI,CACZ,uBAAuB,CACrB,YAAY,EACZ,MAAM,EACN,KAAK,EACL,YAAY,EACZ,SAAS,CACV,EACD,UAAU,CACX,GACA,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AACxC,wBAAgB,gBAAgB,CAC9B,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,QAAQ,EAAE,SAAS,EACnB,OAAO,EAAE,aAAa,CAAC,YAAY,EAAE,SAAS,CAAC,EAC/C,OAAO,CAAC,EAAE,IAAI,CACZ,uBAAuB,CACrB,YAAY,EACZ,MAAM,EACN,KAAK,EACL,YAAY,EACZ,SAAS,CACV,EACD,UAAU,GAAG,SAAS,CACvB,GACA,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA"}