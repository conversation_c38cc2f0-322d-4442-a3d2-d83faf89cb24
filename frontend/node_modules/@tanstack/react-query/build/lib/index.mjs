import './setBatchUpdatesFn.mjs';
export * from '@tanstack/query-core';
export { useQueries } from './useQueries.mjs';
export { useQuery } from './useQuery.mjs';
export { useSuspenseQuery } from './useSuspenseQuery.mjs';
export { useSuspenseQueries } from './useSuspenseQueries.mjs';
export { queryOptions } from './queryOptions.mjs';
export { QueryClientProvider, defaultContext, useQueryClient } from './QueryClientProvider.mjs';
export { Hydrate, useHydrate } from './Hydrate.mjs';
export { QueryErrorResetBoundary, useQueryErrorResetBoundary } from './QueryErrorResetBoundary.mjs';
export { useIsFetching } from './useIsFetching.mjs';
export { useIsMutating } from './useIsMutating.mjs';
export { useMutation } from './useMutation.mjs';
export { useInfiniteQuery } from './useInfiniteQuery.mjs';
export { IsRestoringProvider, useIsRestoring } from './isRestoring.mjs';
//# sourceMappingURL=index.mjs.map
