import './setBatchUpdatesFn';
export * from '@tanstack/query-core';
export * from './types';
export { useQueries } from './useQueries';
export type { QueriesResults, QueriesOptions } from './useQueries';
export { useQuery } from './useQuery';
export { useSuspenseQuery } from './useSuspenseQuery';
export { useSuspenseQueries } from './useSuspenseQueries';
export type { SuspenseQueriesResults, SuspenseQueriesOptions, } from './useSuspenseQueries';
export { queryOptions } from './queryOptions';
export type { DefinedInitialDataOptions, UndefinedInitialDataOptions, } from './queryOptions';
export { defaultContext, QueryClientProvider, useQueryClient, } from './QueryClientProvider';
export type { QueryClientProviderProps } from './QueryClientProvider';
export type { QueryErrorResetBoundaryProps } from './QueryErrorResetBoundary';
export { useHydrate, Hydrate } from './Hydrate';
export type { HydrateProps } from './Hydrate';
export { QueryErrorResetBoundary, useQueryErrorResetBoundary, } from './QueryErrorResetBoundary';
export { useIsFetching } from './useIsFetching';
export { useIsMutating } from './useIsMutating';
export { useMutation } from './useMutation';
export { useInfiniteQuery } from './useInfiniteQuery';
export { useIsRestoring, IsRestoringProvider } from './isRestoring';
//# sourceMappingURL=index.d.ts.map