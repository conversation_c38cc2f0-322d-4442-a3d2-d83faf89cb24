import './setBatchUpdatesFn.esm.js';
export * from '@tanstack/query-core';
export { useQueries } from './useQueries.esm.js';
export { useQuery } from './useQuery.esm.js';
export { useSuspenseQuery } from './useSuspenseQuery.esm.js';
export { useSuspenseQueries } from './useSuspenseQueries.esm.js';
export { queryOptions } from './queryOptions.esm.js';
export { QueryClientProvider, defaultContext, useQueryClient } from './QueryClientProvider.esm.js';
export { Hydrate, useHydrate } from './Hydrate.esm.js';
export { QueryErrorResetBoundary, useQueryErrorResetBoundary } from './QueryErrorResetBoundary.esm.js';
export { useIsFetching } from './useIsFetching.esm.js';
export { useIsMutating } from './useIsMutating.esm.js';
export { useMutation } from './useMutation.esm.js';
export { useInfiniteQuery } from './useInfiniteQuery.esm.js';
export { IsRestoringProvider, useIsRestoring } from './isRestoring.esm.js';
//# sourceMappingURL=index.esm.js.map
