{"version": 3, "file": "useSuspenseQuery.mjs", "sources": ["../../src/useSuspenseQuery.ts"], "sourcesContent": ["import { QueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type { QueryKey } from '@tanstack/query-core'\nimport type { UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types'\n\nexport function useSuspenseQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(options: UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>) {\n  return useBaseQuery(\n    {\n      ...options,\n      enabled: true,\n      useErrorBoundary: true,\n      suspense: true,\n      placeholderData: undefined,\n      networkMode: 'always',\n      onSuccess: undefined,\n      onError: undefined,\n      onSettled: undefined,\n    },\n    QueryObserver,\n  ) as UseSuspenseQueryResult<TData, TError>\n}\n"], "names": ["useSuspenseQuery", "options", "useBaseQuery", "enabled", "useErrorBoundary", "suspense", "placeholderData", "undefined", "networkMode", "onSuccess", "onError", "onSettled", "QueryObserver"], "mappings": ";;;AAKO,SAASA,gBAAT,CAKLC,OALK,EAKqE;AAC1E,EAAA,OAAOC,YAAY,CACjB,EACE,GAAGD,OADL;AAEEE,IAAAA,OAAO,EAAE,IAFX;AAGEC,IAAAA,gBAAgB,EAAE,IAHpB;AAIEC,IAAAA,QAAQ,EAAE,IAJZ;AAKEC,IAAAA,eAAe,EAAEC,SALnB;AAMEC,IAAAA,WAAW,EAAE,QANf;AAOEC,IAAAA,SAAS,EAAEF,SAPb;AAQEG,IAAAA,OAAO,EAAEH,SARX;AASEI,IAAAA,SAAS,EAAEJ,SAAAA;GAVI,EAYjBK,aAZiB,CAAnB,CAAA;AAcD;;;;"}