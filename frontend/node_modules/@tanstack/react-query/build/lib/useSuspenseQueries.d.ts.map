{"version": 3, "file": "useSuspenseQueries.d.ts", "sourceRoot": "", "sources": ["../../src/useSuspenseQueries.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,eAAe,EACf,uBAAuB,EACvB,sBAAsB,EACvB,MAAM,SAAS,CAAA;AAChB,OAAO,KAAK,EAAe,aAAa,EAAE,MAAM,sBAAsB,CAAA;AAGtE,aAAK,aAAa,GAAG,EAAE,CAAA;AAEvB,aAAK,kBAAkB,CAAC,CAAC,IAEvB,CAAC,SAAS;IACR,WAAW,EAAE,MAAM,YAAY,CAAA;IAC/B,KAAK,CAAC,EAAE,MAAM,MAAM,CAAA;IACpB,IAAI,EAAE,MAAM,KAAK,CAAA;CAClB,GACG,uBAAuB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,GACpD,CAAC,SAAS;IAAE,WAAW,EAAE,MAAM,YAAY,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,MAAM,CAAA;CAAE,GACnE,uBAAuB,CAAC,YAAY,EAAE,MAAM,CAAC,GAC7C,CAAC,SAAS;IAAE,IAAI,EAAE,MAAM,KAAK,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,MAAM,CAAA;CAAE,GACrD,uBAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,GAEjD,CAAC,SAAS,CAAC,MAAM,YAAY,EAAE,MAAM,MAAM,EAAE,MAAM,KAAK,CAAC,GACvD,uBAAuB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,GACpD,CAAC,SAAS,CAAC,MAAM,YAAY,EAAE,MAAM,MAAM,CAAC,GAC5C,uBAAuB,CAAC,YAAY,EAAE,MAAM,CAAC,GAC7C,CAAC,SAAS,CAAC,MAAM,YAAY,CAAC,GAC9B,uBAAuB,CAAC,YAAY,CAAC,GAEvC,CAAC,SAAS;IACN,OAAO,CAAC,EAAE,aAAa,CAAC,MAAM,YAAY,EAAE,MAAM,SAAS,CAAC,CAAA;IAC5D,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,KAAK,MAAM,KAAK,CAAA;CACpC,GACD,uBAAuB,CAAC,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,GAChE,CAAC,SAAS;IACR,OAAO,CAAC,EAAE,aAAa,CAAC,MAAM,YAAY,EAAE,MAAM,SAAS,CAAC,CAAA;CAC7D,GACD,uBAAuB,CAAC,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC,GAEvE,uBAAuB,CAAA;AAE7B,aAAK,kBAAkB,CAAC,CAAC,IAEvB,CAAC,SAAS;IAAE,WAAW,EAAE,GAAG,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,MAAM,CAAC;IAAC,IAAI,EAAE,MAAM,KAAK,CAAA;CAAE,GACnE,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,GACrC,CAAC,SAAS;IAAE,WAAW,EAAE,MAAM,YAAY,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,MAAM,CAAA;CAAE,GACnE,sBAAsB,CAAC,YAAY,EAAE,MAAM,CAAC,GAC5C,CAAC,SAAS;IAAE,IAAI,EAAE,MAAM,KAAK,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,MAAM,CAAA;CAAE,GACrD,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,GAEvC,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,MAAM,EAAE,MAAM,KAAK,CAAC,GACxC,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,GACrC,CAAC,SAAS,CAAC,MAAM,YAAY,EAAE,MAAM,MAAM,CAAC,GAC5C,sBAAsB,CAAC,YAAY,EAAE,MAAM,CAAC,GAC5C,CAAC,SAAS,CAAC,MAAM,YAAY,CAAC,GAC9B,sBAAsB,CAAC,YAAY,CAAC,GAEtC,CAAC,SAAS;IACN,OAAO,CAAC,EAAE,aAAa,CAAC,MAAM,YAAY,EAAE,GAAG,CAAC,CAAA;IAChD,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,KAAK,MAAM,KAAK,CAAA;CACpC,GACD,sBAAsB,CAAC,OAAO,SAAS,KAAK,GAAG,YAAY,GAAG,KAAK,CAAC,GACpE,CAAC,SAAS;IACR,OAAO,CAAC,EAAE,aAAa,CAAC,MAAM,YAAY,EAAE,GAAG,CAAC,CAAA;CACjD,GACD,sBAAsB,CAAC,YAAY,CAAC,GAEpC,sBAAsB,CAAA;AAE5B;;GAEG;AACH,oBAAY,sBAAsB,CAChC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,EACpB,OAAO,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAC/B,MAAM,SAAS,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,IACvC,MAAM,CAAC,QAAQ,CAAC,SAAS,aAAa,GACtC,KAAK,CAAC,uBAAuB,CAAC,GAC9B,CAAC,SAAS,EAAE,GACZ,EAAE,GACF,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,GACtB,CAAC,GAAG,OAAO,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC,GACtC,CAAC,SAAS,CAAC,MAAM,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,GACrC,sBAAsB,CACpB;IAAC,GAAG,IAAI;CAAC,EACT;IAAC,GAAG,OAAO;IAAE,kBAAkB,CAAC,IAAI,CAAC;CAAC,EACtC;IAAC,GAAG,MAAM;IAAE,CAAC;CAAC,CACf,GACD,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GACxB,CAAC,GAGH,CAAC,SAAS,KAAK,CACX,uBAAuB,CACrB,MAAM,YAAY,EAClB,MAAM,MAAM,EACZ,MAAM,KAAK,EACX,MAAM,SAAS,CAChB,CACF,GACD,KAAK,CAAC,uBAAuB,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,GAEtE,KAAK,CAAC,uBAAuB,CAAC,CAAA;AAElC;;GAEG;AACH,oBAAY,sBAAsB,CAChC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,EACpB,OAAO,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAC/B,MAAM,SAAS,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,IACvC,MAAM,CAAC,QAAQ,CAAC,SAAS,aAAa,GACtC,KAAK,CAAC,sBAAsB,CAAC,GAC7B,CAAC,SAAS,EAAE,GACZ,EAAE,GACF,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,GACtB,CAAC,GAAG,OAAO,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC,GACtC,CAAC,SAAS,CAAC,MAAM,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,GACrC,sBAAsB,CACpB;IAAC,GAAG,IAAI;CAAC,EACT;IAAC,GAAG,OAAO;IAAE,kBAAkB,CAAC,IAAI,CAAC;CAAC,EACtC;IAAC,GAAG,MAAM;IAAE,CAAC;CAAC,CACf,GACD,CAAC,SAAS,KAAK,CACb,uBAAuB,CACrB,MAAM,YAAY,EAClB,MAAM,MAAM,EACZ,MAAM,KAAK,EACX,GAAG,CACJ,CACF,GAED,KAAK,CACH,sBAAsB,CACpB,OAAO,SAAS,KAAK,GAAG,YAAY,GAAG,KAAK,EAC5C,MAAM,CACP,CACF,GAED,KAAK,CAAC,sBAAsB,CAAC,CAAA;AAEjC,wBAAgB,kBAAkB,CAAC,CAAC,SAAS,GAAG,EAAE,EAAE,EAClD,OAAO,EACP,OAAO,GACR,EAAE;IACD,OAAO,EAAE,SAAS,CAAC,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAA;IAChD,OAAO,CAAC,EAAE,eAAe,CAAC,SAAS,CAAC,CAAA;CACrC,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAY5B"}