{"version": 3, "file": "useQuery.d.ts", "sourceRoot": "", "sources": ["../../src/useQuery.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EACV,mBAAmB,EACnB,iBAAiB,EACjB,SAAS,EACT,aAAa,EACb,QAAQ,EACT,MAAM,sBAAsB,CAAA;AAC7B,OAAO,KAAK,EACV,qBAAqB,EACrB,eAAe,EACf,cAAc,EACf,MAAM,SAAS,CAAA;AAGhB,wBAAgB,QAAQ,CACtB,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,OAAO,EAAE,SAAS,CAChB,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACvD,aAAa,CACd,GAAG;IACF,WAAW,EACP,iBAAiB,CAAC,YAAY,CAAC,GAC/B,CAAC,MAAM,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAA;CAC5C,GACA,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AACvC,wBAAgB,QAAQ,CACtB,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,OAAO,EAAE,SAAS,CAChB,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACvD,aAAa,CACd,GAAG;IACF,WAAW,CAAC,EACR,SAAS,GACT,mBAAmB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,GACpD,iBAAiB,CAAC,YAAY,CAAC,CAAA;CACpC,GACA,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AAChC,wBAAgB,QAAQ,CACtB,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,OAAO,EAAE,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,GAC/D,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AAEhC,kBAAkB;AAClB,wBAAgB,QAAQ,CACtB,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,QAAQ,EAAE,SAAS,EACnB,OAAO,CAAC,EAAE,SAAS,CACjB,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACvD,UAAU,GAAG,aAAa,CAC3B,GACA,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AAChC,kBAAkB;AAClB,wBAAgB,QAAQ,CACtB,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,QAAQ,EAAE,SAAS,EACnB,OAAO,CAAC,EAAE,SAAS,CACjB,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACvD,UAAU,GAAG,aAAa,CAC3B,GAAG;IAAE,WAAW,EAAE,YAAY,GAAG,CAAC,MAAM,YAAY,CAAC,CAAA;CAAE,GACvD,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AACvC,kBAAkB;AAClB,wBAAgB,QAAQ,CACtB,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,QAAQ,EAAE,SAAS,EACnB,OAAO,CAAC,EAAE,SAAS,CACjB,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACvD,UAAU,CACX,GACA,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AAChC,kBAAkB;AAClB,wBAAgB,QAAQ,CACtB,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,QAAQ,EAAE,SAAS,EACnB,OAAO,EAAE,aAAa,CAAC,YAAY,EAAE,SAAS,CAAC,EAC/C,OAAO,CAAC,EAAE,SAAS,CACjB,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACvD,UAAU,GAAG,SAAS,GAAG,aAAa,CACvC,GAAG;IAAE,WAAW,CAAC,EAAE,MAAM,SAAS,CAAA;CAAE,GACpC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AAChC,kBAAkB;AAClB,wBAAgB,QAAQ,CACtB,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,QAAQ,EAAE,SAAS,EACnB,OAAO,EAAE,aAAa,CAAC,YAAY,EAAE,SAAS,CAAC,EAC/C,OAAO,CAAC,EAAE,SAAS,CACjB,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACvD,UAAU,GAAG,SAAS,GAAG,aAAa,CACvC,GAAG;IAAE,WAAW,EAAE,YAAY,GAAG,CAAC,MAAM,YAAY,CAAC,CAAA;CAAE,GACvD,qBAAqB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;AACvC,kBAAkB;AAClB,wBAAgB,QAAQ,CACtB,YAAY,GAAG,OAAO,EACtB,MAAM,GAAG,OAAO,EAChB,KAAK,GAAG,YAAY,EACpB,SAAS,SAAS,QAAQ,GAAG,QAAQ,EAErC,QAAQ,EAAE,SAAS,EACnB,OAAO,EAAE,aAAa,CAAC,YAAY,EAAE,SAAS,CAAC,EAC/C,OAAO,CAAC,EAAE,SAAS,CACjB,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EACvD,UAAU,GAAG,SAAS,CACvB,GACA,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA"}