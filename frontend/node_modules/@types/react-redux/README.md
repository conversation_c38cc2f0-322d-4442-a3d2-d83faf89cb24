# Installation
> `npm install --save @types/react-redux`

# Summary
This package contains type definitions for react-redux (https://github.com/reduxjs/react-redux).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-redux.

### Additional Details
 * Last updated: Mon, 23 Sep 2024 18:39:57 GMT
 * Dependencies: [@types/hoist-non-react-statics](https://npmjs.com/package/@types/hoist-non-react-statics), [@types/react](https://npmjs.com/package/@types/react), [hoist-non-react-statics](https://npmjs.com/package/hoist-non-react-statics), [redux](https://npmjs.com/package/redux)

# Credits
These definitions were written by [<PERSON>ub<PERSON>](https://github.com/tkqubo), [<PERSON><PERSON><PERSON>](https://github.com/clayne11), [<PERSON>](https://github.com/tansongyang), [<PERSON>](https://github.com/nicholasboll), [<PERSON><PERSON><PERSON>](https://github.com/mdibyo), [<PERSON><PERSON>](https://github.com/val1984), [Johann Rakotoharisoa](https://github.com/jrakotoharisoa), [Anatoli Papirovski](https://github.com/apapirovski), [Boris Sergeyev](https://github.com/surgeboris), [Søren Bruus Frank](https://github.com/soerenbf), [Jonathan Ziller](https://github.com/mrwolfz), [Dylan Vann](https://github.com/dylanvann), [Yuki Ito](https://github.com/Lazyuki), [Kazuma Ebina](https://github.com/kazuma1989), [Michael Lebedev](https://github.com/megazazik), [Lenz Weber](https://github.com/phryneas), and [Mark Erikson](https://github.com/markerikson).
