{"name": "@types/react-redux", "version": "7.1.34", "description": "TypeScript definitions for react-redux", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-redux", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "tkqubo", "url": "https://github.com/tkqubo"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "clayne11", "url": "https://github.com/clayne11"}, {"name": "<PERSON>", "githubUsername": "tan<PERSON><PERSON><PERSON>", "url": "https://github.com/tansongyang"}, {"name": "<PERSON>", "githubUsername": "nicholasboll", "url": "https://github.com/nicholasboll"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "mdibyo", "url": "https://github.com/mdibyo"}, {"name": "<PERSON><PERSON>", "githubUsername": "val1984", "url": "https://github.com/val1984"}, {"name": "<PERSON>", "githubUsername": "jrakotoharisoa", "url": "https://github.com/jrakotoharisoa"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/apa<PERSON><PERSON>ski"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON>is", "url": "https://github.com/surgeboris"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "soerenbf", "url": "https://github.com/soerenbf"}, {"name": "<PERSON>", "githubUsername": "m<PERSON><PERSON><PERSON>", "url": "https://github.com/mrwolfz"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/dylanvann"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Lazyuki"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "kazuma1989", "url": "https://github.com/kazuma1989"}, {"name": "<PERSON>", "githubUsername": "megazazik", "url": "https://github.com/megazazik"}, {"name": "<PERSON><PERSON>", "githubUsername": "phr<PERSON><PERSON>", "url": "https://github.com/phryneas"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/markerikson"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-redux"}, "scripts": {}, "dependencies": {"@types/hoist-non-react-statics": "^3.3.0", "@types/react": "*", "hoist-non-react-statics": "^3.3.0", "redux": "^4.0.0"}, "typesPublisherContentHash": "0bca859870c928967a511a0732c00eb6423bfdfae1fad656ebe55b93d83599ce", "typeScriptVersion": "4.8"}