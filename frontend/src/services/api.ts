import axios, { AxiosResponse } from 'axios'
import { message } from 'antd'
import { useAuthStore } from '../stores/authStore'
import type {
  ApiResponse,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  User,
  UserUpdateRequest,
  PasswordChangeRequest,
  Task,
  TaskCreateRequest,
  TaskUpdateRequest,
  TaskListResponse,
  TaskStatusUpdateRequest,
  KanbanData,
  QuadrantData,
  TaskStatistics,
  TaskLog
} from '../types'

// 创建 axios 实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证 token
api.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse<any>>) => {
    return response
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response
      
      // 401 未授权 - 清除认证信息并跳转登录
      if (status === 401) {
        useAuthStore.getState().clearAuth()
        window.location.href = '/login'
        message.error('登录已过期，请重新登录')
        return Promise.reject(error)
      }
      
      // 显示错误消息
      const errorMessage = data?.message || '请求失败'
      message.error(errorMessage)
    } else if (error.request) {
      message.error('网络连接失败，请检查网络')
    } else {
      message.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 认证相关 API
export const authApi = {
  // 用户登录
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    const response = await api.post<ApiResponse<LoginResponse>>('/auth/login', data)
    return response.data.data!
  },

  // 用户注册
  register: async (data: RegisterRequest): Promise<User> => {
    const response = await api.post<ApiResponse<User>>('/auth/register', data)
    return response.data.data!
  },
}

// 用户相关 API
export const userApi = {
  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    const response = await api.get<ApiResponse<User>>('/users/me')
    return response.data.data!
  },

  // 更新用户信息
  updateProfile: async (data: UserUpdateRequest): Promise<User> => {
    const response = await api.put<ApiResponse<User>>('/users/me', data)
    return response.data.data!
  },

  // 修改密码
  changePassword: async (data: PasswordChangeRequest): Promise<void> => {
    await api.post<ApiResponse<void>>('/users/change-password', data)
  },

  // 获取用户列表（管理员）
  getUserList: async (page: number = 1, pageSize: number = 20) => {
    const response = await api.get<ApiResponse<{
      users: User[]
      total: number
      page: number
      pageSize: number
    }>>('/users/list', {
      params: { page, pageSize }
    })
    return response.data.data!
  },

  // 重置用户密码（管理员）
  resetPassword: async (userId: number): Promise<void> => {
    await api.post<ApiResponse<void>>('/users/reset-password', { userId })
  },
}

// 任务相关 API
export const taskApi = {
  // 创建任务
  createTask: async (data: TaskCreateRequest): Promise<Task> => {
    const response = await api.post<ApiResponse<Task>>('/tasks', data)
    return response.data.data!
  },

  // 获取任务列表
  getTaskList: async (params?: any): Promise<TaskListResponse> => {
    const response = await api.get<ApiResponse<TaskListResponse>>('/tasks', { params })
    return response.data.data!
  },

  // 获取单个任务
  getTask: async (id: number): Promise<Task> => {
    const response = await api.get<ApiResponse<Task>>(`/tasks/${id}`)
    return response.data.data!
  },

  // 更新任务
  updateTask: async (id: number, data: TaskUpdateRequest): Promise<Task> => {
    const response = await api.put<ApiResponse<Task>>(`/tasks/${id}`, data)
    return response.data.data!
  },

  // 更新任务状态
  updateTaskStatus: async (id: number, data: TaskStatusUpdateRequest): Promise<Task> => {
    const response = await api.patch<ApiResponse<Task>>(`/tasks/${id}/status`, data)
    return response.data.data!
  },

  // 删除任务
  deleteTask: async (id: number): Promise<void> => {
    await api.delete(`/tasks/${id}`)
  },

  // 获取任务日志
  getTaskLogs: async (id: number): Promise<TaskLog[]> => {
    const response = await api.get<ApiResponse<TaskLog[]>>(`/tasks/${id}/logs`)
    return response.data.data!
  },

  // 获取看板数据
  getKanbanData: async (): Promise<KanbanData> => {
    const response = await api.get<ApiResponse<KanbanData>>('/tasks/kanban')
    return response.data.data!
  },

  // 获取四象限数据
  getQuadrantData: async (): Promise<QuadrantData> => {
    const response = await api.get<ApiResponse<QuadrantData>>('/tasks/quadrant')
    return response.data.data!
  },

  // 获取任务统计
  getTaskStatistics: async (): Promise<TaskStatistics> => {
    const response = await api.get<ApiResponse<TaskStatistics>>('/tasks/statistics')
    return response.data.data!
  },
}

export default api
